package com.cdkit.modules.cm.domain.gateway.plm;

import com.cdkit.modules.cm.domain.gateway.plm.entity.ProductFileTreeEntity;
import com.cdkit.modules.cm.domain.gateway.plm.entity.ProductRecipeInfo;

import java.util.List;

/**
 * 产品档案外部接口对接
 * <AUTHOR>
 * @date 2025/07/18
 */
public interface ProductFileGateway {

    /**
     * 获取产品档案树结构
     * 调用PLM的IPlmBaseApi.getProductFile方法
     *
     * @param productCode 产品编码
     * @return 产品档案树结构
     */
    List<ProductFileTreeEntity> getProductFileTree(String productCode);

    /**
     * 从产品档案树中提取最后节点的物料编码列表
     *
     * @param productCode 产品编码
     * @return 最后节点的物料编码列表
     */
    List<String> getLastNodeMaterialCodes(String productCode);

    /**
     * 获取产品配方信息（第一层作为配方信息，最底层作为物料列表）
     *
     * @param productCode 产品编码
     * @return 产品配方信息
     */
    ProductRecipeInfo getProductRecipeInfo(String productCode);
}
