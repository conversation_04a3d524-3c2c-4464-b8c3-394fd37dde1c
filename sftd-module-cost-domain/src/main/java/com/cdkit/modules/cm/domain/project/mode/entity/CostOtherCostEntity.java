package com.cdkit.modules.cm.domain.project.mode.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.cdkitframework.poi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 其他成本明细
 * @Author: cdkit-boot
 * @Date:   2025-07-18
 * @Version: V1.0
 */
@Schema(description="cost_other_cost对象")
@Data
public class CostOtherCostEntity implements Serializable {
    private static final long serialVersionUID = 1L;

	/**UUID主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "UUID主键")
    private String id;
	/**关联计划ID*/
    @Schema(description = "关联计划ID")
    private String planId;
	/**成本类目*/
	@Excel(name = "成本类目", width = 15)
    @Schema(description = "成本类目")
    private String costCategory;
	/**成本说明*/
	@Excel(name = "成本说明", width = 15)
    @Schema(description = "成本说明")
    private String costDescription;
	/**费用金额(万元)*/
	@Excel(name = "费用金额(万元)", width = 15)
    @Schema(description = "费用金额(万元)")
    private java.math.BigDecimal feeAmount;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**租户ID*/
	@Excel(name = "租户ID", width = 15)
    @Schema(description = "租户ID")
    private Integer tenantId;
	/**删除标识 0:未删除 1:删除*/
	@Excel(name = "删除标识 0:未删除 1:删除", width = 15)
    @Schema(description = "删除标识 0:未删除 1:删除")
    @TableLogic
    private Integer delFlag;
	/**所属部门代码*/
    @Schema(description = "所属部门代码")
    private String sysOrgCode;
}
