package com.cdkit.modules.cm.domain.outsourcing.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 外委单据领域实体
 * 用于存储外委单据信息，支持已转量查询
 * 
 * <AUTHOR>
 * @date 2025-08-13
 */
@Data
@Accessors(chain = true)
public class OutsourcingOrderEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键ID*/
    private String id;

    /**外委单据编号*/
    private String orderCode;

    /**上游单据号（项目计划号）*/
    private String upstreamOrderCode;

    /**项目计划ID*/
    private String planId;

    /**项目计划编码（来源单据号）*/
    private String planCode;

    /**产品名称*/
    private String productName;

    /**物料编码*/
    private String materialCode;

    /**物料名称*/
    private String materialName;

    /**物料类型（PRODUCT-产品，SEMI-半成品）*/
    private String itemType;

    /**外委产品名称*/
    private String outsourcingProduct;

    /**外委量（吨）*/
    private BigDecimal outsourcingAmount;

    /**配方名称*/
    private String formulaName;

    /**配方编码*/
    private String formulaCode;

    /**审批状态*/
    private String approvalStatus;

    /**是否审批通过*/
    private Boolean approved;

    /**创建时间*/
    private Date createTime;

    /**创建人*/
    private String createBy;

    /**更新时间*/
    private Date updateTime;

    /**更新人*/
    private String updateBy;

    /**租户ID*/
    private Integer tenantId;

    /**删除标识*/
    private Integer delFlag;

    /**所属部门代码*/
    private String sysOrgCode;

    /**
     * 检查是否已审批通过
     */
    public boolean isApproved() {
        return Boolean.TRUE.equals(approved);
    }

    /**
     * 检查是否为产品
     */
    public boolean isProduct() {
        return "PRODUCT".equals(itemType);
    }

    /**
     * 检查是否为半成品
     */
    public boolean isSemiProduct() {
        return "SEMI".equals(itemType);
    }
}
