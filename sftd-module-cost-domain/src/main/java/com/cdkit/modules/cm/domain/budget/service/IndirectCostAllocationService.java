package com.cdkit.modules.cm.domain.budget.service;

import com.cdkit.modules.cm.domain.budget.mode.entity.CostAnnualBudgetEntity;
import com.cdkit.modules.cm.domain.budget.model.dto.CenterCostImportDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 间接成本分摊服务
 * 负责将导入的中心间接成本按项目收入预算比例进行分摊
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class IndirectCostAllocationService {

    /**
     * 分摊间接成本
     * 
     * @param importDataList 导入的中心间接成本数据
     * @param budgetDetailList 年度预算明细列表（包含项目收入预算信息）
     * @return 分摊结果
     */
    public AllocationResult allocateIndirectCost(List<CenterCostImportDTO> importDataList, 
                                               List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList) {
        if (importDataList == null || importDataList.isEmpty()) {
            log.warn("导入数据为空，无需分摊");
            return new AllocationResult();
        }
        
        if (budgetDetailList == null || budgetDetailList.isEmpty()) {
            log.warn("预算明细数据为空，无法进行分摊");
            return new AllocationResult();
        }

        log.info("开始间接成本分摊计算，导入数据数量: {}, 预算明细数量: {}", 
                importDataList.size(), budgetDetailList.size());

        AllocationResult result = new AllocationResult();
        
        // 按模版类型分组处理
        Map<String, List<CenterCostImportDTO>> groupedByTemplate = importDataList.stream()
                .collect(Collectors.groupingBy(CenterCostImportDTO::getTemplateType));

        for (Map.Entry<String, List<CenterCostImportDTO>> entry : groupedByTemplate.entrySet()) {
            String templateType = entry.getKey();
            List<CenterCostImportDTO> templateData = entry.getValue();
            
            log.info("处理模版类型: {}, 数据数量: {}", templateType, templateData.size());
            
            // 按中心分组处理
            Map<String, List<CenterCostImportDTO>> groupedByCenter = templateData.stream()
                    .collect(Collectors.groupingBy(CenterCostImportDTO::getCenter));

            for (Map.Entry<String, List<CenterCostImportDTO>> centerEntry : groupedByCenter.entrySet()) {
                String centerName = centerEntry.getKey();
                List<CenterCostImportDTO> centerData = centerEntry.getValue();
                
                // 获取该中心下的所有项目
                List<CostAnnualBudgetEntity.BudgetDetailInfo> centerProjects = budgetDetailList.stream()
                        .filter(detail -> centerName.equals(detail.getCenter()))
                        .collect(Collectors.toList());

                if (centerProjects.isEmpty()) {
                    log.warn("中心 {} 下没有找到项目，跳过分摊", centerName);
                    continue;
                }

                // 计算该中心的分摊结果
                List<AllocationItem> centerAllocationItems = allocateByCenterProjects(centerData, centerProjects);
                
                // 根据模版类型添加到对应的结果列表
                switch (templateType) {
                    case "this_center_indirect_cost_template":
                        result.getCenterIndirectCostList().addAll(centerAllocationItems);
                        break;
                    case "non_operational_center_indirect_cost_template":
                        result.getNonOperatingIndirectCostList().addAll(centerAllocationItems);
                        break;
                    case "general_admin_indirect_cost_template":
                        result.getComprehensiveIndirectCostList().addAll(centerAllocationItems);
                        break;
                    default:
                        log.warn("未知的模版类型: {}", templateType);
                        break;
                }
            }
        }

        log.info("间接成本分摊计算完成，本中心间接成本: {}, 非经营中心间接成本: {}, 综合管理间接成本: {}", 
                result.getCenterIndirectCostList().size(),
                result.getNonOperatingIndirectCostList().size(),
                result.getComprehensiveIndirectCostList().size());

        return result;
    }

    /**
     * 按中心项目进行分摊计算
     * 
     * @param centerData 中心的间接成本数据
     * @param centerProjects 中心下的项目列表
     * @return 分摊结果列表
     */
    private List<AllocationItem> allocateByCenterProjects(List<CenterCostImportDTO> centerData, 
                                                        List<CostAnnualBudgetEntity.BudgetDetailInfo> centerProjects) {
        List<AllocationItem> allocationItems = new ArrayList<>();
        
        // 计算中心收入预算总额
        BigDecimal centerTotalRevenue = centerProjects.stream()
                .map(CostAnnualBudgetEntity.BudgetDetailInfo::getRevenueBudget)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (centerTotalRevenue.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("中心收入预算总额为0或负数，无法进行分摊，中心: {}", 
                    centerProjects.get(0).getCenter());
            return allocationItems;
        }

        log.info("中心 {} 收入预算总额: {}", centerProjects.get(0).getCenter(), centerTotalRevenue);

        // 按预算科目分摊
        for (CenterCostImportDTO costData : centerData) {
            BigDecimal totalCostAmount = costData.getCostAmount();
            if (totalCostAmount == null || totalCostAmount.compareTo(BigDecimal.ZERO) <= 0) {
                log.warn("成本金额为0或负数，跳过分摊，科目: {}", costData.getSubjectName());
                continue;
            }

            // 为每个项目计算分摊金额
            BigDecimal allocatedTotal = BigDecimal.ZERO;
            for (int i = 0; i < centerProjects.size(); i++) {
                CostAnnualBudgetEntity.BudgetDetailInfo project = centerProjects.get(i);
                BigDecimal projectRevenue = project.getRevenueBudget();
                if (projectRevenue == null) {
                    projectRevenue = BigDecimal.ZERO;
                }

                BigDecimal allocatedAmount;
                if (i == centerProjects.size() - 1) {
                    // 最后一个项目做补差
                    allocatedAmount = totalCostAmount.subtract(allocatedTotal);
                } else {
                    // 计算分摊比例
                    BigDecimal ratio = projectRevenue.divide(centerTotalRevenue, 6, RoundingMode.HALF_UP);
                    allocatedAmount = totalCostAmount.multiply(ratio).setScale(2, RoundingMode.HALF_UP);
                    allocatedTotal = allocatedTotal.add(allocatedAmount);
                }

                // 创建分摊项
                AllocationItem item = new AllocationItem();
                item.setBudgetDetailId(project.getId());
                item.setProjectCode(project.getProjectCode());
                item.setProjectName(project.getProjectName());
                item.setCenter(project.getCenter());
                item.setSubjectCode(costData.getSubjectCode());
                item.setSubjectName(costData.getSubjectName());
                item.setSubjectDescription(costData.getSubjectDescription());
                item.setBudgetAmount(allocatedAmount);
                item.setProjectRevenue(projectRevenue);
                item.setOriginalCostAmount(totalCostAmount);

                allocationItems.add(item);

                log.debug("项目分摊计算: 项目={}, 科目={}, 项目收入={}, 分摊金额={}", 
                        project.getProjectName(), costData.getSubjectName(), projectRevenue, allocatedAmount);
            }
        }

        return allocationItems;
    }

    /**
     * 分摊结果
     */
    public static class AllocationResult {
        private List<AllocationItem> centerIndirectCostList = new ArrayList<>();
        private List<AllocationItem> nonOperatingIndirectCostList = new ArrayList<>();
        private List<AllocationItem> comprehensiveIndirectCostList = new ArrayList<>();

        public List<AllocationItem> getCenterIndirectCostList() {
            return centerIndirectCostList;
        }

        public void setCenterIndirectCostList(List<AllocationItem> centerIndirectCostList) {
            this.centerIndirectCostList = centerIndirectCostList;
        }

        public List<AllocationItem> getNonOperatingIndirectCostList() {
            return nonOperatingIndirectCostList;
        }

        public void setNonOperatingIndirectCostList(List<AllocationItem> nonOperatingIndirectCostList) {
            this.nonOperatingIndirectCostList = nonOperatingIndirectCostList;
        }

        public List<AllocationItem> getComprehensiveIndirectCostList() {
            return comprehensiveIndirectCostList;
        }

        public void setComprehensiveIndirectCostList(List<AllocationItem> comprehensiveIndirectCostList) {
            this.comprehensiveIndirectCostList = comprehensiveIndirectCostList;
        }
    }

    /**
     * 分摊项
     */
    public static class AllocationItem {
        private String budgetDetailId;
        private String projectCode;
        private String projectName;
        private String center;
        private String subjectCode;
        private String subjectName;
        private String subjectDescription;
        private BigDecimal budgetAmount;
        private BigDecimal projectRevenue;
        private BigDecimal originalCostAmount;

        // Getters and Setters
        public String getBudgetDetailId() {
            return budgetDetailId;
        }

        public void setBudgetDetailId(String budgetDetailId) {
            this.budgetDetailId = budgetDetailId;
        }

        public String getProjectCode() {
            return projectCode;
        }

        public void setProjectCode(String projectCode) {
            this.projectCode = projectCode;
        }

        public String getProjectName() {
            return projectName;
        }

        public void setProjectName(String projectName) {
            this.projectName = projectName;
        }

        public String getCenter() {
            return center;
        }

        public void setCenter(String center) {
            this.center = center;
        }

        public String getSubjectCode() {
            return subjectCode;
        }

        public void setSubjectCode(String subjectCode) {
            this.subjectCode = subjectCode;
        }

        public String getSubjectName() {
            return subjectName;
        }

        public void setSubjectName(String subjectName) {
            this.subjectName = subjectName;
        }

        public String getSubjectDescription() {
            return subjectDescription;
        }

        public void setSubjectDescription(String subjectDescription) {
            this.subjectDescription = subjectDescription;
        }

        public BigDecimal getBudgetAmount() {
            return budgetAmount;
        }

        public void setBudgetAmount(BigDecimal budgetAmount) {
            this.budgetAmount = budgetAmount;
        }

        public BigDecimal getProjectRevenue() {
            return projectRevenue;
        }

        public void setProjectRevenue(BigDecimal projectRevenue) {
            this.projectRevenue = projectRevenue;
        }

        public BigDecimal getOriginalCostAmount() {
            return originalCostAmount;
        }

        public void setOriginalCostAmount(BigDecimal originalCostAmount) {
            this.originalCostAmount = originalCostAmount;
        }
    }
}
