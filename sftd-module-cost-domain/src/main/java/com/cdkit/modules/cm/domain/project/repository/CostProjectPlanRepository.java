package com.cdkit.modules.cm.domain.project.repository;

import com.cdkit.modules.cm.domain.IBaseDomainRepository;
import com.cdkit.modules.cm.domain.project.mode.entity.CostProjectPlanEntity;
import com.cdkit.modules.cm.domain.project.mode.entity.CostProjectPlanDetailEntity;
import com.cdkit.modules.cm.domain.project.mode.entity.CostDirectCostEntity;
import com.cdkit.modules.cm.domain.project.mode.entity.CostOtherCostEntity;
import com.cdkit.modules.cm.domain.project.mode.entity.CostTaxCostEntity;
import com.cdkit.modules.cm.domain.project.mode.entity.CostMaterialDetailEntity;
import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;

import java.util.List;

/**
 * 项目计划仓储层
 * <AUTHOR>
 * @date 2025/07/18
 */
public interface CostProjectPlanRepository extends IBaseDomainRepository<CostProjectPlanEntity, CostProjectPlanEntity> {

    /**
     * 保存主表和子表数据
     *
     * @param costProjectPlan 项目计划主表数据
     * @param detailList 项目计划明细列表
     * @param directCostList 直接成本列表
     * @param otherCostList 其他成本列表
     * @param taxCostList 税金及附加列表
     * @param materialDetailList 原料明细列表
     * @return 保存结果
     */
    boolean saveMain(CostProjectPlanEntity costProjectPlan,
                     List<CostProjectPlanDetailEntity> detailList,
                     List<CostDirectCostEntity> directCostList,
                     List<CostOtherCostEntity> otherCostList,
                     List<CostTaxCostEntity> taxCostList,
                     List<CostMaterialDetailEntity> materialDetailList);

    /**
     * 更新主表和子表数据
     *
     * @param costProjectPlan 项目计划主表数据
     * @param detailList 项目计划明细列表
     * @param directCostList 直接成本列表
     * @param otherCostList 其他成本列表
     * @param taxCostList 税金及附加列表
     * @param materialDetailList 原料明细列表
     * @return 更新结果
     */
    boolean updateMain(CostProjectPlanEntity costProjectPlan,
                       List<CostProjectPlanDetailEntity> detailList,
                       List<CostDirectCostEntity> directCostList,
                       List<CostOtherCostEntity> otherCostList,
                       List<CostTaxCostEntity> taxCostList,
                       List<CostMaterialDetailEntity> materialDetailList);

    /**
     * 删除主表和子表数据
     *
     * @param id 主表ID
     * @return 删除结果
     */
    boolean deleteMain(String id);

    /**
     * 批量删除主表和子表数据
     *
     * @param ids 主表ID列表
     * @return 删除结果
     */
    boolean deleteBatchMain(List<String> ids);

    /**
     * 根据主表ID查询项目计划明细数据
     *
     * @param planId 项目计划ID
     * @return 项目计划明细列表
     */
    List<CostProjectPlanDetailEntity> queryDetailByPlanId(String planId);

    /**
     * 根据主表ID查询直接成本数据
     *
     * @param planId 项目计划ID
     * @return 直接成本列表
     */
    List<CostDirectCostEntity> queryDirectCostByPlanId(String planId);

    /**
     * 根据主表ID查询其他成本数据
     *
     * @param planId 项目计划ID
     * @return 其他成本列表
     */
    List<CostOtherCostEntity> queryOtherCostByPlanId(String planId);

    /**
     * 根据主表ID查询税金及附加数据
     *
     * @param planId 项目计划ID
     * @return 税金及附加列表
     */
    List<CostTaxCostEntity> queryTaxCostByPlanId(String planId);

    /**
     * 根据主表ID查询原料明细数据
     *
     * @param planId 项目计划ID
     * @return 原料明细列表
     */
    List<CostMaterialDetailEntity> queryMaterialDetailByPlanId(String planId);

    /**
     * 根据ID查询项目计划详情（包含所有子表数据）
     *
     * @param id 项目计划ID
     * @return 项目计划详情
     */
    CostProjectPlanEntity queryByIdWithDetails(String id);

    /**
     * 计算项目计划预算依据
     *
     * @param planId 项目计划ID
     * @return 计算后的项目计划实体
     */
    CostProjectPlanEntity calculateBudgetBasis(String planId);

    /**
     * 查询指定状态的项目计划列表
     *
     * @param status 项目计划状态
     * @return 项目计划列表
     */
    List<CostProjectPlanEntity> queryByStatus(String status);

    /**
     * 根据项目编号查询项目计划列表
     *
     * @param projectCode 项目编号
     * @return 项目计划列表
     */
    List<CostProjectPlanEntity> queryByProjectCode(String projectCode);

    /**
     * 根据父计划ID查询子计划列表
     *
     * @param parentPlanId 父计划ID
     * @return 子计划列表
     */
    List<CostProjectPlanEntity> queryByParentPlanId(String parentPlanId);

    /**
     * 按计划类型分页查询项目计划列表
     *
     * @param queryEntity 查询条件（必须包含planType）
     * @param pageReq 分页条件
     * @return 分页查询结果
     */
    PageRes<CostProjectPlanEntity> pageByPlanType(CostProjectPlanEntity queryEntity, PageReq pageReq);
}
