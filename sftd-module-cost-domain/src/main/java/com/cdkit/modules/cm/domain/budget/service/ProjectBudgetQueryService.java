package com.cdkit.modules.cm.domain.budget.service;

import com.cdkit.modules.cm.domain.budget.repository.CostAnnualBudgetDetailRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 项目预算查询领域服务
 * 负责项目年度预算信息的查询业务逻辑
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ProjectBudgetQueryService {

    private final CostAnnualBudgetDetailRepository costAnnualBudgetDetailRepository;

    /**
     * 根据项目编号查询项目年度预算信息
     * 
     * @param projectCode 项目编号
     * @return 项目年度预算信息列表
     */
    public List<CostAnnualBudgetDetailRepository.ProjectBudgetInfo> queryByProjectCode(String projectCode) {
        if (!StringUtils.hasText(projectCode)) {
            throw new IllegalArgumentException("项目编号不能为空");
        }

        log.info("开始查询项目年度预算信息，项目编号: {}", projectCode);

        // 调用仓储层查询数据
        List<CostAnnualBudgetDetailRepository.ProjectBudgetInfo> budgetInfoList = 
            costAnnualBudgetDetailRepository.findByProjectCode(projectCode);

        if (budgetInfoList == null || budgetInfoList.isEmpty()) {
            log.info("未找到项目年度预算信息，项目编号: {}", projectCode);
            return budgetInfoList;
        }

        log.info("查询项目年度预算信息成功，项目编号: {}, 查询到{}条记录", projectCode, budgetInfoList.size());
        return budgetInfoList;
    }
}
