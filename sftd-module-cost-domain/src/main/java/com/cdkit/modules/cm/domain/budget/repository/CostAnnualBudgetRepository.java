package com.cdkit.modules.cm.domain.budget.repository;

import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.cdkit.modules.cm.domain.IBaseDomainRepository;
import com.cdkit.modules.cm.domain.budget.mode.entity.CostAnnualBudgetEntity;

import java.util.List;

/**
 * 年度总预算仓储接口
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface CostAnnualBudgetRepository extends IBaseDomainRepository<CostAnnualBudgetEntity, CostAnnualBudgetEntity> {

    /**
     * 保存年度总预算
     *
     * @param entity 年度总预算实体
     * @return 保存结果
     */
    CostAnnualBudgetEntity save(CostAnnualBudgetEntity entity);

    /**
     * 根据ID更新年度总预算
     *
     * @param entity 年度总预算实体
     * @return 更新结果
     */
    CostAnnualBudgetEntity updateById(CostAnnualBudgetEntity entity);

    /**
     * 根据ID删除年度总预算
     *
     * @param id 年度总预算ID
     */
    void deleteById(String id);

    /**
     * 批量删除年度总预算
     *
     * @param ids 年度总预算ID列表
     */
    void deleteByIds(List<String> ids);

    /**
     * 根据ID查询年度总预算
     *
     * @param id 年度总预算ID
     * @return 年度总预算实体
     */
    CostAnnualBudgetEntity findById(String id);

    /**
     * 根据ID列表查询年度总预算
     *
     * @param ids 年度总预算ID列表
     * @return 年度总预算实体列表
     */
    List<CostAnnualBudgetEntity> findByIds(List<String> ids);

    /**
     * 分页查询年度总预算
     *
     * @param queryEntity 查询条件
     * @param pageReq 分页参数
     * @return 分页结果
     */
    PageRes<CostAnnualBudgetEntity> queryPageList(CostAnnualBudgetEntity queryEntity, PageReq pageReq);

    /**
     * 根据预算编号查询年度总预算
     *
     * @param budgetCode 预算编号
     * @return 年度总预算实体
     */
    CostAnnualBudgetEntity findByBudgetCode(String budgetCode);

    /**
     * 根据年份查询年度总预算列表
     *
     * @param budgetYear 年份
     * @return 年度总预算实体列表
     */
    List<CostAnnualBudgetEntity> findByBudgetYear(String budgetYear);

    /**
     * 根据状态查询年度总预算列表
     *
     * @param budgetStatus 预算状态
     * @return 年度总预算实体列表
     */
    List<CostAnnualBudgetEntity> findByBudgetStatus(String budgetStatus);

    /**
     * 根据年份查询最大的预算编号
     *
     * @param year 年份（4位数字）
     * @return 最大的预算编号，如果没有记录则返回null
     */
    String findMaxBudgetCodeByYear(String year);

    /**
     * 更新总预算的汇总统计值
     *
     * @param budgetId 总预算ID
     * @param revenueTotalAmount 收入（不含税）总金额（元）
     * @param directCostTotalAmount 直接成本（不含税）总金额（元）
     * @param profitTotalAmount 利润总额
     * @param profitRate 利润率
     */
    void updateBudgetSummaryStatistics(String budgetId,
                                     java.math.BigDecimal revenueTotalAmount,
                                     java.math.BigDecimal directCostTotalAmount,
                                     java.math.BigDecimal profitTotalAmount,
                                     java.math.BigDecimal profitRate);

    /**
     * 根据年份和所属单位查询年度总预算列表
     *
     * @param budgetYear 年份
     * @param professionalCompany 所属单位
     * @return 年度总预算实体列表
     */
    List<CostAnnualBudgetEntity> findByBudgetYearAndProfessionalCompany(String budgetYear, String professionalCompany);

    /**
     * 根据年份和所属单位查询已锁定状态的年度总预算列表
     *
     * @param budgetYear 年份
     * @param professionalCompany 所属单位
     * @return 已锁定状态的年度总预算实体列表
     */
    List<CostAnnualBudgetEntity> findLockedBudgetsByYearAndCompany(String budgetYear, String professionalCompany);

    /**
     * 批量更新预算状态
     *
     * @param budgetIds 预算ID列表
     * @param newStatus 新状态
     */
    void updateBudgetStatusBatch(List<String> budgetIds, String newStatus);
}
