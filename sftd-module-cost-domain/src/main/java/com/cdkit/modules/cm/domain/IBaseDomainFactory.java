package com.cdkit.modules.cm.domain;

import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 * @Date 2025-04-30 08:09
 * @Description domain 工厂
 * @since 1.0
 */
@Configuration
public interface IBaseDomainFactory<T, D> {

    /**
     * 创建对象
     * @param objectDTO 对象 DTO 实体
     * @return 对象
     */
    default T create(D objectDTO) {
        // 参数校验
        this.validate(objectDTO);

        return null;
    }

    /**
     * domain 必要参数校验
     */
    default void validate(@Validated D objectDTO) {}
}
