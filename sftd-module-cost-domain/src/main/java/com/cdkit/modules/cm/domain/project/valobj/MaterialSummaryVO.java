package com.cdkit.modules.cm.domain.project.valobj;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 原料明细汇总值对象
 * 用于汇总显示所有物料的统计信息
 * 
 * <AUTHOR>
 * @date 2025/07/18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MaterialSummaryVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**物料编码*/
    private String materialCode;

    /**物料名称*/
    private String materialName;

    /**用量*/
    private BigDecimal usage;

    /**单位*/
    private String unit;

    /**税率*/
    private BigDecimal taxRate;

    /**含税单价*/
    private BigDecimal unitPriceIncludingTax;

    /**不含税单价*/
    private BigDecimal unitPriceExcludingTax;

    /**含税总价*/
    private BigDecimal totalPriceIncludingTax;

    /**不含税总价*/
    private BigDecimal totalPriceExcludingTax;

    /**配方编号*/
    private String formulaCode;

    /**配方名称*/
    private String formulaName;

    /**
     * 计算含税总价和不含税总价
     * 含税总价 = 用量 × 含税单价
     * 不含税总价 = 用量 × 不含税单价
     */
    public void calculateTotalPrice() {
        if (usage != null) {
            if (unitPriceIncludingTax != null) {
                this.totalPriceIncludingTax = usage.multiply(unitPriceIncludingTax);
            } else {
                this.totalPriceIncludingTax = BigDecimal.ZERO;
            }

            if (unitPriceExcludingTax != null) {
                this.totalPriceExcludingTax = usage.multiply(unitPriceExcludingTax);
            } else {
                this.totalPriceExcludingTax = BigDecimal.ZERO;
            }
        } else {
            this.totalPriceIncludingTax = BigDecimal.ZERO;
            this.totalPriceExcludingTax = BigDecimal.ZERO;
        }
    }

    /**
     * 判断是否为有效的物料汇总
     * @return true-有效，false-无效
     */
    public boolean isValid() {
        return materialCode != null && !materialCode.trim().isEmpty() 
            && materialName != null && !materialName.trim().isEmpty();
    }

    /**
     * 获取显示名称
     * @return 物料编码 + 物料名称
     */
    public String getDisplayName() {
        if (materialCode != null && materialName != null) {
            return materialCode + " - " + materialName;
        }
        return materialName != null ? materialName : materialCode;
    }
}
