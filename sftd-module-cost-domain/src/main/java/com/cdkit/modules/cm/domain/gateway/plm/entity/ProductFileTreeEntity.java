package com.cdkit.modules.cm.domain.gateway.plm.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 产品档案树结构实体
 * 基于PLM的ProductTreeDTO结构定义
 * <AUTHOR>
 * @date 2025/07/18
 */
@Data
@Accessors(chain = true)
public class ProductFileTreeEntity implements Serializable {

    /**
     * 节点ID
     */
    private String id;

    /**
     * 父节点ID
     */
    private String parentId;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 生产类型
     */
    private String manufactureType;

    /**
     * 零件类型
     */
    private String partType;

    /**
     * 处理类型
     */
    private String handleType;

    /**
     * 结构类型
     */
    private String structType;

    /**
     * 重量
     */
    private String weight;

    /**
     * 规格
     */
    private String specs;

    /**
     * 版本号
     */
    private String version;

    /**
     * 装配数量
     */
    private BigDecimal assembleQuantity;

    /**
     * 装配序号
     */
    private Integer assembleSort;

    /**
     * 装配单位
     */
    private String assembleUnit;

    /**
     * 标准用量
     */
    private BigDecimal standardQuantity;

    /**
     * 父级工序ID
     */
    private String parentProcessId;

    /**
     * 工序在工艺路线中的ID
     */
    private String processRouteDetailId;

    /**
     * 工时
     */
    private BigDecimal workHour;

    /**
     * 成品总损耗比例
     */
    private BigDecimal lossRatio;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 文档分类
     */
    private String category;

    /**
     * 文档后缀类型
     */
    private String fileType;

    /**
     * 是否已出库
     */
    private Boolean outbound;

    /**
     * 是否已红批
     */
    private Boolean redBatch;

    /**
     * 是否已签名
     */
    private Boolean sign;

    /**
     * 文件大小
     */
    private Integer fileSize;

    /**
     * 差异对比
     */
    private String diff;

    /**
     * 是否是借用节点
     */
    private Boolean borrow;

    /**
     * 原件path
     */
    private String sourcePath;

    /**
     * 计划用量
     */
    private BigDecimal planNum;

    /**
     * 工序名称
     */
    private String processName;

    /**
     * 工序ID
     */
    private String processId;

    /**
     * 产品分类
     */
    private String productTypeName;

    /**
     * 料件类型
     */
    private String itemType;

    /**
     * 原材料线边仓存量
     */
    private BigDecimal rawMaterialLineStock;

    /**
     * 仓库存量
     */
    private Double warehouseStock;

    /**
     * 产品种类代码
     */
    private String productTypeCode;

    /**
     * 是否替代物:1-是 2-否
     */
    private Integer alternativeFlag;

    /**
     * 被替代物物料编码
     */
    private String beforeMaterialCode;

    /**
     * 配方编码
     */
    private String code;

    /**
     * 配方名称
     */
    private String name;

    /**
     * 子节点列表
     */
    private List<ProductFileTreeEntity> children;

    /**
     * 判断是否为最后一个节点（叶子节点）
     * 
     * @return true表示是叶子节点
     */
    public boolean isLastNode() {
        return children == null || children.isEmpty();
    }
}
