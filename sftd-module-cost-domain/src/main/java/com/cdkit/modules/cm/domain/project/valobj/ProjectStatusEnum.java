package com.cdkit.modules.cm.domain.project.valobj;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 项目状态枚举
 * <AUTHOR>
 * @date 2025/07/14
 */
@Getter
@AllArgsConstructor
public enum ProjectStatusEnum {

    /**
     * 风险
     */
    RISK("risk", "风险"),

    /**
     * 待开发
     */
    FOR_DEVELOPMENT("for_development", "待开发"),

    /**
     * 常规
     */
    ROUTINE("routine", "常规");

    /**
     * 状态码
     */
    private final String code;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 根据状态码获取状态名称
     *
     * @param code 状态码
     * @return 状态名称
     */
    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (ProjectStatusEnum status : ProjectStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getName();
            }
        }
        return code;
    }

    /**
     * 根据状态名称获取状态码
     *
     * @param name 状态名称
     * @return 状态码
     */
    public static String getCodeByName(String name) {
        if (name == null) {
            return null;
        }
        for (ProjectStatusEnum status : ProjectStatusEnum.values()) {
            if (status.getName().equals(name)) {
                return status.getCode();
            }
        }
        return name;
    }

    /**
     * 验证状态码是否有效
     *
     * @param code 状态码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        if (code == null) {
            return false;
        }
        for (ProjectStatusEnum status : ProjectStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }
}
