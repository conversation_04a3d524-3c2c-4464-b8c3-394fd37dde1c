package com.cdkit.modules.cm.domain.budget.repository;

import com.cdkit.modules.cm.domain.budget.service.IndirectCostAllocationService;

import java.util.List;

/**
 * 间接成本仓储接口
 * 负责间接成本数据的持久化操作
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
public interface IndirectCostRepository {

    /**
     * 保存本中心间接成本数据
     * 
     * @param allocationItems 分摊结果列表
     * @return 保存成功的数量
     */
    int saveCenterIndirectCost(List<IndirectCostAllocationService.AllocationItem> allocationItems);

    /**
     * 保存非经营中心间接成本数据
     * 
     * @param allocationItems 分摊结果列表
     * @return 保存成功的数量
     */
    int saveNonOperatingIndirectCost(List<IndirectCostAllocationService.AllocationItem> allocationItems);

    /**
     * 保存综合管理间接成本数据
     * 
     * @param allocationItems 分摊结果列表
     * @return 保存成功的数量
     */
    int saveComprehensiveIndirectCost(List<IndirectCostAllocationService.AllocationItem> allocationItems);

    /**
     * 根据预算明细ID删除本中心间接成本数据
     * 
     * @param budgetDetailIds 预算明细ID列表
     * @return 删除的数量
     */
    int deleteCenterIndirectCostByBudgetDetailIds(List<String> budgetDetailIds);

    /**
     * 根据预算明细ID删除非经营中心间接成本数据
     * 
     * @param budgetDetailIds 预算明细ID列表
     * @return 删除的数量
     */
    int deleteNonOperatingIndirectCostByBudgetDetailIds(List<String> budgetDetailIds);

    /**
     * 根据预算明细ID删除综合管理间接成本数据
     * 
     * @param budgetDetailIds 预算明细ID列表
     * @return 删除的数量
     */
    int deleteComprehensiveIndirectCostByBudgetDetailIds(List<String> budgetDetailIds);

    /**
     * 根据预算ID删除所有间接成本数据
     *
     * @param budgetId 预算ID
     * @return 删除的数量
     */
    int deleteAllIndirectCostByBudgetId(String budgetId);

    /**
     * 根据预算明细ID查询本中心间接成本总额
     *
     * @param budgetDetailId 预算明细ID
     * @return 本中心间接成本总额
     */
    java.math.BigDecimal getCenterIndirectCostTotalByBudgetDetailId(String budgetDetailId);

    /**
     * 根据预算明细ID查询非经营中心间接成本总额
     *
     * @param budgetDetailId 预算明细ID
     * @return 非经营中心间接成本总额
     */
    java.math.BigDecimal getNonOperatingIndirectCostTotalByBudgetDetailId(String budgetDetailId);

    /**
     * 根据预算明细ID查询综合管理间接成本总额
     *
     * @param budgetDetailId 预算明细ID
     * @return 综合管理间接成本总额
     */
    java.math.BigDecimal getComprehensiveIndirectCostTotalByBudgetDetailId(String budgetDetailId);
}
