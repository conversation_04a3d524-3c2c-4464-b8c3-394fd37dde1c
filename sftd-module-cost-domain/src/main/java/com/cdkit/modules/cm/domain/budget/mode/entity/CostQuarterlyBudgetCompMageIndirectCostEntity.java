package com.cdkit.modules.cm.domain.budget.mode.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.cdkitframework.poi.excel.annotation.Excel;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @Description: 季度预算-预算科目明细综合管理间接成本表
 * @Author: cdkit-boot
 * @Date:   2025-08-12
 * @Version: V1.0
 */
@Schema(description="cost_quarterly_budget_comp_mage_indirect_cost对象")
@Data
@TableName("cost_quarterly_budget_comp_mage_indirect_cost")
public class CostQuarterlyBudgetCompMageIndirectCostEntity implements Serializable {
    private static final long serialVersionUID = 1L;

	/**UUID主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "UUID主键")
    private String id;
	/**关联季度预算主表ID*/
    @Schema(description = "关联季度预算主表ID")
    private String quarterlyBudgetId;
	/**综合管理间接成本-预算科目编码（关联预算科目表）*/
	@Excel(name = "综合管理间接成本-预算科目编码（关联预算科目表）", width = 15)
    @Schema(description = "综合管理间接成本-预算科目编码（关联预算科目表）")
    private String budgetSubjectCode;
	/**综合管理间接成本-预算科目名称*/
	@Excel(name = "综合管理间接成本-预算科目名称", width = 15)
    @Schema(description = "综合管理间接成本-预算科目名称")
    private String budgetSubjectName;
	/**综合管理间接成本-预算释义*/
	@Excel(name = "综合管理间接成本-预算释义", width = 15)
    @Schema(description = "综合管理间接成本-预算释义")
    private String subjectDescription;
	/**综合管理间接成本-支出预算金额（元）*/
	@Excel(name = "综合管理间接成本-支出预算金额（元）", width = 15)
    @Schema(description = "综合管理间接成本-支出预算金额（元）")
    private java.math.BigDecimal expendBudgetAmount;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**租户ID*/
	@Excel(name = "租户ID", width = 15)
    @Schema(description = "租户ID")
    private Integer tenantId;
	/**删除标识 0:未删除 1:删除*/
	@Excel(name = "删除标识 0:未删除 1:删除", width = 15)
    @Schema(description = "删除标识 0:未删除 1:删除")
    @TableLogic
    private Integer delFlag;
	/**所属部门代码*/
    @Schema(description = "所属部门代码")
    private String sysOrgCode;
}
