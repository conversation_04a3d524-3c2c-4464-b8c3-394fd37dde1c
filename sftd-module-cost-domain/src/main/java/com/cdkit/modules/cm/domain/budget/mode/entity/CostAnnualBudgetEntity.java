package com.cdkit.modules.cm.domain.budget.mode.entity;

import com.cdkitframework.poi.excel.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 年度总预算领域实体
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
public class CostAnnualBudgetEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**UUID主键*/
    private String id;

    /**总预算编号(ZYS+4位年份+3位流水)*/
    private String budgetCode;

    /**总预算名称*/
    private String budgetName;

    /**年份*/
    private String budgetYear;

    /**版本号*/
    private String version;

    /**状态(PENDING_LOCK-待锁定/APPROVING-审批中/LOCKED-已锁定/CHANGED-已变更)*/
    private String budgetStatus;

    /**所属单位*/
    private String professionalCompany;
    /**收入（不含税）总金额（元）*/
    private java.math.BigDecimal revenueTotalAmount;
    /**直接成本（不含税）总金额（元）*/
    private java.math.BigDecimal directCostTotalAmount;

    /**利润总额*/
    private BigDecimal profitTotalAmount;

    /**利润率*/
    private BigDecimal profitRate;

    /**提交时间*/
    private Date submitTime;

    /**提交人*/
    private String submitBy;

    /**审批时间*/
    private Date approveTime;

    /**审批人*/
    private String approveBy;

    /**审批备注*/
    private String approveRemark;

    /**备注*/
    private String remark;

    /**父预算ID(变更时关联原预算)*/
    private String parentBudgetId;

    /**变更原因*/
    private String changeReason;

    /**附件URL*/
    private String attachmentUrl;

    /**创建时间*/
    private Date createTime;

    /**创建人*/
    private String createBy;

    /**更新时间*/
    private Date updateTime;

    /**更新人*/
    private String updateBy;

    /**租户ID*/
    private Integer tenantId;

    /**删除标识 0:未删除 1:删除*/
    private Integer delFlag;

    /**所属部门代码*/
    private String sysOrgCode;


    /**
     * 验证预算状态是否可以修改
     *
     * @return true-可以修改，false-不可修改
     */
    public boolean canModify() {
        return "PENDING_LOCK".equals(budgetStatus) || "CHANGED".equals(budgetStatus);
    }

    /**
     * 根据明细数据计算总预算汇总统计值
     *
     * @param budgetDetailList 预算明细列表
     */
    public void calculateSummaryStatistics(List<BudgetDetailInfo> budgetDetailList) {
        if (budgetDetailList == null || budgetDetailList.isEmpty()) {
            // 如果没有明细数据，设置为0
            this.revenueTotalAmount = BigDecimal.ZERO;
            this.directCostTotalAmount = BigDecimal.ZERO;
            this.profitTotalAmount = BigDecimal.ZERO;
            this.profitRate = BigDecimal.ZERO;
            return;
        }

        // 1. 收入（不含税）总额 = 各项目收入预算总额之和
        BigDecimal totalRevenue = budgetDetailList.stream()
                .map(BudgetDetailInfo::getRevenueBudget)
                .filter(amount -> amount != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 2. 直接成本（不含税）总额 = 各项目直接成本总额之和
        BigDecimal totalDirectCost = budgetDetailList.stream()
                .map(BudgetDetailInfo::getDirectCostBudget)
                .filter(amount -> amount != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 3. 间接成本总额 = 本中心间接成本之和 + 非经营中心间接成本之和 + 综合管理间接成本之和
        BigDecimal totalCenterIndirectCost = budgetDetailList.stream()
                .map(BudgetDetailInfo::getCenterIndirectCostTotal)
                .filter(amount -> amount != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalNonOperatingIndirectCost = budgetDetailList.stream()
                .map(BudgetDetailInfo::getNonOperatingCenterIndirectCostTotal)
                .filter(amount -> amount != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalComprehensiveIndirectCost = budgetDetailList.stream()
                .map(BudgetDetailInfo::getComprehensiveManagementIndirectCostTotal)
                .filter(amount -> amount != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalIndirectCost = totalCenterIndirectCost
                .add(totalNonOperatingIndirectCost)
                .add(totalComprehensiveIndirectCost);

        // 4. 利润总额 = 收入（不含税）总额 - 直接成本（不含税）总额 - 间接成本总额
        BigDecimal totalProfit = totalRevenue
                .subtract(totalDirectCost)
                .subtract(totalIndirectCost);

        // 5. 利润率 = 利润总额 / 收入（不含税）总额 × 100
        BigDecimal profitRateValue = BigDecimal.ZERO;
        if (totalRevenue.compareTo(BigDecimal.ZERO) > 0) {
            profitRateValue = totalProfit
                    .divide(totalRevenue, 6, java.math.RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"))
                    .setScale(2, java.math.RoundingMode.HALF_UP);
        }

        // 设置计算结果（收入和直接成本单位都是元，无需转换）
        this.revenueTotalAmount = totalRevenue.setScale(2, java.math.RoundingMode.HALF_UP);
        this.directCostTotalAmount = totalDirectCost.setScale(2, java.math.RoundingMode.HALF_UP);
        this.profitTotalAmount = totalProfit.setScale(2, java.math.RoundingMode.HALF_UP);
        this.profitRate = profitRateValue;
    }

    /**
     * 验证预算状态是否可以删除
     *
     * @return true-可以删除，false-不可删除
     */
    public boolean canDelete() {
        return "PENDING_LOCK".equals(budgetStatus);
    }

    /**
     * 项目年度预算明细信息
     */
    @Data
    public static class BudgetDetailInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        /**UUID主键*/
        private String id;

        /**关联预算主表ID*/
        private String budgetId;

        /**关联项目年度计划ID*/
        private String planId;

        /**计划编号*/
        private String planCode;

        /**项目编号*/
        private String projectCode;

        /**年度预算项目名称*/
        private String projectName;

        /**所属单位*/
        private String professionalCompany;

        /**下属中心*/
        private String center;

        /**预算类型*/
        private String budgetType;

        /**WBS编号*/
        private String wbsCode;

        /**项目类型*/
        private String projectType;

        /**四级业务*/
        private String fourthLevelBusiness;

        /**业务小类*/
        private String businessSubcategory;

        /**收入预算(元)*/
        private BigDecimal revenueBudget;

        /**直接成本预算(元)*/
        private BigDecimal directCostBudget;

        /**间接成本总额(元)*/
        private BigDecimal indirectCostTotal;

        /**本中心间接成本总额(元)*/
        private BigDecimal centerIndirectCostTotal;

        /**非经营中心间接成本总额(元)*/
        private BigDecimal nonOperatingCenterIndirectCostTotal;

        /**综合管理间接成本总额(元)*/
        private BigDecimal comprehensiveManagementIndirectCostTotal;

        /**净利润(元)*/
        private BigDecimal netProfit;

        /**净利润率(%)*/
        private BigDecimal netProfitRate;

        /**毛利润(元)*/
        private BigDecimal grossProfit;

        /**毛利润率(%)*/
        private BigDecimal grossProfitRate;

        /**边际利润(元)*/
        private BigDecimal marginalProfit;

        /**边际利润率(%)*/
        private BigDecimal marginalProfitRate;

        /**直接成本明细列表*/
        private List<DirectCostInfo> directCostList;

        /**
         * 计算间接成本总额
         * 间接成本总额 = 本中心间接成本 + 非经营中心间接成本 + 综合管理间接成本
         */
        public void calculateIndirectCostTotal() {
            BigDecimal total = BigDecimal.ZERO;

            if (centerIndirectCostTotal != null) {
                total = total.add(centerIndirectCostTotal);
            }
            if (nonOperatingCenterIndirectCostTotal != null) {
                total = total.add(nonOperatingCenterIndirectCostTotal);
            }
            if (comprehensiveManagementIndirectCostTotal != null) {
                total = total.add(comprehensiveManagementIndirectCostTotal);
            }

            this.indirectCostTotal = total;
        }

        /**
         * 计算净利润
         * 净利润 = 收入预算总额(元) - 直接成本总额(元) - 间接成本总额(元)
         */
        public void calculateNetProfit() {
            if (revenueBudget == null || directCostBudget == null) {
                this.netProfit = null;
                return;
            }

            // 收入预算是万元，需要转换为元
            BigDecimal revenueInYuan = revenueBudget.multiply(new BigDecimal("10000"));
            // 直接成本预算是万元，需要转换为元
            BigDecimal directCostInYuan = directCostBudget.multiply(new BigDecimal("10000"));

            BigDecimal profit = revenueInYuan.subtract(directCostInYuan);

            if (indirectCostTotal != null) {
                profit = profit.subtract(indirectCostTotal);
            }

            this.netProfit = profit.setScale(2, java.math.RoundingMode.HALF_UP);
        }

        /**
         * 计算净利润率
         * 净利润率 = 净利润 / 收入预算总额(元) × 100
         */
        public void calculateNetProfitRate() {
            if (netProfit == null || revenueBudget == null || revenueBudget.compareTo(BigDecimal.ZERO) == 0) {
                this.netProfitRate = null;
                return;
            }

            // 收入预算单位是元，无需转换
            this.netProfitRate = netProfit
                    .divide(revenueBudget, 6, java.math.RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"))
                    .setScale(2, java.math.RoundingMode.HALF_UP);
        }

        /**
         * 计算毛利润
         * 毛利润 = 收入预算总额(元) - 直接成本总额(元) - 本中心间接成本总额(元)
         */
        public void calculateGrossProfit() {
            if (revenueBudget == null || directCostBudget == null) {
                this.grossProfit = null;
                return;
            }

            // 收入预算和直接成本预算单位都是元，无需转换
            BigDecimal profit = revenueBudget.subtract(directCostBudget);

            if (centerIndirectCostTotal != null) {
                profit = profit.subtract(centerIndirectCostTotal);
            }

            this.grossProfit = profit.setScale(2, java.math.RoundingMode.HALF_UP);
        }

        /**
         * 计算毛利润率
         * 毛利润率 = 毛利润 / 收入预算总额(元) × 100
         */
        public void calculateGrossProfitRate() {
            if (grossProfit == null || revenueBudget == null || revenueBudget.compareTo(BigDecimal.ZERO) == 0) {
                this.grossProfitRate = null;
                return;
            }

            // 收入预算单位是元，无需转换
            this.grossProfitRate = grossProfit
                    .divide(revenueBudget, 6, java.math.RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"))
                    .setScale(2, java.math.RoundingMode.HALF_UP);
        }

        /**
         * 计算边际利润
         * 边际利润 = 收入预算总额(元) - 直接成本总额(元)
         */
        public void calculateMarginalProfit() {
            if (revenueBudget == null || directCostBudget == null) {
                this.marginalProfit = null;
                return;
            }

            // 收入预算和直接成本预算单位都是元，无需转换
            this.marginalProfit = revenueBudget.subtract(directCostBudget)
                    .setScale(2, java.math.RoundingMode.HALF_UP);
        }

        /**
         * 计算边际利润率
         * 边际利润率 = 边际利润 / 收入预算总额(元) × 100
         */
        public void calculateMarginalProfitRate() {
            if (marginalProfit == null || revenueBudget == null || revenueBudget.compareTo(BigDecimal.ZERO) == 0) {
                this.marginalProfitRate = null;
                return;
            }

            // 收入预算单位是元，无需转换
            this.marginalProfitRate = marginalProfit
                    .divide(revenueBudget, 6, java.math.RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"))
                    .setScale(2, java.math.RoundingMode.HALF_UP);
        }

        /**
         * 计算所有统计值
         * 按照依赖关系顺序计算：间接成本总额 -> 各种利润 -> 各种利润率
         */
        public void calculateAllStatistics() {
            calculateIndirectCostTotal();
            calculateNetProfit();
            calculateNetProfitRate();
            calculateGrossProfit();
            calculateGrossProfitRate();
            calculateMarginalProfit();
            calculateMarginalProfitRate();
        }
    }

    /**
     * 直接成本明细信息
     */
    @Data
    public static class DirectCostInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        /**UUID主键*/
        private String id;

        /**关联年度预算明细ID*/
        private String budgetDetailId;

        /**预算科目编码*/
        private String subjectCode;

        /**预算科目名称*/
        private String subjectName;

        /**科目释义*/
        private String subjectDescription;

        /**支出预算金额(万元)*/
        private BigDecimal budgetAmount;
    }
}
