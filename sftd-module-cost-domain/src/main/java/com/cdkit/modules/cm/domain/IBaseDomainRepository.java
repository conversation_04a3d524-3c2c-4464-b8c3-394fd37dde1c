package com.cdkit.modules.cm.domain;

import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-04-30 08:10
 * @Description domain Repository
 * @since 1.0
 */
public interface IBaseDomainRepository<T, D> {

    /**
     * 保存
     *
     * @param objectDTO DTO对象
     * @return 唯一主键
     */
    default String addDomain(D objectDTO) {
        return null;
    }

    /**
     * 删除
     *
     * @param id 唯一主键
     */
    default void deleteDomainById(String id) {

    }

    /**
     * 批量删除
     *
     * @param ids 唯一主键集合
     */
    default void deleteDomainById(List<String> ids) {

    }

    /**
     * 修改
     *
     * @param objectDTO dto对象
     * @return 修改后的domain对象
     */
    default T updateDomainById(D objectDTO) {
        return null;
    }

    /**
     * 根据主键查询领域对象
     *
     * @param id 唯一主键
     * @return DTO对象
     */
    default T getDomainById(String id) {
        return null;
    }

    /**
     * 根据条件分页查询
     *
     * @param objectDTO 查询条件
     * @param pageReq   分页条件
     * @return 查询结果
     */
    default PageRes<D> page(D objectDTO, PageReq pageReq) {
        return null;
    }
}
