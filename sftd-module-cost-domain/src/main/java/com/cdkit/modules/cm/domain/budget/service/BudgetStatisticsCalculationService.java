package com.cdkit.modules.cm.domain.budget.service;

import com.cdkit.modules.cm.domain.budget.mode.entity.CostAnnualBudgetEntity;
import com.cdkit.modules.cm.domain.budget.repository.IndirectCostRepository;
import com.cdkit.modules.cm.domain.budget.repository.CostAnnualBudgetDetailRepository;
import com.cdkit.modules.cm.domain.budget.repository.CostAnnualBudgetRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 预算统计值计算服务
 * 负责计算项目年度预算的各种统计值
 * 符合DDD架构规范，业务逻辑在Domain层处理
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BudgetStatisticsCalculationService {

    private final IndirectCostRepository indirectCostRepository;
    private final CostAnnualBudgetDetailRepository costAnnualBudgetDetailRepository;
    private final CostAnnualBudgetRepository costAnnualBudgetRepository;

    /**
     * 计算并更新预算明细的统计值
     *
     * @param budgetId 总预算ID
     * @param budgetDetailList 预算明细列表
     */
    public void calculateAndUpdateStatistics(String budgetId, List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList) {
        if (budgetDetailList == null || budgetDetailList.isEmpty()) {
            log.info("预算明细列表为空，无需计算统计值");
            return;
        }

        log.info("开始计算预算明细统计值，明细数量: {}", budgetDetailList.size());

        // 1. 计算明细统计值
        for (CostAnnualBudgetEntity.BudgetDetailInfo detailInfo : budgetDetailList) {
            calculateDetailStatistics(detailInfo);
        }

        // 2. 保存明细统计值到数据库
        saveStatisticsToDatabase(budgetDetailList);

        // 3. 计算并保存总预算汇总统计值
        calculateAndSaveBudgetSummaryStatistics(budgetId, budgetDetailList);

        log.info("预算明细统计值计算完成");
    }

    /**
     * 计算单个预算明细的统计值
     * 
     * @param detailInfo 预算明细信息
     */
    public void calculateDetailStatistics(CostAnnualBudgetEntity.BudgetDetailInfo detailInfo) {
        if (detailInfo == null || detailInfo.getId() == null) {
            log.warn("预算明细信息为空或ID为空，跳过统计值计算");
            return;
        }

        log.debug("开始计算预算明细统计值，项目编号: {}, 项目名称: {}", 
                detailInfo.getProjectCode(), detailInfo.getProjectName());

        // 1. 查询并设置间接成本分摊值
        setIndirectCostAmounts(detailInfo);

        // 2. 计算所有统计值（使用Domain层的业务逻辑）
        detailInfo.calculateAllStatistics();

        log.debug("预算明细统计值计算完成，项目编号: {}, 净利润: {}, 净利润率: {}%", 
                detailInfo.getProjectCode(), detailInfo.getNetProfit(), detailInfo.getNetProfitRate());
    }

    /**
     * 设置间接成本分摊值
     * 从Infrastructure层查询数据，但业务逻辑保持在Domain层
     * 
     * @param detailInfo 预算明细信息
     */
    private void setIndirectCostAmounts(CostAnnualBudgetEntity.BudgetDetailInfo detailInfo) {
        String budgetDetailId = detailInfo.getId();

        try {
            // 查询本中心间接成本总额
            BigDecimal centerIndirectCost = indirectCostRepository.getCenterIndirectCostTotalByBudgetDetailId(budgetDetailId);
            detailInfo.setCenterIndirectCostTotal(centerIndirectCost != null ? centerIndirectCost : BigDecimal.ZERO);

            // 查询非经营中心间接成本总额
            BigDecimal nonOperatingIndirectCost = indirectCostRepository.getNonOperatingIndirectCostTotalByBudgetDetailId(budgetDetailId);
            detailInfo.setNonOperatingCenterIndirectCostTotal(nonOperatingIndirectCost != null ? nonOperatingIndirectCost : BigDecimal.ZERO);

            // 查询综合管理间接成本总额
            BigDecimal comprehensiveIndirectCost = indirectCostRepository.getComprehensiveIndirectCostTotalByBudgetDetailId(budgetDetailId);
            detailInfo.setComprehensiveManagementIndirectCostTotal(comprehensiveIndirectCost != null ? comprehensiveIndirectCost : BigDecimal.ZERO);

            log.debug("间接成本分摊值设置完成，项目编号: {}, 本中心: {}, 非经营中心: {}, 综合管理: {}", 
                    detailInfo.getProjectCode(), centerIndirectCost, nonOperatingIndirectCost, comprehensiveIndirectCost);

        } catch (Exception e) {
            log.error("查询间接成本分摊值失败，预算明细ID: {}, 项目编号: {}", 
                    budgetDetailId, detailInfo.getProjectCode(), e);
            
            // 设置默认值
            detailInfo.setCenterIndirectCostTotal(BigDecimal.ZERO);
            detailInfo.setNonOperatingCenterIndirectCostTotal(BigDecimal.ZERO);
            detailInfo.setComprehensiveManagementIndirectCostTotal(BigDecimal.ZERO);
        }
    }

    /**
     * 批量计算预算明细统计值（用于数据修复或批量更新）
     * 
     * @param budgetId 预算ID
     * @param budgetDetailList 预算明细列表
     */
    public void batchCalculateStatistics(String budgetId, List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList) {
        log.info("开始批量计算预算统计值，预算ID: {}, 明细数量: {}", budgetId, budgetDetailList.size());

        calculateAndUpdateStatistics(budgetId, budgetDetailList);

        log.info("批量计算预算统计值完成，预算ID: {}", budgetId);
    }

    /**
     * 保存统计值到数据库
     *
     * @param budgetDetailList 预算明细列表
     */
    private void saveStatisticsToDatabase(List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList) {
        log.info("开始保存统计值到数据库，明细数量: {}", budgetDetailList.size());

        try {
            for (CostAnnualBudgetEntity.BudgetDetailInfo detailInfo : budgetDetailList) {
                // 更新预算明细的统计字段
                costAnnualBudgetDetailRepository.updateBudgetDetailStatistics(
                    detailInfo.getId(),
                    detailInfo.getIndirectCostTotal(),
                    detailInfo.getCenterIndirectCostTotal(),
                    detailInfo.getNonOperatingCenterIndirectCostTotal(),
                    detailInfo.getComprehensiveManagementIndirectCostTotal(),
                    detailInfo.getNetProfit(),
                    detailInfo.getNetProfitRate(),
                    detailInfo.getGrossProfit(),
                    detailInfo.getGrossProfitRate(),
                    detailInfo.getMarginalProfit(),
                    detailInfo.getMarginalProfitRate()
                );

                log.debug("保存统计值完成，项目编号: {}, 净利润: {}, 净利润率: {}%",
                        detailInfo.getProjectCode(), detailInfo.getNetProfit(), detailInfo.getNetProfitRate());
            }

            log.info("统计值保存到数据库完成");

        } catch (Exception e) {
            log.error("保存统计值到数据库失败", e);
            throw new RuntimeException("保存统计值失败：" + e.getMessage(), e);
        }
    }

    /**
     * 计算并保存总预算汇总统计值
     *
     * @param budgetId 总预算ID
     * @param budgetDetailList 预算明细列表
     */
    private void calculateAndSaveBudgetSummaryStatistics(String budgetId, List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList) {
        if (budgetId == null || budgetId.trim().isEmpty()) {
            log.warn("总预算ID为空，跳过汇总统计值计算");
            return;
        }

        log.info("开始计算总预算汇总统计值，budgetId: {}", budgetId);

        try {
            // 创建临时的总预算实体用于计算
            CostAnnualBudgetEntity tempBudgetEntity = new CostAnnualBudgetEntity();
            tempBudgetEntity.setId(budgetId);

            // 使用Domain层的业务逻辑计算汇总统计值
            tempBudgetEntity.calculateSummaryStatistics(budgetDetailList);

            // 保存汇总统计值到数据库
            costAnnualBudgetRepository.updateBudgetSummaryStatistics(
                budgetId,
                tempBudgetEntity.getRevenueTotalAmount(),
                tempBudgetEntity.getDirectCostTotalAmount(),
                tempBudgetEntity.getProfitTotalAmount(),
                tempBudgetEntity.getProfitRate()
            );

            log.info("总预算汇总统计值计算完成，budgetId: {}, 利润总额: {}, 利润率: {}%",
                    budgetId, tempBudgetEntity.getProfitTotalAmount(), tempBudgetEntity.getProfitRate());

        } catch (Exception e) {
            log.error("计算总预算汇总统计值失败，budgetId: {}", budgetId, e);
            throw new RuntimeException("计算汇总统计值失败：" + e.getMessage(), e);
        }
    }
}
