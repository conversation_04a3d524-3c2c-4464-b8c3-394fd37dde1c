package com.cdkit.modules.cm.domain.budget.service;

import com.cdkit.modules.cm.domain.budget.repository.CostQuarterlyBudgetRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 季度预算编号生成领域服务
 * 负责生成季度预算编号，规则：JDYS+8位日期+3位流水
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class QuarterlyBudgetNumberGenerationService {

    private final CostQuarterlyBudgetRepository costQuarterlyBudgetRepository;

    /**
     * 生成下一个季度预算编号
     * 规则：JDYS+8位日期+3位流水（如：JDYS202508120001）
     *
     * @return 下一个季度预算编号
     */
    public String generateNextQuarterlyBudgetNo() {
        // 获取当前日期，格式化为8位日期字符串（YYYYMMDD）
        String currentDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        
        log.info("开始生成季度预算编号，当前日期: {}", currentDate);

        // 查询指定日期的最大预算编号
        String maxBudgetNo = costQuarterlyBudgetRepository.findMaxQuarterlyBudgetNoByDate(currentDate);

        int nextSequence = 1; // 默认从001开始

        if (StringUtils.hasText(maxBudgetNo)) {
            // 解析流水号：JDYS+8位日期+3位流水号
            // 预算编号格式：JDYS20250812001，总长度15位
            if (maxBudgetNo.length() >= 15) {
                String sequenceStr = maxBudgetNo.substring(12); // 去掉"JDYS"和8位日期，取最后3位流水号
                try {
                    int currentSequence = Integer.parseInt(sequenceStr);
                    nextSequence = currentSequence + 1;
                    log.info("解析到当前最大流水号: {}, 下一个流水号: {}", currentSequence, nextSequence);
                } catch (NumberFormatException e) {
                    log.warn("解析季度预算编号流水号失败，使用默认值001，预算编号: {}", maxBudgetNo);
                }
            } else {
                log.warn("季度预算编号格式不正确，使用默认流水号001，预算编号: {}", maxBudgetNo);
            }
        } else {
            log.info("当前日期没有找到已有的季度预算编号，使用默认流水号001");
        }

        // 生成新的季度预算编号：JDYS+8位日期+3位流水号（补零）
        String nextBudgetNo = String.format("JDYS%s%03d", currentDate, nextSequence);

        log.info("生成下一个季度预算编号成功，日期: {}, 预算编号: {}", currentDate, nextBudgetNo);
        return nextBudgetNo;
    }

    /**
     * 验证季度预算编号格式
     * 
     * @param budgetNo 季度预算编号
     * @return 是否符合格式要求
     */
    public boolean validateBudgetNoFormat(String budgetNo) {
        if (!StringUtils.hasText(budgetNo)) {
            return false;
        }

        // 检查长度：JDYS(4) + 日期(8) + 流水号(3) = 15位
        if (budgetNo.length() != 15) {
            log.warn("季度预算编号长度不正确，期望15位，实际{}位，编号: {}", budgetNo.length(), budgetNo);
            return false;
        }

        // 检查前缀
        if (!budgetNo.startsWith("JDYS")) {
            log.warn("季度预算编号前缀不正确，期望JDYS，编号: {}", budgetNo);
            return false;
        }

        // 检查日期部分（第5-12位）
        String datePart = budgetNo.substring(4, 12);
        try {
            LocalDate.parse(datePart, DateTimeFormatter.ofPattern("yyyyMMdd"));
        } catch (Exception e) {
            log.warn("季度预算编号日期部分格式不正确，日期部分: {}, 编号: {}", datePart, budgetNo);
            return false;
        }

        // 检查流水号部分（第13-15位）
        String sequencePart = budgetNo.substring(12);
        try {
            int sequence = Integer.parseInt(sequencePart);
            if (sequence < 1 || sequence > 999) {
                log.warn("季度预算编号流水号超出范围(001-999)，流水号: {}, 编号: {}", sequence, budgetNo);
                return false;
            }
        } catch (NumberFormatException e) {
            log.warn("季度预算编号流水号格式不正确，流水号部分: {}, 编号: {}", sequencePart, budgetNo);
            return false;
        }

        return true;
    }
}
