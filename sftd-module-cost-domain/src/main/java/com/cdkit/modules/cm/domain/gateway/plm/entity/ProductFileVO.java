package com.cdkit.modules.cm.domain.gateway.plm.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * PLM产品档案返回结果VO
 * 对应PLM的ProductFileVO
 * <AUTHOR>
 * @date 2025/07/18
 */
@Data
public class ProductFileVO implements Serializable {
    
    /**
     * 产品BOM树结构
     */
    private ProductFileTreeEntity productBom;
    
    /**
     * 工艺路线列表
     */
    private List<ProductProcessRouteVO> processRoute;
}
