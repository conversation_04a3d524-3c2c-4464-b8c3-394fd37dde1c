package com.cdkit.modules.cm.domain.budget.repository;

import com.cdkit.modules.cm.domain.budget.mode.entity.CostAnnualBudgetEntity;

import java.math.BigDecimal;
import java.util.List;

/**
 * 年度预算明细仓储接口
 * <AUTHOR>
 * @date 2025-08-04
 */
public interface CostAnnualBudgetDetailRepository {

    /**
     * 保存年度预算明细数据
     * 
     * @param budgetId 年度预算主表ID
     * @param budgetDetailList 明细数据列表
     */
    void saveBudgetDetails(String budgetId, List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList);

    /**
     * 根据预算ID查询明细数据
     * 
     * @param budgetId 年度预算主表ID
     * @return 明细数据列表
     */
    List<CostAnnualBudgetEntity.BudgetDetailInfo> findByBudgetId(String budgetId);

    /**
     * 删除年度预算明细数据
     *
     * @param budgetId 年度预算主表ID
     */
    void deleteByBudgetId(String budgetId);

    /**
     * 更新预算明细的统计值
     *
     * @param budgetDetailId 预算明细ID
     * @param indirectCostTotal 间接成本总额
     * @param centerIndirectCostTotal 本中心间接成本总额
     * @param nonOperatingCenterIndirectCostTotal 非经营中心间接成本总额
     * @param comprehensiveManagementIndirectCostTotal 综合管理间接成本总额
     * @param netProfit 净利润
     * @param netProfitRate 净利润率
     * @param grossProfit 毛利润
     * @param grossProfitRate 毛利润率
     * @param marginalProfit 边际利润
     * @param marginalProfitRate 边际利润率
     */
    void updateBudgetDetailStatistics(String budgetDetailId,
                                    java.math.BigDecimal indirectCostTotal,
                                    java.math.BigDecimal centerIndirectCostTotal,
                                    java.math.BigDecimal nonOperatingCenterIndirectCostTotal,
                                    java.math.BigDecimal comprehensiveManagementIndirectCostTotal,
                                    java.math.BigDecimal netProfit,
                                    java.math.BigDecimal netProfitRate,
                                    java.math.BigDecimal grossProfit,
                                    java.math.BigDecimal grossProfitRate,
                                    java.math.BigDecimal marginalProfit,
                                    java.math.BigDecimal marginalProfitRate);

    /**
     * 根据项目编号查询项目年度预算信息
     *
     * @param projectCode 项目编号
     * @return 项目年度预算信息列表
     */
    List<ProjectBudgetInfo> findByProjectCode(String projectCode);

    /**
     * 项目年度预算信息
     */
    class ProjectBudgetInfo {
        private String annualBudgetCode;
        private String wbsCode;
        private String professionalCompany;
        private String center;
        private String projectName;
        private String budgetType;
        private BigDecimal annualRevenueBudget;
        private BigDecimal annualExpenditureBudget;

        // Getters and Setters
        public String getAnnualBudgetCode() { return annualBudgetCode; }
        public void setAnnualBudgetCode(String annualBudgetCode) { this.annualBudgetCode = annualBudgetCode; }

        public String getWbsCode() { return wbsCode; }
        public void setWbsCode(String wbsCode) { this.wbsCode = wbsCode; }

        public String getProfessionalCompany() { return professionalCompany; }
        public void setProfessionalCompany(String professionalCompany) { this.professionalCompany = professionalCompany; }

        public String getCenter() { return center; }
        public void setCenter(String center) { this.center = center; }

        public String getProjectName() { return projectName; }
        public void setProjectName(String projectName) { this.projectName = projectName; }

        public String getBudgetType() { return budgetType; }
        public void setBudgetType(String budgetType) { this.budgetType = budgetType; }

        public BigDecimal getAnnualRevenueBudget() { return annualRevenueBudget; }
        public void setAnnualRevenueBudget(BigDecimal annualRevenueBudget) { this.annualRevenueBudget = annualRevenueBudget; }

        public BigDecimal getAnnualExpenditureBudget() { return annualExpenditureBudget; }
        public void setAnnualExpenditureBudget(BigDecimal annualExpenditureBudget) { this.annualExpenditureBudget = annualExpenditureBudget; }
    }
}
