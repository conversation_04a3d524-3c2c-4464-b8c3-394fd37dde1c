package com.cdkit.modules.cm.domain.gateway.material;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.modules.cm.domain.gateway.material.entity.MaterialDetailEntity;

/**
 * 材料外部服务接口
 * 用于调用外部材料管理模块的API
 * <AUTHOR>
 * @date 2025/07/16
 */
public interface MaterialExternalGateway {

    /**
     * 分页查询材料列表
     * 调用IMdMaterialApi.listByPage方法
     *
     * @param materialCode 物料编码
     * @param materialName 物料名称
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @return 分页查询结果
     */
    Page<MaterialDetailEntity> listByPage(String materialCode, String materialName, Integer pageNo, Integer pageSize);
}
