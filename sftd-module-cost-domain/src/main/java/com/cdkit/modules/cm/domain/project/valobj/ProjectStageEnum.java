package com.cdkit.modules.cm.domain.project.valobj;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 项目阶段枚举
 * <AUTHOR>
 * @date 2025/07/14
 */
@Getter
@AllArgsConstructor
public enum ProjectStageEnum {

    /**
     * 待签合同
     */
    PENDING_CONTRACT("pending_contract", "待签合同"),

    /**
     * 已签合同
     */
    CONTRACT_SIGNED("contract_signed", "已签合同"),

    /**
     * 执行中
     */
    IN_EXECUTION("in_execution", "执行中"),

    /**
     * 已关闭
     */
    CLOSED("closed", "已关闭");

    /**
     * 阶段码
     */
    private final String code;

    /**
     * 阶段名称
     */
    private final String name;

    /**
     * 根据阶段码获取阶段名称
     *
     * @param code 阶段码
     * @return 阶段名称
     */
    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (ProjectStageEnum stage : ProjectStageEnum.values()) {
            if (stage.getCode().equals(code)) {
                return stage.getName();
            }
        }
        return code;
    }

    /**
     * 根据阶段名称获取阶段码
     *
     * @param name 阶段名称
     * @return 阶段码
     */
    public static String getCodeByName(String name) {
        if (name == null) {
            return null;
        }
        for (ProjectStageEnum stage : ProjectStageEnum.values()) {
            if (stage.getName().equals(name)) {
                return stage.getCode();
            }
        }
        return name;
    }

    /**
     * 验证阶段码是否有效
     *
     * @param code 阶段码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        if (code == null) {
            return false;
        }
        for (ProjectStageEnum stage : ProjectStageEnum.values()) {
            if (stage.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }
}
