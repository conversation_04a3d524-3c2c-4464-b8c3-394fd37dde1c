package com.cdkit.modules.cm.domain.gateway.outsourcing;

import com.cdkit.modules.cm.domain.outsourcing.entity.OutsourcingOrderEntity;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 外委外部服务网关接口
 * 用于调用外委服务的API
 * 
 * <AUTHOR>
 * @date 2025-08-13
 */
public interface OutsourcingExternalGateway {

    /**
     * 新增外委单据
     * 调用外委服务接口：/wms/api/outsourcingOrder/addOutsourcingOrder
     * 
     * @param outsourcingOrders 外委单据列表
     * @return 外委单据ID
     */
    String addOutsourcingOrder(List<OutsourcingOrderEntity> outsourcingOrders);

    /**
     * 查询是否有关联外委单（主子表全）
     * 调用外委服务接口：/wms/api/outsourcingOrder/querBySourceBillNumberAll
     * 
     * @param sourceBillNumber 上游单据号（项目计划号）
     * @return 外委单据列表
     */
    List<OutsourcingOrderEntity> queryBySourceBillNumberAll(String sourceBillNumber);

    /**
     * 根据项目计划ID和物料编码批量查询已转量
     * 只统计审批通过的外委单据
     *
     * @param planId 项目计划ID
     * @param materialCodes 物料编码列表
     * @return 物料编码与已转量的映射
     */
    Map<String, BigDecimal> batchGetTransferredAmountByProducts(String planId, List<String> materialCodes);

    /**
     * 根据项目计划ID和物料编码批量查询已转量
     * 只统计审批通过的外委单据
     * 
     * @param planId 项目计划ID
     * @param materialCodes 物料编码列表
     * @return 物料编码与已转量的映射
     */
    Map<String, BigDecimal> batchGetTransferredAmountByMaterials(String planId, List<String> materialCodes);

    /**
     * 检查外委单据状态
     * 检查指定外委单据的审批状态
     * 
     * @param outsourcingOrderId 外委单据ID
     * @return 外委单据实体
     */
    OutsourcingOrderEntity checkOutsourcingOrderStatus(String outsourcingOrderId);

    /**
     * 取消外委单据
     * 取消指定的外委单据
     * 
     * @param outsourcingOrderId 外委单据ID
     * @return 是否取消成功
     */
    boolean cancelOutsourcingOrder(String outsourcingOrderId);
}
