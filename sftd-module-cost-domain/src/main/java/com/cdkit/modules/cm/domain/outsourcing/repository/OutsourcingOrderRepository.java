package com.cdkit.modules.cm.domain.outsourcing.repository;

import com.cdkit.modules.cm.domain.outsourcing.entity.OutsourcingOrderEntity;

import java.math.BigDecimal;
import java.util.List;

/**
 * 外委单据仓储接口
 * 
 * <AUTHOR>
 * @date 2025-08-13
 */
public interface OutsourcingOrderRepository {

    /**
     * 保存外委单据
     * 
     * @param entity 外委单据实体
     * @return 保存后的实体
     */
    OutsourcingOrderEntity save(OutsourcingOrderEntity entity);

    /**
     * 根据ID查询外委单据
     * 
     * @param id 外委单据ID
     * @return 外委单据实体
     */
    OutsourcingOrderEntity findById(String id);

    /**
     * 根据项目计划ID查询外委单据列表
     * 
     * @param planId 项目计划ID
     * @return 外委单据列表
     */
    List<OutsourcingOrderEntity> findByPlanId(String planId);

    /**
     * 根据项目计划ID和产品名称查询已转量
     * 只统计审批通过的外委单据
     * 
     * @param planId 项目计划ID
     * @param productName 产品名称
     * @return 已转量
     */
    BigDecimal getTransferredAmountByPlanIdAndProduct(String planId, String productName);

    /**
     * 根据项目计划ID和物料编码查询已转量
     * 只统计审批通过的外委单据
     * 
     * @param planId 项目计划ID
     * @param materialCode 物料编码
     * @return 已转量
     */
    BigDecimal getTransferredAmountByPlanIdAndMaterial(String planId, String materialCode);

    /**
     * 批量查询已转量
     * 根据项目计划ID和产品名称列表批量查询已转量
     * 
     * @param planId 项目计划ID
     * @param productNames 产品名称列表
     * @return 产品名称与已转量的映射
     */
    java.util.Map<String, BigDecimal> batchGetTransferredAmountByProducts(String planId, List<String> productNames);

    /**
     * 批量查询已转量
     * 根据项目计划ID和物料编码列表批量查询已转量
     * 
     * @param planId 项目计划ID
     * @param materialCodes 物料编码列表
     * @return 物料编码与已转量的映射
     */
    java.util.Map<String, BigDecimal> batchGetTransferredAmountByMaterials(String planId, List<String> materialCodes);

    /**
     * 删除外委单据
     * 
     * @param id 外委单据ID
     * @return 是否删除成功
     */
    boolean deleteById(String id);

    /**
     * 更新外委单据
     * 
     * @param entity 外委单据实体
     * @return 更新后的实体
     */
    OutsourcingOrderEntity updateById(OutsourcingOrderEntity entity);
}
