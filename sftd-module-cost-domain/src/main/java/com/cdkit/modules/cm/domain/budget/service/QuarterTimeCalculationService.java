package com.cdkit.modules.cm.domain.budget.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 季度时间计算领域服务
 * 负责季度选项生成和季度日期范围计算
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
@Slf4j
public class QuarterTimeCalculationService {

    /**
     * 获取季度下拉框选项
     * 返回当年四个季度和下一年第一季度的选项列表（共5个选项）
     *
     * @return 季度选项列表
     */
    public List<String> getQuarterOptions() {
        log.info("开始生成季度下拉框选项");
        
        List<String> quarterOptions = new ArrayList<>();
        int currentYear = LocalDate.now().getYear();
        
        // 当年四个季度
        quarterOptions.add(currentYear + "年第一季度");
        quarterOptions.add(currentYear + "年第二季度");
        quarterOptions.add(currentYear + "年第三季度");
        quarterOptions.add(currentYear + "年第四季度");
        
        // 下一年第一季度
        int nextYear = currentYear + 1;
        quarterOptions.add(nextYear + "年第一季度");
        
        log.info("生成季度下拉框选项成功，共{}个选项：{}", quarterOptions.size(), quarterOptions);
        return quarterOptions;
    }

    /**
     * 根据季度标识计算开始结束时间
     *
     * @param quarter 季度标识（如"2025年第一季度"）
     * @return 季度日期范围对象，包含开始日期和结束日期
     */
    public QuarterDateRange calculateQuarterDateRange(String quarter) {
        log.info("开始计算季度日期范围，季度标识: {}", quarter);
        
        if (quarter == null || quarter.trim().isEmpty()) {
            throw new IllegalArgumentException("季度标识不能为空");
        }
        
        // 解析季度标识，格式：YYYY年第X季度
        String[] parts = quarter.replace("年第", "|").replace("季度", "").split("\\|");
        if (parts.length != 2) {
            throw new IllegalArgumentException("季度标识格式不正确，应为：YYYY年第X季度");
        }
        
        int year;
        int quarterNum;
        try {
            year = Integer.parseInt(parts[0]);
            quarterNum = parseQuarterNumber(parts[1]);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("季度标识格式不正确，年份或季度编号无法解析");
        }
        
        // 计算季度的开始和结束日期
        LocalDate startDate = calculateQuarterStartDate(year, quarterNum);
        LocalDate endDate = calculateQuarterEndDate(year, quarterNum);
        
        // 转换为Date类型
        Date startDateAsDate = Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endDateAsDate = Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        
        QuarterDateRange dateRange = new QuarterDateRange(quarter, startDateAsDate, endDateAsDate);
        
        log.info("计算季度日期范围成功，季度: {}, 开始日期: {}, 结束日期: {}", 
                quarter, startDate, endDate);
        
        return dateRange;
    }

    /**
     * 解析季度编号
     */
    private int parseQuarterNumber(String quarterStr) {
        switch (quarterStr) {
            case "一":
                return 1;
            case "二":
                return 2;
            case "三":
                return 3;
            case "四":
                return 4;
            default:
                throw new IllegalArgumentException("不支持的季度编号: " + quarterStr);
        }
    }

    /**
     * 计算季度开始日期
     */
    private LocalDate calculateQuarterStartDate(int year, int quarter) {
        switch (quarter) {
            case 1:
                return LocalDate.of(year, 1, 1);  // 第一季度：1月1日
            case 2:
                return LocalDate.of(year, 4, 1);  // 第二季度：4月1日
            case 3:
                return LocalDate.of(year, 7, 1);  // 第三季度：7月1日
            case 4:
                return LocalDate.of(year, 10, 1); // 第四季度：10月1日
            default:
                throw new IllegalArgumentException("不支持的季度编号: " + quarter);
        }
    }

    /**
     * 计算季度结束日期
     */
    private LocalDate calculateQuarterEndDate(int year, int quarter) {
        switch (quarter) {
            case 1:
                return LocalDate.of(year, 3, 31);  // 第一季度：3月31日
            case 2:
                return LocalDate.of(year, 6, 30);  // 第二季度：6月30日
            case 3:
                return LocalDate.of(year, 9, 30);  // 第三季度：9月30日
            case 4:
                return LocalDate.of(year, 12, 31); // 第四季度：12月31日
            default:
                throw new IllegalArgumentException("不支持的季度编号: " + quarter);
        }
    }

    /**
     * 季度日期范围内部类
     */
    public static class QuarterDateRange {
        private final String quarter;
        private final Date startDate;
        private final Date endDate;

        public QuarterDateRange(String quarter, Date startDate, Date endDate) {
            this.quarter = quarter;
            this.startDate = startDate;
            this.endDate = endDate;
        }

        public String getQuarter() {
            return quarter;
        }

        public Date getStartDate() {
            return startDate;
        }

        public Date getEndDate() {
            return endDate;
        }
    }
}
