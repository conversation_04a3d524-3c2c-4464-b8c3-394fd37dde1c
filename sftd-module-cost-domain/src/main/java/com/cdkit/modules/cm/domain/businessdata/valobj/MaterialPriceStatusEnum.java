package com.cdkit.modules.cm.domain.businessdata.valobj;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 材料单价状态枚举
 * <AUTHOR>
 * @date 2025/07/16
 */
@Getter
@AllArgsConstructor
public enum MaterialPriceStatusEnum {

    /**
     * 待提交
     */
    PENDING_SUBMIT("pending_submit", "待提交"),

    /**
     * 未生效
     */
    NOT_EFFECTIVE("not_effective", "未生效"),

    /**
     * 生效中
     */
    IN_EFFECT("in_effect", "生效中"),

    /**
     * 已失效
     */
    EXPIRED("expired", "已失效");

    /**
     * 状态码
     */
    private final String code;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 根据状态码获取状态名称
     *
     * @param code 状态码
     * @return 状态名称
     */
    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (MaterialPriceStatusEnum status : MaterialPriceStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getName();
            }
        }
        return code;
    }

    /**
     * 根据状态名称获取状态码
     *
     * @param name 状态名称
     * @return 状态码
     */
    public static String getCodeByName(String name) {
        if (name == null) {
            return null;
        }
        for (MaterialPriceStatusEnum status : MaterialPriceStatusEnum.values()) {
            if (status.getName().equals(name)) {
                return status.getCode();
            }
        }
        return name;
    }

    /**
     * 验证状态码是否有效
     *
     * @param code 状态码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        if (code == null) {
            return false;
        }
        for (MaterialPriceStatusEnum status : MaterialPriceStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否可以编辑
     *
     * @param code 状态码
     * @return 是否可以编辑
     */
    public static boolean canEdit(String code) {
        return PENDING_SUBMIT.getCode().equals(code);
    }

    /**
     * 判断是否可以删除
     *
     * @param code 状态码
     * @return 是否可以删除
     */
    public static boolean canDelete(String code) {
        return PENDING_SUBMIT.getCode().equals(code);
    }

    /**
     * 判断是否可以提交
     *
     * @param code 状态码
     * @return 是否可以提交
     */
    public static boolean canSubmit(String code) {
        return PENDING_SUBMIT.getCode().equals(code);
    }
}
