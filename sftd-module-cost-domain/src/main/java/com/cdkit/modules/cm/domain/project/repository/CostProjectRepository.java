package com.cdkit.modules.cm.domain.project.repository;

import com.cdkit.modules.cm.domain.IBaseDomainRepository;
import com.cdkit.modules.cm.domain.project.mode.entity.CostProjectEntity;
import com.cdkit.modules.cm.domain.project.mode.entity.CostProjectWorkloadEntity;

import java.util.List;

/**
 * 市场项目台账仓储层
 * <AUTHOR>
 * @date 2025/07/14
 */
public interface CostProjectRepository extends IBaseDomainRepository<CostProjectEntity, CostProjectEntity> {

    /**
     * 保存主表和子表数据
     *
     * @param costProject 项目主表数据
     * @param workloadList 工作量子表数据
     * @return 保存结果
     */
    boolean saveMain(CostProjectEntity costProject, List<CostProjectWorkloadEntity> workloadList);

    /**
     * 更新主表和子表数据
     *
     * @param costProject 项目主表数据
     * @param workloadList 工作量子表数据
     * @return 更新结果
     */
    boolean updateMain(CostProjectEntity costProject, List<CostProjectWorkloadEntity> workloadList);

    /**
     * 删除主表和子表数据
     *
     * @param id 主表ID
     * @return 删除结果
     */
    boolean deleteMain(String id);

    /**
     * 批量删除主表和子表数据
     *
     * @param ids 主表ID列表
     * @return 删除结果
     */
    boolean deleteBatchMain(List<String> ids);

    /**
     * 根据主表ID查询子表数据
     *
     * @param projectId 项目ID
     * @return 工作量列表
     */
    List<CostProjectWorkloadEntity> queryWorkloadByProjectId(String projectId);

    /**
     * 根据ID查询项目详情（包含子表数据）
     *
     * @param id 项目ID
     * @return 项目详情
     */
    CostProjectEntity queryByIdWithWorkload(String id);

    /**
     * 查询指定年度执行中的项目列表
     *
     * @param projectYear 项目年度
     * @return 执行中的项目列表
     */
    List<CostProjectEntity> queryInExecutionProjectsByYear(String projectYear);
}
