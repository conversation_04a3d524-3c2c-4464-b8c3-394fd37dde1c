package com.cdkit.modules.cm.domain.gateway.plm.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 产品工艺路线VO
 * <AUTHOR>
 * @date 2025/07/18
 */
@Data
public class ProductProcessRouteVO implements Serializable {
    
    /**
     * 工艺路线ID
     */
    private String id;
    
    /**
     * 工序名称
     */
    private String processName;
    
    /**
     * 工序编码
     */
    private String processCode;
    
    /**
     * 工序序号
     */
    private Integer processSort;
    
    /**
     * 工时
     */
    private BigDecimal workHour;
    
    /**
     * 备注
     */
    private String remark;
}
