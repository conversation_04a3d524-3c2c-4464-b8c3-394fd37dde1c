package com.cdkit.modules.cm.domain.budget.repository;

import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.cdkit.modules.cm.domain.budget.model.dto.CenterCostImportDTO;
import com.cdkit.modules.cm.domain.budget.model.dto.CenterCostSubjectSummaryDTO;

import java.util.List;

/**
 * 中心间接成本导入数据仓储接口
 * 定义中心间接成本导入数据的持久化操作
 * 
 * <AUTHOR>
 * @date 2025-08-06
 */
public interface CenterCostImportRepository {

    /**
     * 批量保存中心间接成本导入数据
     * 
     * @param importDataList 导入数据列表
     * @return 保存成功的数量
     */
    int saveBatch(List<CenterCostImportDTO> importDataList);

    /**
     * 根据预算编号删除导入数据
     *
     * @param budgetCode 预算编号
     * @return 删除的数量
     */
    int deleteByBudgetCode(String budgetCode);

    /**
     * 根据预算编号和模版类型删除导入数据
     *
     * @param budgetCode 预算编号
     * @param templateType 模版类型
     * @return 删除的数量
     */
    int deleteByBudgetCodeAndTemplateType(String budgetCode, String templateType);

    /**
     * 根据预算编号查询导入数据
     *
     * @param budgetCode 预算编号
     * @return 导入数据列表
     */
    List<CenterCostImportDTO> findByBudgetCode(String budgetCode);

    /**
     * 根据预算编码和模版类型查询科目汇总数据
     * 按科目编码和名称分组，汇总金额
     *
     * @param budgetCode 预算编码
     * @param templateType 模版类型
     * @return 科目汇总列表
     */
    List<CenterCostSubjectSummaryDTO> querySubjectSummaryByBudgetCodeAndTemplateType(String budgetCode, String templateType);

    /**
     * 复制间接成本导入数据
     * 将原预算编号的导入数据复制一份，并关联到新预算编号
     *
     * @param originalBudgetCode 原预算编号
     * @param newBudgetCode 新预算编号
     * @return 复制成功的数量
     */
    int copyImportDataByBudgetCode(String originalBudgetCode, String newBudgetCode);
}
