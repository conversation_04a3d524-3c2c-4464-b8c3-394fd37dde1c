package com.cdkit.modules.cm.domain.gateway.material.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 物料实体
 * <AUTHOR>
 * @date 2025/07/17
 */
@Data
@Accessors(chain = true)
public class MaterialDetailEntity implements Serializable {

    /**
     *物料编码
     */
    private String materialCode;

    /**
     *物料名称
     */
    private String materialName;

    /**
     *物料类型
     */
    private String materialType;

    /**
     * 单位
     */
    private String basicUnitId;

    /**
     * 备注
     */
    private String remark;
}
