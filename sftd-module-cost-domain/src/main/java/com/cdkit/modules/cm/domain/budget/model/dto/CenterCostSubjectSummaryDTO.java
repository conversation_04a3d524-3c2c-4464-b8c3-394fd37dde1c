package com.cdkit.modules.cm.domain.budget.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 中心间接成本科目汇总数据传输对象（领域层）
 * 用于封装科目汇总查询结果
 * 
 * <AUTHOR>
 * @date 2025-08-06
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CenterCostSubjectSummaryDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**预算科目编码*/
    private String subjectCode;

    /**预算科目名称*/
    private String subjectName;

    /**科目释义*/
    private String subjectDescription;

    /**汇总金额(元)*/
    private BigDecimal totalAmount;

    /**模版类型*/
    private String templateType;
}
