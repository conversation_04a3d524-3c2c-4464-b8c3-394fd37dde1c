package com.cdkit.modules.cm.domain.project.mode.entity;

import com.cdkit.modules.cm.domain.project.valobj.ProjectStageEnum;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * CostProjectEntity 测试类
 * 测试项目阶段判断功能
 * 
 * <AUTHOR>
 * @date 2025/07/15
 */
public class CostProjectEntityTest {

    @Test
    public void testCanEdit_WhenPendingContract_ShouldReturnTrue() {
        // 准备测试数据
        CostProjectEntity project = CostProjectEntity.builder()
                .id("test-id")
                .projectName("测试项目")
                .projectStage(ProjectStageEnum.PENDING_CONTRACT.getCode())
                .build();

        // 执行测试
        boolean canEdit = project.canEdit();

        // 验证结果
        assertTrue(canEdit, "待签合同阶段的项目应该可以编辑");
    }

    @Test
    public void testCanEdit_WhenContractSigned_ShouldReturnTrue() {
        // 准备测试数据
        CostProjectEntity project = CostProjectEntity.builder()
                .id("test-id")
                .projectName("测试项目")
                .projectStage(ProjectStageEnum.CONTRACT_SIGNED.getCode())
                .build();

        // 执行测试
        boolean canEdit = project.canEdit();

        // 验证结果
        assertTrue(canEdit, "已签合同阶段的项目应该可以编辑");
    }

    @Test
    public void testCanEdit_WhenInExecution_ShouldReturnFalse() {
        // 准备测试数据
        CostProjectEntity project = CostProjectEntity.builder()
                .id("test-id")
                .projectName("测试项目")
                .projectStage(ProjectStageEnum.IN_EXECUTION.getCode())
                .build();

        // 执行测试
        boolean canEdit = project.canEdit();

        // 验证结果
        assertFalse(canEdit, "执行中阶段的项目不应该可以编辑");
    }

    @Test
    public void testCanDelete_WhenPendingContract_ShouldReturnTrue() {
        // 准备测试数据
        CostProjectEntity project = CostProjectEntity.builder()
                .id("test-id")
                .projectName("测试项目")
                .projectStage(ProjectStageEnum.PENDING_CONTRACT.getCode())
                .build();

        // 执行测试
        boolean canDelete = project.canDelete();

        // 验证结果
        assertTrue(canDelete, "待签合同阶段的项目应该可以删除");
    }

    @Test
    public void testCanDelete_WhenContractSigned_ShouldReturnTrue() {
        // 准备测试数据
        CostProjectEntity project = CostProjectEntity.builder()
                .id("test-id")
                .projectName("测试项目")
                .projectStage(ProjectStageEnum.CONTRACT_SIGNED.getCode())
                .build();

        // 执行测试
        boolean canDelete = project.canDelete();

        // 验证结果
        assertTrue(canDelete, "已签合同阶段的项目应该可以删除");
    }

    @Test
    public void testCanDelete_WhenInExecution_ShouldReturnFalse() {
        // 准备测试数据
        CostProjectEntity project = CostProjectEntity.builder()
                .id("test-id")
                .projectName("测试项目")
                .projectStage(ProjectStageEnum.IN_EXECUTION.getCode())
                .build();

        // 执行测试
        boolean canDelete = project.canDelete();

        // 验证结果
        assertFalse(canDelete, "执行中阶段的项目不应该可以删除");
    }

    @Test
    public void testCanLinkContract_WhenPendingContract_ShouldReturnTrue() {
        // 准备测试数据
        CostProjectEntity project = CostProjectEntity.builder()
                .id("test-id")
                .projectName("测试项目")
                .projectStage(ProjectStageEnum.PENDING_CONTRACT.getCode())
                .build();

        // 执行测试
        boolean canLinkContract = project.canLinkContract();

        // 验证结果
        assertTrue(canLinkContract, "待签合同阶段的项目应该可以关联合同");
    }

    @Test
    public void testCanLinkContract_WhenContractSigned_ShouldReturnFalse() {
        // 准备测试数据
        CostProjectEntity project = CostProjectEntity.builder()
                .id("test-id")
                .projectName("测试项目")
                .projectStage(ProjectStageEnum.CONTRACT_SIGNED.getCode())
                .build();

        // 执行测试
        boolean canLinkContract = project.canLinkContract();

        // 验证结果
        assertFalse(canLinkContract, "已签合同阶段的项目不应该可以关联合同");
    }

    @Test
    public void testCanExecute_WhenContractSigned_ShouldReturnTrue() {
        // 准备测试数据
        CostProjectEntity project = CostProjectEntity.builder()
                .id("test-id")
                .projectName("测试项目")
                .projectStage(ProjectStageEnum.CONTRACT_SIGNED.getCode())
                .build();

        // 执行测试
        boolean canExecute = project.canExecute();

        // 验证结果
        assertTrue(canExecute, "已签合同阶段的项目应该可以执行");
    }

    @Test
    public void testCanExecute_WhenPendingContract_ShouldReturnFalse() {
        // 准备测试数据
        CostProjectEntity project = CostProjectEntity.builder()
                .id("test-id")
                .projectName("测试项目")
                .projectStage(ProjectStageEnum.PENDING_CONTRACT.getCode())
                .build();

        // 执行测试
        boolean canExecute = project.canExecute();

        // 验证结果
        assertFalse(canExecute, "待签合同阶段的项目不应该可以执行");
    }

    @Test
    public void testIsViewOnly_WhenInExecution_ShouldReturnTrue() {
        // 准备测试数据
        CostProjectEntity project = CostProjectEntity.builder()
                .id("test-id")
                .projectName("测试项目")
                .projectStage(ProjectStageEnum.IN_EXECUTION.getCode())
                .build();

        // 执行测试
        boolean isViewOnly = project.isViewOnly();

        // 验证结果
        assertTrue(isViewOnly, "执行中阶段的项目应该只能查看");
    }

    @Test
    public void testIsViewOnly_WhenClosed_ShouldReturnTrue() {
        // 准备测试数据
        CostProjectEntity project = CostProjectEntity.builder()
                .id("test-id")
                .projectName("测试项目")
                .projectStage(ProjectStageEnum.CLOSED.getCode())
                .build();

        // 执行测试
        boolean isViewOnly = project.isViewOnly();

        // 验证结果
        assertTrue(isViewOnly, "已关闭阶段的项目应该只能查看");
    }

    @Test
    public void testIsViewOnly_WhenPendingContract_ShouldReturnFalse() {
        // 准备测试数据
        CostProjectEntity project = CostProjectEntity.builder()
                .id("test-id")
                .projectName("测试项目")
                .projectStage(ProjectStageEnum.PENDING_CONTRACT.getCode())
                .build();

        // 执行测试
        boolean isViewOnly = project.isViewOnly();

        // 验证结果
        assertFalse(isViewOnly, "待签合同阶段的项目不应该只能查看");
    }

    @Test
    public void testGetProjectStageName_ShouldReturnCorrectName() {
        // 准备测试数据
        CostProjectEntity project = CostProjectEntity.builder()
                .id("test-id")
                .projectName("测试项目")
                .projectStage(ProjectStageEnum.PENDING_CONTRACT.getCode())
                .build();

        // 执行测试
        String stageName = project.getProjectStageName();

        // 验证结果
        assertEquals("待签合同", stageName, "应该返回正确的项目阶段名称");
    }

    @Test
    public void testGetProjectStageName_WhenNullStage_ShouldReturnNull() {
        // 准备测试数据
        CostProjectEntity project = CostProjectEntity.builder()
                .id("test-id")
                .projectName("测试项目")
                .projectStage(null)
                .build();

        // 执行测试
        String stageName = project.getProjectStageName();

        // 验证结果
        assertNull(stageName, "项目阶段为null时应该返回null");
    }
}
