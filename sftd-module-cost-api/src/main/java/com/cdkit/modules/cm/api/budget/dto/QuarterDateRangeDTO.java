package com.cdkit.modules.cm.api.budget.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 季度日期范围DTO
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@Schema(description = "季度日期范围DTO")
public class QuarterDateRangeDTO {

    /**季度标识*/
    @Schema(description = "季度标识（如：2025年第一季度）")
    private String quarter;

    /**开始时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "开始时间")
    private Date startDate;

    /**结束时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "结束时间")
    private Date endDate;
}
