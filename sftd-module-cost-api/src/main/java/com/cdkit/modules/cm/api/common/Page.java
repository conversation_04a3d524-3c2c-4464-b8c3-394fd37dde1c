package com.cdkit.modules.cm.api.common;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description page 对象
 * @createDate 2025-06-11 09:22
 * @since 1.0
 */
public class Page<T> implements Serializable {

    /**
     * 查询数据列表
     */
    protected List<T> records = Collections.emptyList();

    /**
     * 总数
     */
    protected long total = 0;
    /**
     * 每页显示条数，默认 10
     */
    protected long size = 10;

    /**
     * 当前页
     */
    protected long current = 1;


    public Page() {
    }

    public Page(long current, long size, long total, List<T> records) {
        this.total = total;
        this.size = size;
        this.current = current;
        this.records = records;
    }

    public Page(long current, long size, long total) {
        this.total = total;
        this.size = size;
        this.current = current;
    }

    public static <T> Page<T> of(long current, long size) {
        return of(current, size, 0, Collections.emptyList());
    }

    public static <T> Page<T> of(long current, long size, long total) {
        return of(current, size, total, Collections.emptyList());
    }


    public static <T> Page<T> of(long current, long size, long total, List<T> records) {
        return new Page<>(current, size, total, records);
    }

    public List<T> getRecords() {
        return records;
    }

    public void setRecords(List<T> records) {
        this.records = records;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public long getSize() {
        return size;
    }

    public void setSize(long size) {
        this.size = size;
    }

    public long getCurrent() {
        return current;
    }

    public void setCurrent(long current) {
        this.current = current;
    }
}
