package com.cdkit.modules.cm.api.outsourcing.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 转外委确认请求DTO
 * 
 * <AUTHOR>
 * @date 2025-08-13
 */
@Data
@Accessors(chain = true)
@Schema(description = "转外委确认请求DTO")
public class OutsourcingTransferConfirmRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**项目计划ID*/
    @NotBlank(message = "项目计划ID不能为空")
    @Schema(description = "项目计划ID", required = true)
    private String planId;

    /**转外委项目列表*/
    @NotEmpty(message = "转外委项目列表不能为空")
    @Valid
    @Schema(description = "转外委项目列表", required = true)
    private List<OutsourcingTransferItemDTO> items;
}
