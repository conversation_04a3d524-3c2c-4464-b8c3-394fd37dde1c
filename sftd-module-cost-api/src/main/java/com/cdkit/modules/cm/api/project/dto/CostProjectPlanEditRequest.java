package com.cdkit.modules.cm.api.project.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 项目计划编辑请求DTO
 * @Author: cdkit-boot
 * @Date: 2025-07-18
 * @Version: V1.0
 */
@Schema(description = "项目计划编辑请求DTO")
@Data
public class CostProjectPlanEditRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**UUID主键*/
    @NotBlank(message = "ID不能为空")
    @Schema(description = "UUID主键", required = true)
    private String id;

    /**计划名称*/
    @NotBlank(message = "计划名称不能为空")
    @Schema(description = "计划名称", required = true)
    private String planName;

    /**关联父计划id*/
    @Schema(description = "关联父计划id")
    private String parentPlanId;

    /**年度预算ID*/
    @Schema(description = "年度预算ID")
    private String annualBudgetId;

    /**项目类型（market_project市场项目、non_market_project非市场项目）*/
    @Schema(description = "项目类型（market_project市场项目、non_market_project非市场项目）")
    private String projectType;

    /**项目编号*/
    @Schema(description = "项目编号")
    private String projectCode;

    /**项目名称*/
    @NotBlank(message = "项目名称不能为空")
    @Schema(description = "项目名称", required = true)
    private String projectName;

    /**计划类型(年度计划/季度计划 ANNUAL/QUARTERLY)*/
    @Schema(description = "计划类型(年度计划/季度计划 ANNUAL/QUARTERLY)")
    private String planType;

    /**中心*/
    @Schema(description = "中心")
    private String center;

    /**项目组*/
    @Schema(description = "项目组")
    private String projectGroup;

    /**合同模式*/
    @Schema(description = "合同模式")
    private String contractMode;

    /**合同编号*/
    @Schema(description = "合同编号")
    private String contractCode;

    /**合同名称*/
    @Schema(description = "合同名称")
    private String contractName;

    /**合同/预估收入(税后万元)*/
    @Schema(description = "合同/预估收入(税后万元)")
    private BigDecimal contractRevenue;

    /**直接成本小计*/
    @Schema(description = "直接成本小计")
    private BigDecimal directCostTotal;

    /**其他成本小计*/
    @Schema(description = "其他成本小计")
    private BigDecimal otherCostTotal;

    /**税金及附加小计*/
    @Schema(description = "税金及附加小计")
    private BigDecimal taxCostTotal;

    /**成本总计*/
    @Schema(description = "成本总计")
    private BigDecimal costTotal;

    /**项目利润(万元)*/
    @Schema(description = "项目利润(万元)")
    private BigDecimal projectProfit;

    /**利润率(%)*/
    @Schema(description = "利润率(%)")
    private BigDecimal profitMargin;

    /**项目计划明细列表*/
    @Valid
    @Schema(description = "项目计划明细列表")
    private List<CostProjectPlanDetailDTO> detailList;

    /**直接成本明细列表*/
    @Valid
    @Schema(description = "直接成本明细列表")
    private List<CostDirectCostDTO> directCostList;

    /**其他成本明细列表*/
    @Valid
    @Schema(description = "其他成本明细列表")
    private List<CostOtherCostDTO> otherCostList;

    /**税金及附加明细列表*/
    @Valid
    @Schema(description = "税金及附加明细列表")
    private List<CostTaxCostDTO> taxCostList;

    /**原料明细列表*/
    @Valid
    @Schema(description = "原料明细列表")
    private List<CostMaterialDetailDTO> materialDetailList;
}
