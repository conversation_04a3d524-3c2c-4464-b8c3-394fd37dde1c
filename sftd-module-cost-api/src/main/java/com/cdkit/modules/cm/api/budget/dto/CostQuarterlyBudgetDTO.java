package com.cdkit.modules.cm.api.budget.dto;

import com.cdkitframework.poi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 季度预算DTO
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@Schema(description = "季度预算DTO")
public class CostQuarterlyBudgetDTO {

    /**UUID主键*/
    @Schema(description = "UUID主键")
    private String id;

    /**季度预算单号(JDYS+8位日期+3位流水)*/
    @Schema(description = "季度预算单号(JDYS+8位日期+3位流水)")
    private String quarterlyBudgetNo;

    /**季度预算编码(融合服务平台回传)*/
    @Schema(description = "季度预算编码(融合服务平台回传)")
    private String quarterlyBudgetCode;

    /**季度预算名称*/
    @Schema(description = "季度预算名称")
    private String quarterlyBudgetName;

    /**版本*/
    @Schema(description = "版本")
    private String version;

    /**状态(PENDING_LOCK-待锁定/APPROVING-审批中/LOCKED-已锁定/REJECTED-已驳回/CHANGED-已变更)*/
    @Schema(description = "状态(PENDING_LOCK-待锁定/APPROVING-审批中/LOCKED-已锁定/REJECTED-已驳回/CHANGED-已变更)")
    private String budgetStatus;

    /**关联年度预算ID*/
    @Schema(description = "关联年度预算ID")
    private String annualBudgetId;

    /**关联年度预算编号*/
    @Schema(description = "关联年度预算编号")
    private String annualBudgetCode;

    /**关联季度计划ID*/
    @Schema(description = "关联季度计划ID")
    private String quarterlyPlanId;

    /**关联季度计划编号*/
    @Schema(description = "关联季度计划编号")
    private String quarterlyPlanCode;

    /**所属单位*/
    @Schema(description = "所属单位")
    private String professionalCompany;

    /**季度(如：2025年第一季度)*/
    @Excel(name = "季度(如：2025年第一季度)", width = 15)
    @Schema(description = "季度(如：2025年第一季度)")
    private String quarter;

    /**开始时间*/
    @Excel(name = "开始时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "开始时间")
    private Date startDate;
    /**结束时间*/
    @Excel(name = "结束时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "结束时间")
    private Date endDate;


    /**项目经理*/
    @Excel(name = "项目经理", width = 15)
    @Schema(description = "项目经理")
    private String projectManagerName;

    /**是否涉及多年预算(Y-是/N-否)*/
    @Excel(name = "是否涉及多年预算(Y-是/N-否)", width = 15)
    @Schema(description = "是否涉及多年预算(Y-是/N-否)")
    private String isMultiYearBudget;

    /**市场项目ID*/
    @Schema(description = "市场项目ID")
    private String marketProjectId;

    /**市场项目编号*/
    @Schema(description = "市场项目编号")
    private String marketProjectCode;

    /**市场项目名称*/
    @Schema(description = "市场项目名称")
    private String marketProjectName;

    /**年度收入剩余预算金额（元）*/
    @Schema(description = "年度收入剩余预算金额（元）")
    private BigDecimal annualRevenueRemainingBudgetAmount;

    /**项目收入预算总额（元）*/
    @Schema(description = "项目收入预算总额（元）")
    private BigDecimal projectRevenueBudgetTotalAmount;

    /**年度支出剩余预算金额（元）*/
    @Schema(description = "年度支出剩余预算金额（元）")
    private BigDecimal annualExpenditureRemainingBudgetAmount;

    /**项目支出预算总额（元）*/
    @Schema(description = "项目支出预算总额（元）")
    private BigDecimal projectExpenditureBudgetTotalAmount;

    /**间接费预算总额（元）*/
    @Schema(description = "间接费预算总额（元）")
    private BigDecimal indirectCostBudgetTotalAmount;

    /**项目边际利润（元）*/
    @Schema(description = "项目边际利润（元）")
    private BigDecimal projectMarginalProfit;

    /**项目边际利润率*/
    @Schema(description = "项目边际利润率")
    private BigDecimal projectMarginalProfitRate;

    /**项目净利润（元）*/
    @Schema(description = "项目净利润（元）")
    private BigDecimal projectNetProfit;

    /**项目净利润率*/
    @Schema(description = "项目净利润率")
    private BigDecimal projectNetProfitRate;

    /**工作流实例ID*/
    @Schema(description = "工作流实例ID")
    private String wiid;

    /**备注*/
    @Schema(description = "备注")
    private String remark;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;

    /**创建人*/
    @Schema(description = "创建人")
    private String createBy;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;

    /**更新人*/
    @Schema(description = "更新人")
    private String updateBy;

    /**租户ID*/
    @Schema(description = "租户ID")
    private String tenantId;
}
