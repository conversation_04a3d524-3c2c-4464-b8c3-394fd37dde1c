package com.cdkit.modules.cm.api.businessdata;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cdkit.common.api.vo.Result;
import com.cdkit.modules.cm.api.businessdata.dto.CostBudgetSubjectDTO;
import com.cdkit.modules.cm.api.businessdata.dto.CostBudgetSubjectAddRequest;
import com.cdkit.modules.cm.api.businessdata.dto.CostBudgetSubjectEditRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

/**
 * 预算科目管理API接口
 * <AUTHOR>
 * @date 2025-07-31
 */
@Tag(name = "预算科目管理")
public interface IBudgetSubjectApi {

    /**
     * 分页查询预算科目列表
     *
     * @param queryVO 查询条件
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @return 分页结果
     */
    @Operation(summary = "预算科目-分页列表查询")
    @GetMapping("/list")
    Result<IPage<CostBudgetSubjectDTO>> queryPageList(
            CostBudgetSubjectDTO queryVO,
            @Parameter(description = "页码", example = "1") @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @Parameter(description = "每页数量", example = "10") @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize
    );

    /**
     * 根据ID查询预算科目详情
     *
     * @param id 预算科目ID
     * @return 预算科目详情
     */
    @Operation(summary = "根据ID查询预算科目详情")
    @GetMapping("/queryById")
    Result<CostBudgetSubjectDTO> queryById(@Parameter(description = "预算科目ID", required = true) @RequestParam String id);

    /**
     * 新增预算科目
     *
     * @param costBudgetSubject 预算科目数据
     * @return 操作结果
     */
    @Operation(summary = "新增预算科目")
    @PostMapping("/add")
    Result<String> add(@RequestBody CostBudgetSubjectAddRequest costBudgetSubject);

    /**
     * 编辑预算科目
     *
     * @param costBudgetSubject 预算科目数据
     * @return 操作结果
     */
    @Operation(summary = "编辑预算科目")
    @PutMapping("/edit")
    Result<String> edit(@RequestBody CostBudgetSubjectEditRequest costBudgetSubject);

    /**
     * 根据ID删除预算科目
     *
     * @param id 预算科目ID
     * @return 操作结果
     */
    @Operation(summary = "根据ID删除预算科目")
    @DeleteMapping("/delete")
    Result<String> delete(@Parameter(description = "预算科目ID", required = true) @RequestParam String id);

    /**
     * 批量删除预算科目
     *
     * @param ids 预算科目ID列表，逗号分隔
     * @return 操作结果
     */
    @Operation(summary = "批量删除预算科目")
    @DeleteMapping("/deleteBatch")
    Result<String> deleteBatch(@Parameter(description = "预算科目ID列表，逗号分隔", required = true) @RequestParam String ids);

    /**
     * 查询所有启用状态的预算科目
     *
     * @return 预算科目列表
     */
    @Operation(summary = "查询所有启用状态的预算科目")
    @GetMapping("/listEnabled")
    Result<java.util.List<CostBudgetSubjectDTO>> listEnabled();
}
