package com.cdkit.modules.cm.api.budget.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 中心间接成本科目汇总DTO
 * 用于第二步列表接口返回科目汇总数据
 * 
 * <AUTHOR>
 * @date 2025-08-06
 */
@Schema(description = "中心间接成本科目汇总DTO")
@Data
public class CenterCostSubjectSummaryDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**预算科目编码*/
    @Schema(description = "预算科目编码")
    private String subjectCode;

    /**预算科目名称*/
    @Schema(description = "预算科目名称")
    private String subjectName;

    /**科目释义*/
    @Schema(description = "科目释义")
    private String subjectDescription;

    /**汇总金额(元)*/
    @Schema(description = "汇总金额(元)")
    private BigDecimal totalAmount;

    /**模版类型*/
    @Schema(description = "模版类型")
    private String templateType;
}
