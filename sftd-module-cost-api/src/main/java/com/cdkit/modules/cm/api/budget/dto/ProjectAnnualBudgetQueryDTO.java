package com.cdkit.modules.cm.api.budget.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.cdkitframework.poi.excel.annotation.Excel;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 项目年度预算查询响应DTO
 * <AUTHOR>
 * @date 2025-08-12
 */
@Schema(description = "项目年度预算查询响应DTO")
@Data
public class ProjectAnnualBudgetQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**年度预算编码*/
    @Excel(name = "年度预算编码", width = 15)
    @Schema(description = "年度预算编码")
    private String annualBudgetCode;

    /**WBS编号*/
    @Excel(name = "WBS编号", width = 15)
    @Schema(description = "WBS编号")
    private String wbsCode;

    /**所属单位*/
    @Excel(name = "所属单位", width = 15)
    @Schema(description = "所属单位")
    private String professionalCompany;

    /**下属中心*/
    @Excel(name = "下属中心", width = 15)
    @Schema(description = "下属中心")
    private String center;

    /**项目名称*/
    @Excel(name = "项目名称", width = 15)
    @Schema(description = "项目名称")
    private String projectName;

    /**预算类型*/
    @Excel(name = "预算类型", width = 15)
    @Schema(description = "预算类型")
    private String budgetType;

    /**年度收入预算金额（不含税，元）*/
    @Excel(name = "年度收入预算金额（不含税，元）", width = 15)
    @Schema(description = "年度收入预算金额（不含税，元）")
    private BigDecimal annualRevenueBudget;

    /**年度支出预算金额（不含税，元）*/
    @Excel(name = "年度支出预算金额（不含税，元）", width = 15)
    @Schema(description = "年度支出预算金额（不含税，元）")
    private BigDecimal annualExpenditureBudget;
}
