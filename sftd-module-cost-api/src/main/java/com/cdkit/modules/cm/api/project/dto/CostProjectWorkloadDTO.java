package com.cdkit.modules.cm.api.project.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.cdkitframework.poi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 工作量动态DTO
 * @Author: cdkit-boot
 * @Date:   2025-07-14
 * @Version: V1.0
 */
@Schema(description="工作量动态DTO")
@Data
public class CostProjectWorkloadDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键ID*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private String id;

    /**关联项目ID*/
    @Schema(description = "关联项目ID")
    private String projectId;

    /**月份 (1-12)*/
    @Excel(name = "月份 (1-12)", width = 15)
    @Schema(description = "月份 (1-12)")
    private Integer month;

    /**预计工作量（元）*/
    @Excel(name = "预计工作量（元）", width = 15)
    @Schema(description = "预计工作量（元）")
    private BigDecimal estimatedWorkload;

    /**实际工作量（元）*/
    @Excel(name = "实际工作量（元）", width = 15)
    @Schema(description = "实际工作量（元）")
    private BigDecimal actualWorkload;

    /**当期预估收入（元）*/
    @Excel(name = "当期预估收入（元）", width = 15)
    @Schema(description = "当期预估收入（元）")
    private BigDecimal estimatedRevenue;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;

    /**创建人*/
    @Schema(description = "创建人")
    private String createBy;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;

    /**更新人*/
    @Schema(description = "更新人")
    private String updateBy;

    /**租户ID*/

    @Schema(description = "租户ID")
    private Integer tenantId;

    /**删除标识 0:未删除 1:删除*/

    @Schema(description = "删除标识 0:未删除 1:删除")
    @TableLogic
    private Integer delFlag;
}
