package com.cdkit.modules.cm.api.project.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 税金及附加明细DTO
 * @Author: cdkit-boot
 * @Date: 2025-07-18
 * @Version: V1.0
 */
@Schema(description = "税金及附加明细DTO")
@Data
public class CostTaxCostDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**UUID主键*/
    @Schema(description = "UUID主键")
    private String id;

    /**关联计划ID*/
    @Schema(description = "关联计划ID")
    private String planId;

    /**成本类目*/
    @Schema(description = "成本类目")
    private String costCategory;

    /**成本说明*/
    @Schema(description = "成本说明")
    private String costDescription;

    /**费用金额(万元)*/
    @Schema(description = "费用金额(万元)")
    private BigDecimal feeAmount;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;

    /**创建人*/
    @Schema(description = "创建人")
    private String createBy;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;

    /**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
}
