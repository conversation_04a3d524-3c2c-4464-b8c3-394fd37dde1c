package com.cdkit.modules.cm.api.project.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 关联合同请求DTO
 * <AUTHOR>
 * @date 2025/07/15
 */
@Schema(description="关联合同请求DTO")
@Data
public class LinkContractRequestDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**项目ID*/
    @Schema(description = "项目ID", required = true)
    private String projectId;

    /**合同编号*/
    @Schema(description = "合同编号")
    private String contractCode;

    /**合同名称*/
    @Schema(description = "合同名称")
    private String contractName;

    /**合同模式*/
    @Schema(description = "合同模式")
    private String contractMode;

    /**合同下发日期*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "合同下发日期")
    private Date contractIssueDate;

    /**合同结束日期*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "合同结束日期")
    private Date contractEndDate;
}
