package com.cdkit.modules.cm.api.procurement.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.cdkit.common.aspect.annotation.Dict;
import com.cdkitframework.poi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 采购订单台账
 * @Author: sunhzh
 * @Date:   2025-07-09
 */
@Schema(name = "采购订单台账")
@Getter
@Setter
public class CostPurchaseOrderLedgerDTO implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键ID*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private String id;
	/**签订时间*/
	@Excel(name = "签订时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "签订时间")
    private Date signTime;
	/**计划交货日期/验收日期*/
	@Excel(name = "计划交货日期/验收日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "计划交货日期/验收日期")
    private Date plannedDeliveryDate;
	/**订单类型*/
	@Excel(name = "订单类型", width = 15, dicCode = "cost_order_type")
	@Dict(dicCode = "cost_order_type")
    @Schema(description = "订单类型")
    private String orderType;
	/**需求编号*/
	@Excel(name = "需求编号", width = 15)
    @Schema(description = "需求编号")
    private String demandNumber;
	/**立项号*/
	@Excel(name = "立项号", width = 15)
    @Schema(description = "立项号")
    private String projectNumber;
	/**PR号*/
	@Excel(name = "PR号", width = 15)
    @Schema(description = "PR号")
    private String prNumber;
	/**订单名称*/
	@Excel(name = "订单名称", width = 15)
    @Schema(description = "订单名称")
    private String orderName;
	/**立项名称*/
	@Excel(name = "立项名称", width = 15)
    @Schema(description = "立项名称")
    private String projectName;
	/**原材料/服务名称*/
	@Excel(name = "原材料/服务名称", width = 15)
    @Schema(description = "原材料/服务名称")
    private String materialName;
	/**包装要求/规格*/
	@Excel(name = "包装要求/规格", width = 15)
    @Schema(description = "包装要求/规格")
    private String specification;
	/**物资编码*/
	@Excel(name = "物资编码", width = 15)
    @Schema(description = "物资编码")
    private String materialCode;
	/**单位*/
	@Excel(name = "单位", width = 15)
    @Schema(description = "单位")
    private String unit;
	/**数量*/
	@Excel(name = "数量", width = 15)
    @Schema(description = "数量")
    private BigDecimal quantity;
	/**预算单价*/
	@Excel(name = "预算单价", width = 15)
    @Schema(description = "预算单价")
    private BigDecimal budgetUnitPrice;
	/**预算总价*/
	@Excel(name = "预算总价", width = 15)
    @Schema(description = "预算总价")
    private BigDecimal budgetTotalPrice;
	/**协议单价*/
	@Excel(name = "协议单价", width = 15)
    @Schema(description = "协议单价")
    private BigDecimal agreementUnitPrice;
	/**协议总价*/
	@Excel(name = "协议总价", width = 15)
    @Schema(description = "协议总价")
    private BigDecimal agreementTotalPrice;
	/**订单单价*/
	@Excel(name = "订单单价", width = 15)
    @Schema(description = "订单单价")
    private BigDecimal orderUnitPrice;
	/**订单不含税单价*/
	@Excel(name = "订单不含税单价", width = 15)
    @Schema(description = "订单不含税单价")
    private BigDecimal orderExcludingTaxPrice;
	/**订单金额*/
	@Excel(name = "订单金额", width = 15)
    @Schema(description = "订单金额")
    private BigDecimal orderAmount;
	/**匹配协议编号*/
	@Excel(name = "匹配协议编号", width = 15)
    @Schema(description = "匹配协议编号")
    private String matchedAgreementNumber;
	/**协议类型*/
	@Excel(name = "协议类型", width = 15)
    @Schema(description = "协议类型")
    private String agreementType;
	/**订单号*/
	@Excel(name = "订单号", width = 15)
    @Schema(description = "订单号")
    private String orderNumber;
	/**SAP订单号*/
	@Excel(name = "SAP订单号", width = 15)
    @Schema(description = "SAP订单号")
    private String sapOrderNumber;
	/**供应商*/
	@Excel(name = "供应商", width = 15)
    @Schema(description = "供应商")
    private String supplier;
	/**执行区域*/
	@Excel(name = "执行区域", width = 15)
    @Schema(description = "执行区域")
    private String executionRegion;
	/**申请单位*/
	@Excel(name = "申请单位", width = 15)
    @Schema(description = "申请单位")
    private String applicationUnit;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;
	/**创建人*/
    @Schema(description = "创建人")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username", ds = "master")
    private String createBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;
	/**更新人*/
    @Schema(description = "更新人")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username", ds = "master")
    private String updateBy;
	/**租户id*/
	@Excel(name = "租户id", width = 15)
    @Schema(description = "租户id")
    private Integer tenantId;
	/**删除标识 0:未删除 1:删除*/
	@Excel(name = "删除标识 0:未删除 1:删除", width = 15)
    @Schema(description = "删除标识 0:未删除 1:删除")
    @TableLogic
    private Integer delFlag;
	/**所属部门*/
    @Schema(description = "所属部门")
    private String sysOrgCode;
}
