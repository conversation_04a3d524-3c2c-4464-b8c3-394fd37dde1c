package com.cdkit.modules.cm.api.budget.converter;

import com.cdkit.modules.cm.api.budget.dto.ProjectAnnualBudgetQueryDTO;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 项目年度预算查询转换器
 * 负责API层DTO的创建和转换
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
public class ProjectAnnualBudgetQueryConverter {

    /**
     * 创建项目年度预算查询DTO
     *
     * @param annualBudgetCode 年度预算编码
     * @param wbsCode WBS编号
     * @param professionalCompany 所属单位
     * @param center 下属中心
     * @param projectName 项目名称
     * @param budgetType 预算类型
     * @param annualRevenueBudget 年度收入预算金额（不含税，元）
     * @param annualExpenditureBudget 年度支出预算金额（不含税，元）
     * @return API层DTO
     */
    public static ProjectAnnualBudgetQueryDTO createDTO(String annualBudgetCode, String wbsCode,
                                                       String professionalCompany, String center,
                                                       String projectName, String budgetType,
                                                       BigDecimal annualRevenueBudget, BigDecimal annualExpenditureBudget) {
        ProjectAnnualBudgetQueryDTO dto = new ProjectAnnualBudgetQueryDTO();
        dto.setAnnualBudgetCode(annualBudgetCode);
        dto.setWbsCode(wbsCode);
        dto.setProfessionalCompany(professionalCompany);
        dto.setCenter(center);
        dto.setProjectName(projectName);
        dto.setBudgetType(budgetType);
        dto.setAnnualRevenueBudget(annualRevenueBudget);
        dto.setAnnualExpenditureBudget(annualExpenditureBudget);
        return dto;
    }
}
