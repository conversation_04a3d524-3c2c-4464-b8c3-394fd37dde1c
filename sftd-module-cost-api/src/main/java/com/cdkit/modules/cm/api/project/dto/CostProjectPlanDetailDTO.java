package com.cdkit.modules.cm.api.project.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 项目计划明细DTO
 * @Author: cdkit-boot
 * @Date: 2025-07-18
 * @Version: V1.0
 */
@Schema(description = "项目计划明细DTO")
@Data
public class CostProjectPlanDetailDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**UUID主键*/
    @Schema(description = "UUID主键")
    private String id;

    /**关联计划ID*/
    @Schema(description = "关联计划ID")
    private String planId;

    /**区块*/
    @Schema(description = "区块")
    private String block;

    /**平台设施*/
    @Schema(description = "平台设施")
    private String platformFacility;

    /**物料编码*/
    @Schema(description = "物料编码")
    private String materialCode;
    /**物料名称*/
    @Schema(description = "物料名称")
    private String materialName;

    /**密度*/
    @Schema(description = "密度")
    private BigDecimal density;

    /**用量(吨)*/
    @Schema(description = "用量(吨)")
    private BigDecimal usageAmount;

    /**预计年处理量(油，方)*/
    @Schema(description = "预计年处理量(油，方)")
    private BigDecimal estimatedAnnualOil;

    /**预计年处理量(水，方)*/
    @Schema(description = "预计年处理量(水，方)")
    private BigDecimal estimatedAnnualWater;

    /**收费费率(元/方)*/
    @Schema(description = "收费费率(元/方)")
    private BigDecimal feeRate;

    /**年度预算应收(油，万元)*/
    @Schema(description = "年度预算应收(油，万元)")
    private BigDecimal revenueOil;

    /**年度预算应收(水，万元)*/
    @Schema(description = "年度预算应收(水，万元)")
    private BigDecimal revenueWater;

    /**年度预算需求吨(吨)*/
    @Schema(description = "年度预算需求吨(吨)")
    private BigDecimal demandTon;


    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;

    /**创建人*/
    @Schema(description = "创建人")
    private String createBy;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;

    /**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
}
