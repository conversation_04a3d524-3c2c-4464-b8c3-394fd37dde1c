package com.cdkit.modules.cm.api.outsourcing.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 转外委列表响应DTO
 * 只返回转外委项目明细列表
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@Data
@Accessors(chain = true)
@Schema(description = "转外委列表响应DTO")
public class OutsourcingTransferListDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**转外委项目列表（产品作为主表，半成品作为子表）*/
    @Schema(description = "转外委项目列表")
    private List<OutsourcingTransferItemDTO> items;
}
