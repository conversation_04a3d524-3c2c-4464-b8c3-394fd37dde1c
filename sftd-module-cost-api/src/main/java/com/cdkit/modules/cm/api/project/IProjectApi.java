package com.cdkit.modules.cm.api.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cdkit.common.api.vo.Result;
import com.cdkit.modules.cm.api.project.dto.CostProjectDTO;
import com.cdkit.modules.cm.api.project.dto.CostProjectWorkloadDTO;
import com.cdkit.modules.cm.api.project.dto.LinkContractRequestDTO;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;
import java.util.Map;

/**
 * 市场项目管理api
 * <AUTHOR>
 * @date 2025/07/14
 */
public interface IProjectApi {

    /**
     * 分页列表查询
     */
    @GetMapping(value = "/list")
    Result<IPage<CostProjectDTO>> queryPageList(CostProjectDTO queryVO,
                                                @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize);

    /**
     * 添加
     */
    @PostMapping(value = "/add")
    Result<String> add(@RequestBody CostProjectDTO costProject);

    /**
     * 编辑
     */
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    Result<String> edit(@RequestBody CostProjectDTO costProject);

    /**
     * 通过id删除
     */
    @DeleteMapping(value = "/delete")
    Result<String> delete(@RequestParam(name = "id", required = true) String id);

    /**
     * 批量删除
     */
    @DeleteMapping(value = "/deleteBatch")
    Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids);

    /**
     * 通过id查询
     */
    @GetMapping(value = "/queryById")
    Result<CostProjectDTO> queryById(@RequestParam(name = "id", required = true) String id);

    /**
     * 通过id查询子表数据
     */
    @GetMapping(value = "/queryWorkloadByMainId")
    Result<List<CostProjectWorkloadDTO>> queryWorkloadByMainId(@RequestParam(name = "id", required = true) String id);

    /**
     * 导出Excel
     * @param costProjectDTO 参数
     * @return {@link ModelAndView }
     */
    @RequestMapping(value = "/exportXlsTemplate")
    ModelAndView exportXlsTemplate(CostProjectDTO costProjectDTO);

    /**
     * 导出Excel
     * @param costProjectDTO 参数
     * @return {@link ModelAndView }
     */
    @RequestMapping(value = "/exportXls")
    ModelAndView exportXls(CostProjectDTO costProjectDTO);

    /**
     * 导入Excel
     * @param request
     * @param response
     * @return {@link Result }<{@link ? }>
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    Result<?> importExcel(HttpServletRequest request, HttpServletResponse response);

    /**
     * 关联合同
     * @param request 关联合同请求参数
     * @return {@link Result}<{@link String}>
     */
    @PostMapping(value = "/linkContract")
    Result<String> linkContract(@RequestBody LinkContractRequestDTO request);

    /**
     * 执行项目
     * 将已签合同阶段的项目更新为执行中阶段
     * @param projectId 项目ID
     * @return {@link Result}<{@link String}>
     */
    @PostMapping(value = "/executeProject")
    Result<String> executeProject(@RequestParam(name = "projectId", required = true) String projectId);
}
