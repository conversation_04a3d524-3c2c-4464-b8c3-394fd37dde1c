package com.cdkit.modules.cm.api.outsourcing.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import com.cdkitframework.poi.excel.annotation.Excel;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 转外委项目DTO
 * 支持产品-半成品的主子表结构
 * - 产品作为主表记录（isProduct=true）
 * - 半成品作为子表记录（isProduct=false），存储在children列表中
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@Data
@Accessors(chain = true)
@Schema(description = "转外委项目DTO")
public class OutsourcingTransferItemDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**序号（仅主表产品使用）*/
    @Schema(description = "序号")
    private Integer sequence;

    /**物料编码*/
    @Excel(name = "物料编码", width = 15)
    @Schema(description = "物料编码")
    private String materialCode;
    /**物料名称*/
    @Schema(description = "物料名称")
    private String materialName;

    /**对应外委产品*/
    @Schema(description = "对应外委产品")
    private String outsourcingProduct;

    /**物料名称*/
    @Schema(description = "对应外委产品物料名称")
    private String outsourcingProductName;

    /**预计用量（吨）*/
    @Schema(description = "预计用量（吨）")
    private BigDecimal estimatedUsage;

    /**配方名称*/
    @Schema(description = "配方名称")
    private String formulaName;

    /**配方编码*/
    @Schema(description = "配方编码")
    private String formulaCode;

    /**已转量（吨）*/
    @Schema(description = "已转量（吨）")
    private BigDecimal transferredAmount;

    /**可转量（吨）*/
    @Schema(description = "可转量（吨）")
    private BigDecimal availableAmount;

    /**外委量（吨）*/
    @Schema(description = "外委量（吨）")
    private BigDecimal outsourcingAmount;

    /**是否为产品（true：产品，false：半成品）*/
    @Schema(description = "是否为产品")
    private Boolean isProduct;

    /**父级物料编码（半成品时使用）*/
    @Schema(description = "父级物料编码")
    private String parentMaterialCode;

    /**配方比例（半成品相对于产品的比例）*/
    @Schema(description = "配方比例")
    private BigDecimal recipeRatio;

    /**是否展开（用于前端控制子母表展开状态）*/
    @Schema(description = "是否展开")
    private Boolean expanded;

    /**子项目列表（产品对应的半成品列表，仅包含partType=SEMI类型的半成品）*/
    @Schema(description = "子项目列表（半成品列表，partType=SEMI）")
    private List<OutsourcingTransferItemDTO> children;

    /**直接成本ID（用于关联原始数据）*/
    @Schema(description = "直接成本ID")
    private String directCostId;

    /**项目计划ID*/
    @Schema(description = "项目计划ID")
    private String planId;

    /**计划编号*/
    @Schema(description = "计划编号(JH+8位日期+3位流水)")
    private String planCode;

    /**物料类型*/
    @Schema(description = "物料类型")
    private String itemType;
}
