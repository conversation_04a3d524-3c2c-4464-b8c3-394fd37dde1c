package com.cdkit.modules.cm.api.project.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.cdkit.common.aspect.annotation.Dict;
import com.cdkitframework.poi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: 市场项目台账DTO
 * @Author: sunhzh
 * @Date:   2025-07-14
 * @Version: V1.0
 */
@Schema(description="市场项目台账DTO")
@Data
public class CostProjectDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键ID*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private String id;

    /**项目年度*/
    @Excel(name = "项目年度", width = 15)
    @Schema(description = "项目年度")
    private String projectYear;

    /**项目编号*/
    @Excel(name = "项目编号", width = 15)
    @Schema(description = "项目编号")
    private String projectCode;

    /**项目名称*/
    @Excel(name = "项目名称", width = 15)
    @Schema(description = "项目名称")
    private String projectName;

    /**项目状态：risk(风险), for_development(待开发), routine(常规)*/
    @Excel(name = "项目状态", width = 15, dicCode = "cost_project_status")
    @Schema(description = "项目状态：risk(风险), for_development(待开发), routine(常规)")
    @Dict(dicCode = "cost_project_status")
    private String projectStatus;

    /**是否新市场*/
    @Excel(name = "是否新市场", width = 15, replace = {"是_Y", "否_N"})
    @Schema(description = "是否新市场")
    private String isNewMarket;

    /**是否新业务*/
    @Excel(name = "是否新业务", width = 15, replace = {"是_Y", "否_N"})
    @Schema(description = "是否新业务")
    private String isNewBusiness;

    /**专业公司*/
    @Excel(name = "专业公司", width = 15)
    @Schema(description = "专业公司")
    private String professionalCompany;

    /**项目阶段：pending_contract(待签合同), contract_signed(已签合同), in_execution(执行中), closed(已关闭)*/
    @Excel(name = "项目阶段", width = 15, dicCode = "cost_project_stage")
    @Schema(description = "项目阶段：pending_contract(待签合同), contract_signed(已签合同), in_execution(执行中), closed(已关闭)")
    @Dict(dicCode = "cost_project_stage")
    private String projectStage;

    /**中心*/
    @Excel(name = "中心", width = 15)
    @Schema(description = "中心")
    private String projectCenter;

    /**项目组*/
    @Excel(name = "项目组", width = 15)
    @Schema(description = "项目组")
    private String projectGroup;

    /**项目负责*/
    @Excel(name = "项目负责", width = 15)
    @Schema(description = "项目负责")
    private String projectLeader;

    /**是否续签项目*/
    @Excel(name = "是否续签项目", width = 15, replace = {"是_Y", "否_N"})
    @Schema(description = "是否续签项目")
    private String isRenewalProject;

    /**项目经理*/
    @Excel(name = "项目经理", width = 15)
    @Schema(description = "项目经理")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username", ds = "master")
    private String projectManager;

    /**客户名称*/
    @Excel(name = "客户名称", width = 15)
    @Schema(description = "客户名称")
    private String customerName;

    /**委托部门*/
    @Excel(name = "委托部门", width = 15)
    @Schema(description = "委托部门")
    private String entrustDepartment;

    /**是否上游业务*/
    @Excel(name = "是否上游业务", width = 15, replace = {"是_Y", "否_N"})
    @Schema(description = "是否上游业务")
    private String isUpstreamBusiness;

    /**上游业务类型*/
    @Excel(name = "上游业务类型", width = 15)
    @Schema(description = "上游业务类型")
    private String upstreamBusinessType;

    /**合同编号*/
    @Excel(name = "合同编号", width = 15)
    @Schema(description = "合同编号")
    private String contractCode;

    /**合同名称*/
    @Excel(name = "合同名称", width = 15)
    @Schema(description = "合同名称")
    private String contractName;

    /**合同模式*/
    @Excel(name = "合同模式", width = 15,dicCode = "cost_contact_mode")
    @Schema(description = "合同模式")
    @Dict(dicCode = "cost_contact_mode")
    private String contractMode;

    /**合同下发日期*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "合同下发日期", width = 15, format = "yyyy-MM-dd")
    @Schema(description = "合同下发日期")
    private Date contractIssueDate;

    /**合同结束日期*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "合同结束日期", width = 15, format = "yyyy-MM-dd")
    @Schema(description = "合同结束日期")
    private Date contractEndDate;

    /**项目预计收入（元，税后）*/
    @Excel(name = "项目预计收入（元，税后）", width = 15)
    @Schema(description = "项目预计收入（元，税后）")
    private BigDecimal estimatedRevenue;

    /**项目预计成本（元，税后）*/
    @Excel(name = "项目预计成本（元，税后）", width = 15)
    @Schema(description = "项目预计成本（元，税后）")
    private BigDecimal estimatedCost;

    /**存在经营性外包*/
    @Excel(name = "存在经营性外包", width = 15, replace = {"是_Y", "否_N"})
    @Schema(description = "存在经营性外包")
    private String hasOutsourcing;

    /**细分业务*/
    @Excel(name = "细分业务", width = 15)
    @Schema(description = "细分业务")
    private String segmentedBusiness;

    /**一级业务*/
    @Excel(name = "一级业务", width = 15)
    @Schema(description = "一级业务")
    private String firstLevelBusiness;

    /**二级业务*/
    @Excel(name = "二级业务", width = 15)
    @Schema(description = "二级业务")
    private String secondLevelBusiness;

    /**三级业务*/
    @Excel(name = "三级业务", width = 15)
    @Schema(description = "三级业务")
    private String thirdLevelBusiness;

    /**业务所属区域*/
    @Excel(name = "业务所属区域", width = 15)
    @Schema(description = "业务所属区域")
    private String businessArea;

    /**执行部门*/
    @Excel(name = "执行部门", width = 15)
    @Schema(description = "执行部门")
    private String execDepartment;

    /**执行单位*/
    @Excel(name = "执行单位", width = 15)
    @Schema(description = "执行单位")
    private String execUnit;

    /**项目附件*/
    @Excel(name = "项目附件", width = 15)
    @Schema(description = "项目附件")
    private String projectAttachment;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;

    /**创建人*/
    @Schema(description = "创建人")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username", ds = "master")
    private String createBy;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;

    /**更新人*/
    @Schema(description = "更新人")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username", ds = "master")
    private String updateBy;

    /**租户ID*/
    @Schema(description = "租户ID")
    private Integer tenantId;

    /**删除标识 0:未删除 1:删除*/
    @Schema(description = "删除标识 0:未删除 1:删除")
    @TableLogic
    private Integer delFlag;

    /**所属部门代码*/
    @Schema(description = "所属部门代码")
    private String sysOrgCode;

    /**工作量动态列表*/
    @Schema(description = "工作量动态列表")
    private List<CostProjectWorkloadDTO> costProjectWorkloadList;
}
