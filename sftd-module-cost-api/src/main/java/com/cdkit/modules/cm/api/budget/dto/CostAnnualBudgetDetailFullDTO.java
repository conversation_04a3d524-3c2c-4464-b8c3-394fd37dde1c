package com.cdkit.modules.cm.api.budget.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 项目年度预算详情完整DTO（包含所有子表数据）
 * <AUTHOR>
 * @date 2025-08-07
 */
@Schema(description = "项目年度预算详情完整DTO")
@Data
public class CostAnnualBudgetDetailFullDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    // ==================== 年度预算详情主表信息 ====================
    
    /**UUID主键*/
    @Schema(description = "UUID主键")
    private String id;

    /**关联预算主表ID*/
    @Schema(description = "关联预算主表ID")
    private String budgetId;

    /**关联项目年度计划ID*/
    @Schema(description = "关联项目年度计划ID")
    private String planId;

    /**计划编号*/
    @Schema(description = "计划编号")
    private String planCode;

    /**项目编号*/
    @Schema(description = "项目编号")
    private String projectCode;

    /**年度预算项目名称*/
    @Schema(description = "年度预算项目名称")
    private String projectName;

    /**所属单位*/
    @Schema(description = "所属单位")
    private String professionalCompany;

    /**下属中心*/
    @Schema(description = "下属中心")
    private String center;

    /**预算类型*/
    @Schema(description = "预算类型")
    private String budgetType;

    /**WBS编号*/
    @Schema(description = "WBS编号")
    private String wbsCode;

    /**项目类型*/
    @Schema(description = "项目类型")
    private String projectType;

    /**四级业务*/
    @Schema(description = "四级业务")
    private String fourthLevelBusiness;

    /**业务小类*/
    @Schema(description = "业务小类")
    private String businessArea;

    /**收入预算总额(元)*/
    @Schema(description = "收入预算总额(元)")
    private BigDecimal revenueBudget;

    /**直接成本总额(元)*/
    @Schema(description = "直接成本总额(元)")
    private BigDecimal directCostBudget;

    /**间接成本总额(元)*/
    @Schema(description = "间接成本总额(元)")
    private BigDecimal indirectCostTotal;

    /**本中心间接成本总额(元)*/
    @Schema(description = "本中心间接成本总额(元)")
    private BigDecimal centerIndirectCostTotal;

    /**非经营中心间接成本总额(元)*/
    @Schema(description = "非经营中心间接成本总额(元)")
    private BigDecimal nonOperatingCenterIndirectCostTotal;

    /**综合管理间接成本总额(元)*/
    @Schema(description = "综合管理间接成本总额(元)")
    private BigDecimal comprehensiveManagementIndirectCostTotal;

    /**净利润(元)*/
    @Schema(description = "净利润(元)")
    private BigDecimal netProfit;

    /**净利润率(%)*/
    @Schema(description = "净利润率(%)")
    private BigDecimal netProfitRate;

    /**毛利润(元)*/
    @Schema(description = "毛利润(元)")
    private BigDecimal grossProfit;

    /**毛利润率(%)*/
    @Schema(description = "毛利润率(%)")
    private BigDecimal grossProfitRate;

    /**边际利润(元)*/
    @Schema(description = "边际利润(元)")
    private BigDecimal marginalProfit;

    /**边际利润率(%)*/
    @Schema(description = "边际利润率(%)")
    private BigDecimal marginalProfitRate;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;

    /**创建人*/
    @Schema(description = "创建人")
    private String createBy;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;

    /**更新人*/
    @Schema(description = "更新人")
    private String updateBy;

    /**租户ID*/
    @Schema(description = "租户ID")
    private Integer tenantId;

    /**所属部门代码*/
    @Schema(description = "所属部门代码")
    private String sysOrgCode;

    // ==================== 子表数据列表 ====================

    /**详情直接成本列表*/
    @Schema(description = "详情直接成本列表")
    private List<DirectCostItemDTO> directCostList;

    /**本中心间接成本列表*/
    @Schema(description = "本中心间接成本列表")
    private List<CenterIndirectCostItemDTO> centerIndirectCostList;

    /**综合间接成本列表*/
    @Schema(description = "综合间接成本列表")
    private List<ComprehensiveIndirectCostItemDTO> comprehensiveIndirectCostList;

    /**非经营间接成本列表*/
    @Schema(description = "非经营间接成本列表")
    private List<NonOperatingIndirectCostItemDTO> nonOperatingIndirectCostList;

    // ==================== 内部DTO类定义 ====================

    /**
     * 详情直接成本DTO
     */
    @Schema(description = "详情直接成本DTO")
    @Data
    public static class DirectCostItemDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**UUID主键*/
        @Schema(description = "UUID主键")
        private String id;

        /**关联年度预算明细ID*/
        @Schema(description = "关联年度预算明细ID")
        private String budgetDetailId;

        /**预算科目编码*/
        @Schema(description = "预算科目编码")
        private String subjectCode;

        /**预算科目名称*/
        @Schema(description = "预算科目名称")
        private String subjectName;

        /**科目释义*/
        @Schema(description = "科目释义")
        private String subjectDescription;

        /**支出预算金额(元)*/
        @Schema(description = "支出预算金额(元)")
        private BigDecimal budgetAmount;

        /**创建时间*/
        @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @Schema(description = "创建时间")
        private Date createTime;

        /**创建人*/
        @Schema(description = "创建人")
        private String createBy;

        /**更新时间*/
        @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @Schema(description = "更新时间")
        private Date updateTime;

        /**更新人*/
        @Schema(description = "更新人")
        private String updateBy;

        /**租户ID*/
        @Schema(description = "租户ID")
        private Integer tenantId;

        /**所属部门代码*/
        @Schema(description = "所属部门代码")
        private String sysOrgCode;
    }

    /**
     * 本中心间接成本DTO
     */
    @Schema(description = "本中心间接成本DTO")
    @Data
    public static class CenterIndirectCostItemDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**UUID主键*/
        @Schema(description = "UUID主键")
        private String id;

        /**关联预算明细ID*/
        @Schema(description = "关联预算明细ID")
        private String budgetDetailId;

        /**预算科目编码*/
        @Schema(description = "预算科目编码")
        private String subjectCode;

        /**预算科目名称*/
        @Schema(description = "预算科目名称")
        private String subjectName;

        /**科目释义*/
        @Schema(description = "科目释义")
        private String subjectDescription;

        /**支出预算金额(元)*/
        @Schema(description = "支出预算金额(元)")
        private BigDecimal budgetAmount;

        /**创建时间*/
        @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @Schema(description = "创建时间")
        private Date createTime;

        /**创建人*/
        @Schema(description = "创建人")
        private String createBy;

        /**更新时间*/
        @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @Schema(description = "更新时间")
        private Date updateTime;

        /**更新人*/
        @Schema(description = "更新人")
        private String updateBy;

        /**租户ID*/
        @Schema(description = "租户ID")
        private Integer tenantId;

        /**所属部门代码*/
        @Schema(description = "所属部门代码")
        private String sysOrgCode;
    }

    /**
     * 综合间接成本DTO
     */
    @Schema(description = "综合间接成本DTO")
    @Data
    public static class ComprehensiveIndirectCostItemDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**UUID主键*/
        @Schema(description = "UUID主键")
        private String id;

        /**关联预算明细ID*/
        @Schema(description = "关联预算明细ID")
        private String budgetDetailId;

        /**预算科目编码*/
        @Schema(description = "预算科目编码")
        private String subjectCode;

        /**预算科目名称*/
        @Schema(description = "预算科目名称")
        private String subjectName;

        /**科目释义*/
        @Schema(description = "科目释义")
        private String subjectDescription;

        /**支出预算金额(元)*/
        @Schema(description = "支出预算金额(元)")
        private BigDecimal budgetAmount;

        /**创建时间*/
        @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @Schema(description = "创建时间")
        private Date createTime;

        /**创建人*/
        @Schema(description = "创建人")
        private String createBy;

        /**更新时间*/
        @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @Schema(description = "更新时间")
        private Date updateTime;

        /**更新人*/
        @Schema(description = "更新人")
        private String updateBy;

        /**租户ID*/
        @Schema(description = "租户ID")
        private Integer tenantId;

        /**所属部门代码*/
        @Schema(description = "所属部门代码")
        private String sysOrgCode;
    }

    /**
     * 非经营间接成本DTO
     */
    @Schema(description = "非经营间接成本DTO")
    @Data
    public static class NonOperatingIndirectCostItemDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**UUID主键*/
        @Schema(description = "UUID主键")
        private String id;

        /**关联预算明细ID*/
        @Schema(description = "关联预算明细ID")
        private String budgetDetailId;

        /**预算科目编码*/
        @Schema(description = "预算科目编码")
        private String subjectCode;

        /**预算科目名称*/
        @Schema(description = "预算科目名称")
        private String subjectName;

        /**科目释义*/
        @Schema(description = "科目释义")
        private String subjectDescription;

        /**支出预算金额(元)*/
        @Schema(description = "支出预算金额(元)")
        private BigDecimal budgetAmount;

        /**创建时间*/
        @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @Schema(description = "创建时间")
        private Date createTime;

        /**创建人*/
        @Schema(description = "创建人")
        private String createBy;

        /**更新时间*/
        @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @Schema(description = "更新时间")
        private Date updateTime;

        /**更新人*/
        @Schema(description = "更新人")
        private String updateBy;

        /**租户ID*/
        @Schema(description = "租户ID")
        private Integer tenantId;

        /**所属部门代码*/
        @Schema(description = "所属部门代码")
        private String sysOrgCode;
    }
}
