package com.cdkit.modules.cm.api.businessdata.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description: 预算科目编辑请求
 * @Author: sunhzh
 * @Date: 2025-07-31
 * @Version: V1.0
 */
@Schema(description = "预算科目编辑请求")
@Data
public class CostBudgetSubjectEditRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键ID*/
    @NotBlank(message = "ID不能为空")
    @Schema(description = "主键ID", required = true)
    private String id;

    /**预算科目编码*/
    @NotBlank(message = "预算科目编码不能为空")
    @Schema(description = "预算科目编码", required = true)
    private String subjectCode;

    /**预算科目名称*/
    @NotBlank(message = "预算科目名称不能为空")
    @Schema(description = "预算科目名称", required = true)
    private String subjectName;

    /**科目释义*/
    @Schema(description = "科目释义")
    private String subjectDescription;

    /**状态(Y-启用/N-停用)*/
    @NotBlank(message = "状态不能为空")
    @Schema(description = "状态(Y-启用/N-停用)", required = true)
    private String subjectStatus;

    /**排序号*/
    @NotNull(message = "排序号不能为空")
    @Schema(description = "排序号", required = true)
    private Integer sortOrder;
}
