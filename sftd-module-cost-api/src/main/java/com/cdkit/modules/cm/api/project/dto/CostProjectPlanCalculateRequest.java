package com.cdkit.modules.cm.api.project.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.Valid;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 项目计划计算请求DTO
 * @Author: cdkit-boot
 * @Date: 2025-07-18
 * @Version: V1.0
 */
@Schema(description = "项目计划计算请求DTO")
@Data
public class CostProjectPlanCalculateRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**计划名称*/
    @Schema(description = "计划名称")
    private String planName;

    /**项目编号*/
    @Schema(description = "项目编号")
    private String projectCode;

    /**项目名称*/
    @Schema(description = "项目名称")
    private String projectName;

    /**计划类型(年度计划/季度计划 ANNUAL/QUARTERLY)*/
    @Schema(description = "计划类型(年度计划/季度计划 ANNUAL/QUARTERLY)")
    private String planType;

    /**中心*/
    @Schema(description = "中心")
    private String center;

    /**项目组*/
    @Schema(description = "项目组")
    private String projectGroup;

    /**合同模式*/
    @Schema(description = "合同模式")
    private String contractMode;

    /**合同编号*/
    @Schema(description = "合同编号")
    private String contractCode;

    /**合同名称*/
    @Schema(description = "合同名称")
    private String contractName;

    /**合同/预估收入(税后万元)*/
    @Schema(description = "合同/预估收入(税后万元)")
    private BigDecimal contractRevenue;

    /**项目计划明细列表*/
    @Valid
    @Schema(description = "项目计划明细列表")
    private List<CostProjectPlanDetailDTO> detailList;

    /**直接成本明细列表*/
    @Valid
    @Schema(description = "直接成本明细列表")
    private List<CostDirectCostDTO> directCostList;

    /**其他成本明细列表*/
    @Valid
    @Schema(description = "其他成本明细列表")
    private List<CostOtherCostDTO> otherCostList;

    /**税金及附加明细列表*/
    @Valid
    @Schema(description = "税金及附加明细列表")
    private List<CostTaxCostDTO> taxCostList;
}
