package com.cdkit.modules.cm.api.common;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Excel导入结果VO
 * <AUTHOR>
 * @date 2025/07/11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Excel导入结果")
public class ExcelImportResult implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 导入总数
     */
    @Schema(description = "导入总数")
    private Integer totalCount;

    /**
     * 成功导入数量
     */
    @Schema(description = "成功导入数量")
    private Integer successCount;

    /**
     * 失败数量
     */
    @Schema(description = "失败数量")
    private Integer failCount;

    /**
     * 错误信息列表
     */
    @Schema(description = "错误信息列表")
    private List<String> errorMessages;

    /**
     * 是否全部成功
     */
    @Schema(description = "是否全部成功")
    private Boolean allSuccess;

    /**
     * 导入耗时（毫秒）
     */
    @Schema(description = "导入耗时（毫秒）")
    private Long duration;
}
