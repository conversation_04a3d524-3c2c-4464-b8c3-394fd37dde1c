package com.cdkit.modules.cm.api.budget;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cdkit.common.api.vo.Result;
import com.cdkit.modules.cm.api.budget.dto.BudgetSubjectInfoDTO;
import com.cdkit.modules.cm.api.budget.dto.CenterCostSubjectSummaryDTO;
import com.cdkit.modules.cm.api.budget.dto.CostAnnualBudgetDTO;
import com.cdkit.modules.cm.api.budget.dto.CostAnnualBudgetDetailDTO;
import com.cdkit.modules.cm.api.budget.dto.CostAnnualBudgetDetailFullDTO;
import com.cdkit.modules.cm.api.budget.dto.ProjectAnnualBudgetQueryDTO;
import com.cdkit.modules.cm.api.budget.request.CostAnnualBudgetSaveRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 年度总预算API接口
 * <AUTHOR>
 * @date 2025-07-30
 */
@Tag(name = "年度总预算管理")
public interface IAnnualBudgetApi {

    /**
     * 分页查询年度总预算列表
     *
     * @param queryVO 查询条件
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @return 分页结果
     */
    @Operation(summary = "年度总预算-分页列表查询")
    @GetMapping("/list")
    Result<IPage<CostAnnualBudgetDTO>> queryPageList(
            CostAnnualBudgetDTO queryVO,
            @Parameter(description = "页码", example = "1") @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @Parameter(description = "每页数量", example = "10") @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize
    );

    /**
     * 根据ID查询年度总预算详情（包含明细数据）
     *
     * @param id 年度总预算ID
     * @return 年度总预算详情（包含明细数据）
     */
    @Operation(summary = "根据ID查询年度总预算详情（包含明细数据）")
    @GetMapping("/queryById")
    Result<CostAnnualBudgetDetailDTO> queryById(@Parameter(description = "年度总预算ID", required = true) @RequestParam String id);

    /**
     * 编辑年度总预算
     *
     * @param costAnnualBudget 年度总预算数据
     * @return 操作结果
     */
    @Operation(summary = "编辑年度总预算")
    @PutMapping("/edit")
    Result<String> edit(@RequestBody CostAnnualBudgetDTO costAnnualBudget);

    /**
     * 根据ID删除年度总预算
     *
     * @param id 年度总预算ID
     * @return 操作结果
     */
    @Operation(summary = "根据ID删除年度总预算")
    @DeleteMapping("/delete")
    Result<String> delete(@Parameter(description = "年度总预算ID", required = true) @RequestParam String id);

    /**
     * 批量删除年度总预算
     *
     * @param ids 年度总预算ID列表，逗号分隔
     * @return 操作结果
     */
    @Operation(summary = "批量删除年度总预算")
    @DeleteMapping("/deleteBatch")
    Result<String> deleteBatch(@Parameter(description = "年度总预算ID列表，逗号分隔", required = true) @RequestParam String ids);

    /**
     * 生成下一个预算编号
     *
     * @return 下一个预算编号（ZYS+当前年份+3位流水）
     */
    @Operation(summary = "生成下一个预算编号")
    @GetMapping("/generateNextBudgetCode")
    Result<String> generateNextBudgetCode();

    /**
     * 保存年度预算（第一步）
     * 保存年度总预算主表信息、项目年度预算信息和直接成本明细
     *
     * @param request 年度预算保存请求对象
     * @return 操作结果
     */
    @Operation(summary = "保存年度预算（第一步）")
    @PostMapping("/saveStep1")
    Result<String> saveStep1(@RequestBody CostAnnualBudgetSaveRequest request);

    /**
     * 编辑年度预算（第一步）
     * 编辑年度总预算主表信息、项目年度预算信息和直接成本明细
     * 采用"先删除再新增"的方式更新明细数据
     *
     * @param request 年度预算保存请求对象
     * @return 操作结果
     */
    @Operation(summary = "编辑年度预算（第一步）")
    @PutMapping("/editStep1")
    Result<String> editStep1(@RequestBody CostAnnualBudgetSaveRequest request);

    /**
     * 根据项目计划ID查询关联的预算科目信息
     *
     * @param projectPlanId 项目计划ID（可选参数）
     * @return 预算科目信息列表
     */
    @Operation(summary = "根据项目计划ID查询关联的预算科目信息")
    @GetMapping("/subjects")
    Result<List<BudgetSubjectInfoDTO>> queryBudgetSubjects(
            @Parameter(description = "项目计划ID（可选参数）", required = false)
            @RequestParam(name = "projectPlanId", required = false) String projectPlanId);

    @Operation(summary = "年度预算明细-导入Excel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<CostAnnualBudgetSaveRequest> importExcel(HttpServletRequest request, HttpServletResponse response);

    /**
     * Step2 导入 Excel 接口
     * 导入中心间接成本数据到 CostAnnualBudgetCenterCostImport 表
     *
     * @param annualBudgetId 年度总预算的ID（必填）
     * @param templateType 模版类型（必填）
     * @param request HTTP请求
     * @param response HTTP响应
     * @return 导入结果
     */
    @Operation(summary = "Step2 导入中心间接成本Excel")
    @RequestMapping(value = "/importStep2Excel", method = RequestMethod.POST)
    public Result<String> importStep2Excel(
            @Parameter(description = "年度总预算的ID", required = true) @RequestParam String annualBudgetId,
            @Parameter(description = "模版类型", required = true) @RequestParam String templateType,
            HttpServletRequest request,
            HttpServletResponse response);

    /**
     * Step2 列表接口 - 查询中心间接成本科目汇总数据
     * 根据预算ID和模版类型查询导入的中心间接成本数据，按科目汇总金额
     *
     * @param budgetId 年度预算ID（必填）
     * @param templateType 模版类型（必填）
     * @return 科目汇总列表
     */
    @Operation(summary = "Step2 列表接口 - 查询中心间接成本科目汇总数据")
    @GetMapping("/step2/list")
    Result<List<CenterCostSubjectSummaryDTO>> queryStep2List(
            @Parameter(description = "年度预算ID", required = true) @RequestParam String budgetId,
            @Parameter(description = "模版类型", required = true) @RequestParam String templateType
    );

    /**
     * 保存年度预算（第二步）
     * 执行间接成本分摊逻辑并保存分摊结果
     *
     * @param budgetId 年度预算ID
     * @return 操作结果
     */
    @Operation(summary = "保存年度预算（第二步）- 执行间接成本分摊")
    @PostMapping("/saveStep2")
    Result<String> saveStep2(@Parameter(description = "年度预算ID", required = true) @RequestParam String budgetId);

    /**
     * 根据项目年度预算明细ID查询完整详情信息
     * 包含主表信息和所有子表数据（直接成本、本中心间接成本、综合间接成本、非经营间接成本）
     *
     * @param budgetDetailId 项目年度预算明细ID
     * @return 完整详情信息
     */
    @Operation(summary = "根据项目年度预算明细ID查询完整详情信息")
    @GetMapping("/queryDetailFull/{budgetDetailId}")
    Result<CostAnnualBudgetDetailFullDTO> queryDetailFullById(
            @Parameter(description = "项目年度预算明细ID", required = true) @PathVariable String budgetDetailId
    );

    /**
     * 根据项目编号查询项目年度预算信息
     * 查询项目的年度预算详情，包括年度预算编码、WBS编号、所属单位、下属中心、项目名称、预算类型、收入预算金额、支出预算金额
     *
     * @param projectCode 项目编号
     * @return 项目年度预算信息列表
     */
    @Operation(summary = "根据项目编号查询项目年度预算信息")
    @GetMapping("/queryByProjectCode")
    Result<List<ProjectAnnualBudgetQueryDTO>> queryByProjectCode(
            @Parameter(description = "项目编号", required = true) @RequestParam String projectCode
    );
}
