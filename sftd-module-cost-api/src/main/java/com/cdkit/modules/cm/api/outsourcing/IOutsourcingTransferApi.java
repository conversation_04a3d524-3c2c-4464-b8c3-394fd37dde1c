package com.cdkit.modules.cm.api.outsourcing;

import com.cdkit.common.api.vo.Result;
import com.cdkit.modules.cm.api.outsourcing.dto.OutsourcingTransferListDTO;
import com.cdkit.modules.cm.api.outsourcing.dto.OutsourcingTransferItemDTO;
import com.cdkit.modules.cm.api.outsourcing.dto.OutsourcingTransferConfirmRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 转外委API接口
 * 
 * <AUTHOR>
 * @date 2025-08-13
 */
@Tag(name = "转外委管理")
public interface IOutsourcingTransferApi {

    /**
     * 获取转外委列表
     * 根据项目计划ID获取可转外委的产品和半成品列表
     *
     * @param planId 项目计划ID
     * @return 转外委项目列表（产品作为主表，半成品作为子表）
     */
    @Operation(summary = "获取转外委列表", description = "根据项目计划ID获取可转外委的产品和半成品列表，支持子母表结构")
    @GetMapping("/list/{planId}")
    List<OutsourcingTransferItemDTO> getOutsourcingTransferList(
            @Parameter(description = "项目计划ID", required = true) @PathVariable String planId);

    /**
     * 确认转外委
     * 根据用户填写的外委量生成外委单据
     *
     * @param items 转外委项目列表
     * @return 外委单据ID
     */
    @Operation(summary = "确认转外委", description = "根据用户填写的外委量生成外委单据")
    @PostMapping("/confirm")
    Result<String> confirmOutsourcingTransfer(
            @Parameter(description = "转外委项目列表", required = true) @Valid @RequestBody List<OutsourcingTransferItemDTO> items);

    /**
     * 根据外委产品更新转外委数据
     * 当用户选择外委产品后，根据外委产品的配方信息更新半成品用量等数据
     *
     * @param itemDTO 转外委项目数据
     * @return 更新后的转外委项目数据
     */
    @Operation(summary = "根据外委产品更新转外委数据", description = "当用户选择外委产品后，根据外委产品的配方信息更新半成品用量等数据")
    @PostMapping("/update-by-outsourcing-product")
    Result<OutsourcingTransferItemDTO> updateByOutsourcingProduct(
            @Parameter(description = "转外委项目数据", required = true) @RequestBody @Valid OutsourcingTransferItemDTO itemDTO);
}
