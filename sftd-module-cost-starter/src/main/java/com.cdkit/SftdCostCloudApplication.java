package com.cdkit;

import com.cdkit.common.base.BaseMap;
import com.cdkit.common.constant.GlobalConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * @Date 2025-06-09 11:06
 * @Description 启动类
 * @since 1.0
 */
@EnableCaching
@EnableFeignClients
@EnableScheduling
@SpringBootApplication(scanBasePackages = {"com.cdkit"})
public class SftdCostCloudApplication extends SpringBootServletInitializer implements CommandLineRunner {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    public static void main(String[] args) {
        SpringApplication.run(SftdCostCloudApplication.class, args);
    }

    /**
     * 启动的时候，触发下 Gateway网关刷新
     * 解决： 先启动gateway后启动服务，Swagger接口文档访问不通的问题
     */
    @Override
    public void run(String... args) {
        BaseMap params = new BaseMap();
        params.put(GlobalConstants.HANDLER_NAME, GlobalConstants.LODER_ROUDER_HANDLER);
        //刷新网关
        redisTemplate.convertAndSend(GlobalConstants.REDIS_TOPIC_NAME, params);
    }
}
