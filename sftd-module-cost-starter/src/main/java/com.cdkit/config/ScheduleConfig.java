package com.cdkit.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

/**
 * 定时任务配置类
 * 配置定时任务的线程池和相关参数
 * 
 * <AUTHOR>
 * @date 2025/07/14
 */
@Configuration
public class ScheduleConfig {

    /**
     * 配置定时任务线程池
     * 
     * @return TaskScheduler
     */
    @Bean
    public TaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        // 设置线程池大小
        scheduler.setPoolSize(5);
        // 设置线程名前缀
        scheduler.setThreadNamePrefix("schedule-task-");
        // 设置线程池关闭的时候等待所有任务都完成再继续销毁其他的Bean
        scheduler.setWaitForTasksToCompleteOnShutdown(true);
        // 设置线程池中任务的等待时间，如果超过这个时候还没有销毁就强制销毁
        scheduler.setAwaitTerminationSeconds(60);
        // 设置线程池中的线程的名称前缀
        scheduler.setRejectedExecutionHandler((r, executor) -> {
            // 当线程池满了之后的处理策略
            throw new RuntimeException("定时任务线程池已满，无法执行任务: " + r.toString());
        });
        scheduler.initialize();
        return scheduler;
    }
}
