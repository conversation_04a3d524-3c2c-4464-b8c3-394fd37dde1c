spring:
  data:
    redis:
      database: 1
      host: **************
      port: 32647
      password: 689M8TnXs6
      jedis:
        pool:
          enabled: true
  datasource:
    dynamic:
      datasource:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: **********************************************************************************************************************************************************************************************************
          username: root
          password: sftd@2024123456

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

thirdparty:
  sys:
    url: http://unitary.sf-dev.io/api
#    url: http://unitary.sf-dev.io/api/sftd-wms
#    url: http://127.0.0.1:6013
#    url: http://unitary.sf-dev.io/api/sftd-plm

cdkit:
  thirdparty:
    plm:
      url: http://unitary.sf-dev.io/api/sftd-plm
    wms:
      url: http://unitary.sf-dev.io/api/sftd-wms
    md:
      url: http://unitary.sf-dev.io/api/sftd-md
    mes:
      url: http://unitary.sf-dev.io/api/sftd-mes

  elasticsearch:
    # 配置 es 连接信息
    uri: https://es.sf-dev.io
  operation:
    logType: es
  workflow:
    workFlowServerUrl: http://unitary.sf-dev.io/f2api
    workFlowServerSecret: af446d70-e2c1-11eb-a2a0-0242ac110002
    encryptKey: 'f2bpm$b;v20^uW1:'
    encryptIV: 'f2bpm$b;v20^uW1:'
    corpId: AppDefault
sysLog:
  storage: es

#swagger
knife4j:
  #开启增强配置
  enable: true
  #开启生产环境屏蔽

# 成本预算模块配置
cost:
  # 定时任务配置
  schedule:
    # 是否启用项目自动关闭定时任务
    enable-project-auto-close: true
    # 项目自动关闭定时任务的cron表达式（每年1月1日凌晨1点执行）
    project-auto-close-cron: "0 0 1 1 1 ?"
    # 是否启用测试定时任务（生产环境建议设置为false）
    enable-test-task: false
    # 测试定时任务的cron表达式（每天凌晨2点执行）
    test-task-cron: "0 0 2 * * ?"
    # 定时任务执行超时时间（秒）
    task-timeout-seconds: 1800