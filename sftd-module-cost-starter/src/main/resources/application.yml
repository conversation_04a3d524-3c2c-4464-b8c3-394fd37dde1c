server:
  port: 8080
spring:
  data:
    redis:
      database: 1
      host: **************
      port: 32647
      password: 689M8TnXs6
      jedis:
        pool:
          enabled: true
  main:
    allow-bean-definition-overriding: true
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  datasource:
    dynamic:
      druid:
        initial-size: 5
        min-idle: 5
        maxActive: 20
        maxWait: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        filters: stat,slf4j
      datasource:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: jdbc:mysql://**************:32582/sftd-cost-budget?characterEncoding=UTF-8&useUnicode=true&useSSL=false&tinyInt1isBit=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai&useAffectedRows=true
          username: root
          password: sftd@2024123456
  cloud:
    openfeign:
      circuitbreaker:
        enabled: true
  cache:
    type: redis
    redis:
      time-to-live: 10s
      cache-null-values: true
      key-prefix: 'plan:ceche'
mybatis-plus:
  mapper-locations: classpath*:com/cdkit/**/xml/*Mapper.xml
  global-config:
    banner: false
    db-config:
      table-underline: true
      logic-delete-field: del_flag
      logic-delete-value: 1
      logic-not-delete-value: 0
  configuration:
    call-setters-on-nulls: true

management:
  endpoint:
    health:
      show-details: never
cdkit:
  elasticsearch:
    # 配置 es 连接信息
    uri: https://es.sf-dev.io
  operation:
    logType: es
  workflow:
    workFlowServerUrl: http://unitary.sf-dev.io/f2api
    workFlowServerSecret: af446d70-e2c1-11eb-a2a0-0242ac110002
    encryptKey: 'f2bpm$b;v20^uW1:'
    encryptIV: 'f2bpm$b;v20^uW1:'
    corpId: AppDefault
sysLog:
  storage: es

#swagger
knife4j:
  #开启增强配置
  enable: true
  #开启生产环境屏蔽
# 成本预算模块配置
cost:
  # 定时任务配置
  schedule:
    # 是否启用项目自动关闭定时任务
    enable-project-auto-close: true
    # 项目自动关闭定时任务的cron表达式（每年1月1日凌晨1点执行）
    project-auto-close-cron: "0 0 1 1 1 ?"
    # 是否启用测试定时任务（生产环境建议设置为false）
    enable-test-task: false
    # 测试定时任务的cron表达式（每天凌晨2点执行）
    test-task-cron: "0 0 2 * * ?"
    # 定时任务执行超时时间（秒）
    task-timeout-seconds: 1800