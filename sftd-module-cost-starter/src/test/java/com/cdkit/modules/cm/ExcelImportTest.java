package com.cdkit.modules.cm;

import com.cdkit.modules.cm.application.procurement.PurchaseOrderLedgerApplication;
import com.cdkit.modules.cm.application.procurement.util.HutoolExcelImportUtil;
import com.cdkit.modules.cm.api.common.ExcelImportResult;
import com.cdkit.modules.cm.domain.procurement.entity.CostPurchaseOrderLedgerEntity;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.mock.web.MockMultipartHttpServletRequest;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

/**
 * Excel导入功能测试
 * <AUTHOR>
 * @date 2025/07/14
 */
@Slf4j
@SpringBootTest
public class ExcelImportTest {

    @Autowired
    private PurchaseOrderLedgerApplication purchaseOrderLedgerApplication;

    @Test
    public void testHutoolExcelImport() throws IOException {
        // 创建测试Excel文件
        byte[] excelData = createTestExcelFile();

        // 直接测试Hutool Excel导入工具
        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(excelData)) {
            List<CostPurchaseOrderLedgerEntity> entities = HutoolExcelImportUtil.importExcel(
                    inputStream, "test_purchase_order.xlsx");

            log.info("Hutool解析结果: 共解析到{}条数据", entities.size());
            for (int i = 0; i < entities.size(); i++) {
                CostPurchaseOrderLedgerEntity entity = entities.get(i);
                log.info("第{}条数据: 订单名称={}, 供应商={}, 数量={}",
                        i + 1, entity.getOrderName(), entity.getSupplier(), entity.getQuantity());
            }
        } catch (Exception e) {
            log.error("Hutool解析失败", e);
        }
    }

    @Test
    public void testExcelImport() throws IOException {
        // 创建测试Excel文件
        byte[] excelData = createTestExcelFile();

        // 创建MockMultipartFile
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "test_purchase_order.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                excelData
        );

        // 创建MockHttpServletRequest
        MockMultipartHttpServletRequest request = new MockMultipartHttpServletRequest();
        request.addFile(file);

        // 执行导入
        ExcelImportResult result = purchaseOrderLedgerApplication.importExcel(request);

        // 验证结果
        log.info("导入结果: {}", result);
        log.info("总数: {}, 成功: {}, 失败: {}", result.getTotalCount(), result.getSuccessCount(), result.getFailCount());

        if (!result.getErrorMessages().isEmpty()) {
            log.error("错误信息: {}", result.getErrorMessages());
        }
    }

    /**
     * 创建测试Excel文件
     */
    private byte[] createTestExcelFile() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("采购订单台账");

        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] headers = {
            "签订时间", "计划交货日期/验收日期", "订单类型", "需求编号", "立项号", "PR号",
            "订单名称", "立项名称", "原材料/服务名称", "包装要求/规格", "物资编码", "单位", "数量",
            "预算单价", "预算总价", "协议单价", "协议总价", "订单单价", "订单不含税单价",
            "订单金额", "匹配协议编号", "协议类型", "订单号", "SAP订单号", "供应商",
            "执行区域", "申请单位"
        };

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
        }

        // 创建测试数据行
        Row dataRow = sheet.createRow(1);
        String[] testData = {
            "2025-07-11", "2025-07-20", "采购订单", "REQ001", "PRJ001", "PR001",
            "测试订单", "测试立项", "测试原材料", "测试包装要求", "MAT001", "个", "100",
            "10.50", "1050.00", "10.00", "1000.00", "10.20", "9.50",
            "1020.00", "AGR001", "框架协议", "ORD001", "SAP001", "测试供应商",
            "华南区域", "采购部"
        };

        for (int i = 0; i < testData.length; i++) {
            Cell cell = dataRow.createCell(i);
            cell.setCellValue(testData[i]);
        }

        // 转换为字节数组
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();
        
        return outputStream.toByteArray();
    }
}
