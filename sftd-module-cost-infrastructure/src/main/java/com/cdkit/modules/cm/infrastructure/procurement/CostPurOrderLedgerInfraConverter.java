package com.cdkit.modules.cm.infrastructure.procurement;

import cn.hutool.core.bean.BeanUtil;
import com.cdkit.modules.cm.domain.procurement.entity.CostPurchaseOrderLedgerEntity;
import com.cdkit.modules.cm.infrastructure.procurement.entity.CostPurchaseOrderLedger;

import java.util.List;

/**
 * DO DTO 转换器
 * <AUTHOR>
 * @date 2025/07/10
 */
public class CostPurOrderLedgerInfraConverter {
    public static CostPurchaseOrderLedger convert(CostPurchaseOrderLedgerEntity costPurchaseOrderLedgerDomain) {
        return BeanUtil.copyProperties(costPurchaseOrderLedgerDomain, CostPurchaseOrderLedger.class);
    }

    public static List<CostPurchaseOrderLedgerEntity> convertList(List<CostPurchaseOrderLedger> orderList) {
        return BeanUtil.copyToList(orderList, CostPurchaseOrderLedgerEntity.class);
    }
}
