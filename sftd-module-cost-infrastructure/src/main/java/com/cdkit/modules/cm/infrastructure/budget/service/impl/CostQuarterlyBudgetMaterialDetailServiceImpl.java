package com.cdkit.modules.cm.infrastructure.budget.service.impl;


import com.cdkit.modules.cm.infrastructure.budget.entity.CostQuarterlyBudgetMaterialDetail;
import com.cdkit.modules.cm.infrastructure.budget.mapper.CostQuarterlyBudgetMaterialDetailMapper;
import com.cdkit.modules.cm.infrastructure.budget.service.ICostQuarterlyBudgetMaterialDetailService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 原材料明细表
 * @Author: cdkit-boot
 * @Date:   2025-08-12
 * @Version: V1.0
 */
@Service
public class CostQuarterlyBudgetMaterialDetailServiceImpl extends ServiceImpl<CostQuarterlyBudgetMaterialDetailMapper, CostQuarterlyBudgetMaterialDetail> implements ICostQuarterlyBudgetMaterialDetailService {
	
	@Autowired
	private CostQuarterlyBudgetMaterialDetailMapper costQuarterlyBudgetMaterialDetailMapper;
	
	@Override
	public List<CostQuarterlyBudgetMaterialDetail> selectByMainId(String mainId) {
		return costQuarterlyBudgetMaterialDetailMapper.selectByMainId(mainId);
	}
}
