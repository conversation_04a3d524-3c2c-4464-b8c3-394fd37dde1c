package com.cdkit.modules.cm.infrastructure.project;

import cn.hutool.core.bean.BeanUtil;
import com.cdkit.modules.cm.domain.project.mode.entity.CostProjectWorkloadEntity;
import com.cdkit.modules.cm.infrastructure.project.entity.CostProjectWorkload;

import java.util.List;

/**
 * 工作量基础设施转换器
 * <AUTHOR>
 * @date 2025/07/14
 */
public class CostProjectWorkloadInfraConverter {

    /**
     * 领域实体转基础设施实体
     */
    public static CostProjectWorkload convert(CostProjectWorkloadEntity entity) {
        if (entity == null) {
            return null;
        }
        return BeanUtil.copyProperties(entity, CostProjectWorkload.class);
    }

    /**
     * 基础设施实体转领域实体
     */
    public static CostProjectWorkloadEntity convert(CostProjectWorkload entity) {
        if (entity == null) {
            return null;
        }
        return BeanUtil.copyProperties(entity, CostProjectWorkloadEntity.class);
    }

    /**
     * 基础设施实体列表转领域实体列表
     */
    public static List<CostProjectWorkloadEntity> convertList(List<CostProjectWorkload> entityList) {
        return BeanUtil.copyToList(entityList, CostProjectWorkloadEntity.class);
    }

    /**
     * 领域实体列表转基础设施实体列表
     */
    public static List<CostProjectWorkload> convertToInfraList(List<CostProjectWorkloadEntity> entityList) {
        return BeanUtil.copyToList(entityList, CostProjectWorkload.class);
    }
}
