package com.cdkit.modules.cm.infrastructure.budget.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cdkit.modules.cm.domain.budget.repository.IndirectCostRepository;
import com.cdkit.modules.cm.domain.budget.service.IndirectCostAllocationService;
import com.cdkit.modules.cm.infrastructure.budget.entity.*;
import com.cdkit.modules.cm.infrastructure.budget.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 间接成本仓储实现
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class IndirectCostRepositoryImpl implements IndirectCostRepository {

    private final ICostAnnualBudgetCenterIndirectCostService centerIndirectCostService;
    private final ICostAnnualBudgetNonOperatingIndirectCostService nonOperatingIndirectCostService;
    private final ICostAnnualBudgetComprehensiveIndirectCostService comprehensiveIndirectCostService;
    private final ICostAnnualBudgetDetailService budgetDetailService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveCenterIndirectCost(List<IndirectCostAllocationService.AllocationItem> allocationItems) {
        if (allocationItems == null || allocationItems.isEmpty()) {
            log.info("本中心间接成本分摊结果为空，无需保存");
            return 0;
        }

        log.info("开始保存本中心间接成本数据，数量: {}", allocationItems.size());

        List<CostAnnualBudgetCenterIndirectCost> entities = new ArrayList<>();
        for (IndirectCostAllocationService.AllocationItem item : allocationItems) {
            CostAnnualBudgetCenterIndirectCost entity = new CostAnnualBudgetCenterIndirectCost();
            entity.setBudgetDetailId(item.getBudgetDetailId());
            entity.setSubjectCode(item.getSubjectCode());
            entity.setSubjectName(item.getSubjectName());
            entity.setSubjectDescription(item.getSubjectDescription());
            entity.setBudgetAmount(item.getBudgetAmount());
            entities.add(entity);
        }

        boolean success = centerIndirectCostService.saveBatch(entities);
        int savedCount = success ? entities.size() : 0;
        
        log.info("本中心间接成本数据保存完成，成功数量: {}", savedCount);
        return savedCount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveNonOperatingIndirectCost(List<IndirectCostAllocationService.AllocationItem> allocationItems) {
        if (allocationItems == null || allocationItems.isEmpty()) {
            log.info("非经营中心间接成本分摊结果为空，无需保存");
            return 0;
        }

        log.info("开始保存非经营中心间接成本数据，数量: {}", allocationItems.size());

        List<CostAnnualBudgetNonOperatingIndirectCost> entities = new ArrayList<>();
        for (IndirectCostAllocationService.AllocationItem item : allocationItems) {
            CostAnnualBudgetNonOperatingIndirectCost entity = new CostAnnualBudgetNonOperatingIndirectCost();
            entity.setBudgetDetailId(item.getBudgetDetailId());
            entity.setSubjectCode(item.getSubjectCode());
            entity.setSubjectName(item.getSubjectName());
            entity.setSubjectDescription(item.getSubjectDescription());
            entity.setBudgetAmount(item.getBudgetAmount());
            entities.add(entity);
        }

        boolean success = nonOperatingIndirectCostService.saveBatch(entities);
        int savedCount = success ? entities.size() : 0;
        
        log.info("非经营中心间接成本数据保存完成，成功数量: {}", savedCount);
        return savedCount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveComprehensiveIndirectCost(List<IndirectCostAllocationService.AllocationItem> allocationItems) {
        if (allocationItems == null || allocationItems.isEmpty()) {
            log.info("综合管理间接成本分摊结果为空，无需保存");
            return 0;
        }

        log.info("开始保存综合管理间接成本数据，数量: {}", allocationItems.size());

        List<CostAnnualBudgetComprehensiveIndirectCost> entities = new ArrayList<>();
        for (IndirectCostAllocationService.AllocationItem item : allocationItems) {
            CostAnnualBudgetComprehensiveIndirectCost entity = new CostAnnualBudgetComprehensiveIndirectCost();
            entity.setBudgetDetailId(item.getBudgetDetailId());
            entity.setSubjectCode(item.getSubjectCode());
            entity.setSubjectName(item.getSubjectName());
            entity.setSubjectDescription(item.getSubjectDescription());
            entity.setBudgetAmount(item.getBudgetAmount());
            entities.add(entity);
        }

        boolean success = comprehensiveIndirectCostService.saveBatch(entities);
        int savedCount = success ? entities.size() : 0;
        
        log.info("综合管理间接成本数据保存完成，成功数量: {}", savedCount);
        return savedCount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteCenterIndirectCostByBudgetDetailIds(List<String> budgetDetailIds) {
        if (budgetDetailIds == null || budgetDetailIds.isEmpty()) {
            return 0;
        }

        LambdaQueryWrapper<CostAnnualBudgetCenterIndirectCost> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CostAnnualBudgetCenterIndirectCost::getBudgetDetailId, budgetDetailIds);

        long count = centerIndirectCostService.count(wrapper);
        boolean success = centerIndirectCostService.remove(wrapper);

        log.info("删除本中心间接成本数据，预算明细ID数量: {}, 删除数量: {}", budgetDetailIds.size(), count);
        return success ? (int) count : 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteNonOperatingIndirectCostByBudgetDetailIds(List<String> budgetDetailIds) {
        if (budgetDetailIds == null || budgetDetailIds.isEmpty()) {
            return 0;
        }

        LambdaQueryWrapper<CostAnnualBudgetNonOperatingIndirectCost> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CostAnnualBudgetNonOperatingIndirectCost::getBudgetDetailId, budgetDetailIds);

        long count = nonOperatingIndirectCostService.count(wrapper);
        boolean success = nonOperatingIndirectCostService.remove(wrapper);

        log.info("删除非经营中心间接成本数据，预算明细ID数量: {}, 删除数量: {}", budgetDetailIds.size(), count);
        return success ? (int) count : 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteComprehensiveIndirectCostByBudgetDetailIds(List<String> budgetDetailIds) {
        if (budgetDetailIds == null || budgetDetailIds.isEmpty()) {
            return 0;
        }

        LambdaQueryWrapper<CostAnnualBudgetComprehensiveIndirectCost> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CostAnnualBudgetComprehensiveIndirectCost::getBudgetDetailId, budgetDetailIds);

        long count = comprehensiveIndirectCostService.count(wrapper);
        boolean success = comprehensiveIndirectCostService.remove(wrapper);

        log.info("删除综合管理间接成本数据，预算明细ID数量: {}, 删除数量: {}", budgetDetailIds.size(), count);
        return success ? (int) count : 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteAllIndirectCostByBudgetId(String budgetId) {
        if (budgetId == null || budgetId.trim().isEmpty()) {
            return 0;
        }

        log.info("开始删除预算ID {} 下的所有间接成本数据", budgetId);

        // 先查询该预算下的所有明细ID
        LambdaQueryWrapper<CostAnnualBudgetDetail> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.eq(CostAnnualBudgetDetail::getBudgetId, budgetId);
        List<CostAnnualBudgetDetail> detailList = budgetDetailService.list(detailWrapper);
        
        if (detailList.isEmpty()) {
            log.info("预算ID {} 下没有明细数据，无需删除间接成本", budgetId);
            return 0;
        }

        List<String> budgetDetailIds = detailList.stream()
                .map(CostAnnualBudgetDetail::getId)
                .collect(Collectors.toList());

        // 删除三种类型的间接成本
        int deletedCount = 0;
        deletedCount += deleteCenterIndirectCostByBudgetDetailIds(budgetDetailIds);
        deletedCount += deleteNonOperatingIndirectCostByBudgetDetailIds(budgetDetailIds);
        deletedCount += deleteComprehensiveIndirectCostByBudgetDetailIds(budgetDetailIds);

        log.info("删除预算ID {} 下的所有间接成本数据完成，总删除数量: {}", budgetId, deletedCount);
        return deletedCount;
    }

    @Override
    public java.math.BigDecimal getCenterIndirectCostTotalByBudgetDetailId(String budgetDetailId) {
        if (budgetDetailId == null || budgetDetailId.trim().isEmpty()) {
            return java.math.BigDecimal.ZERO;
        }

        try {
            LambdaQueryWrapper<CostAnnualBudgetCenterIndirectCost> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CostAnnualBudgetCenterIndirectCost::getBudgetDetailId, budgetDetailId);

            List<CostAnnualBudgetCenterIndirectCost> list = centerIndirectCostService.list(wrapper);

            java.math.BigDecimal total = list.stream()
                    .map(CostAnnualBudgetCenterIndirectCost::getBudgetAmount)
                    .filter(amount -> amount != null)
                    .reduce(java.math.BigDecimal.ZERO, java.math.BigDecimal::add);

            log.debug("查询本中心间接成本总额，预算明细ID: {}, 总额: {}", budgetDetailId, total);
            return total;

        } catch (Exception e) {
            log.error("查询本中心间接成本总额失败，预算明细ID: {}", budgetDetailId, e);
            return java.math.BigDecimal.ZERO;
        }
    }

    @Override
    public java.math.BigDecimal getNonOperatingIndirectCostTotalByBudgetDetailId(String budgetDetailId) {
        if (budgetDetailId == null || budgetDetailId.trim().isEmpty()) {
            return java.math.BigDecimal.ZERO;
        }

        try {
            LambdaQueryWrapper<CostAnnualBudgetNonOperatingIndirectCost> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CostAnnualBudgetNonOperatingIndirectCost::getBudgetDetailId, budgetDetailId);

            List<CostAnnualBudgetNonOperatingIndirectCost> list = nonOperatingIndirectCostService.list(wrapper);

            java.math.BigDecimal total = list.stream()
                    .map(CostAnnualBudgetNonOperatingIndirectCost::getBudgetAmount)
                    .filter(amount -> amount != null)
                    .reduce(java.math.BigDecimal.ZERO, java.math.BigDecimal::add);

            log.debug("查询非经营中心间接成本总额，预算明细ID: {}, 总额: {}", budgetDetailId, total);
            return total;

        } catch (Exception e) {
            log.error("查询非经营中心间接成本总额失败，预算明细ID: {}", budgetDetailId, e);
            return java.math.BigDecimal.ZERO;
        }
    }

    @Override
    public java.math.BigDecimal getComprehensiveIndirectCostTotalByBudgetDetailId(String budgetDetailId) {
        if (budgetDetailId == null || budgetDetailId.trim().isEmpty()) {
            return java.math.BigDecimal.ZERO;
        }

        try {
            LambdaQueryWrapper<CostAnnualBudgetComprehensiveIndirectCost> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CostAnnualBudgetComprehensiveIndirectCost::getBudgetDetailId, budgetDetailId);

            List<CostAnnualBudgetComprehensiveIndirectCost> list = comprehensiveIndirectCostService.list(wrapper);

            java.math.BigDecimal total = list.stream()
                    .map(CostAnnualBudgetComprehensiveIndirectCost::getBudgetAmount)
                    .filter(amount -> amount != null)
                    .reduce(java.math.BigDecimal.ZERO, java.math.BigDecimal::add);

            log.debug("查询综合管理间接成本总额，预算明细ID: {}, 总额: {}", budgetDetailId, total);
            return total;

        } catch (Exception e) {
            log.error("查询综合管理间接成本总额失败，预算明细ID: {}", budgetDetailId, e);
            return java.math.BigDecimal.ZERO;
        }
    }
}
