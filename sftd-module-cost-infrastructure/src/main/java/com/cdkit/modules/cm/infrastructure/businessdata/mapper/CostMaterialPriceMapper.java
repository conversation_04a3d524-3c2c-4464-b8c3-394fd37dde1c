package com.cdkit.modules.cm.infrastructure.businessdata.mapper;

import java.util.Date;
import java.util.List;

import com.cdkit.modules.cm.infrastructure.businessdata.entity.CostMaterialPrice;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 材料单价管理表
 * @Author: sunhzh
 * @Date:   2025-07-16
 * @Version: V1.0
 */
public interface CostMaterialPriceMapper extends BaseMapper<CostMaterialPrice> {

    /**
     * 根据物料编码获取最新的材料单价记录
     *
     * @param materialCode 物料编码
     * @return 最新的材料单价记录
     */
    CostMaterialPrice getLatestByMaterialCode(@Param("materialCode") String materialCode);

    /**
     * 根据物料编码查询在指定时间段内生效的记录
     *
     * @param materialCode 物料编码
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 生效的记录列表
     */
    List<CostMaterialPrice> findEffectiveInPeriod(@Param("materialCode") String materialCode,
                                                   @Param("startDate") Date startDate,
                                                   @Param("endDate") Date endDate);

    /**
     * 批量更新状态为已失效
     *
     * @param ids ID列表
     */
    void batchSetExpired(@Param("ids") List<String> ids);

    /**
     * 查询需要更新状态的记录（根据生效失效日期）
     *
     * @return 需要更新状态的记录列表
     */
    List<CostMaterialPrice> findNeedUpdateStatus();
}
