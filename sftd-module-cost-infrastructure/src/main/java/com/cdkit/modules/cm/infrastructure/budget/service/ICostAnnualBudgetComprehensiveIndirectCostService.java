package com.cdkit.modules.cm.infrastructure.budget.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostAnnualBudgetComprehensiveIndirectCost;

import java.util.List;

/**
 * @Description: 其他成本-综合管理间接成本
 * @Author: cdkit-boot
 * @Date:   2025-07-30
 * @Version: V1.0
 */
public interface ICostAnnualBudgetComprehensiveIndirectCostService extends IService<CostAnnualBudgetComprehensiveIndirectCost> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<CostAnnualBudgetComprehensiveIndirectCost>
	 */
	public List<CostAnnualBudgetComprehensiveIndirectCost> selectByMainId(String mainId);
}
