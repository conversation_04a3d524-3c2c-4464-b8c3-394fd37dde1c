package com.cdkit.modules.cm.infrastructure.budget.service.impl;


import com.cdkit.modules.cm.infrastructure.budget.entity.CostAnnualBudgetNonOperatingIndirectCost;
import com.cdkit.modules.cm.infrastructure.budget.mapper.CostAnnualBudgetNonOperatingIndirectCostMapper;
import com.cdkit.modules.cm.infrastructure.budget.service.ICostAnnualBudgetNonOperatingIndirectCostService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 其他成本-非经营中心间接成本
 * @Author: sunhzh
 * @Date:   2025-07-30
 * @Version: V1.0
 */
@Service
public class CostAnnualBudgetNonOperatingIndirectCostServiceImpl extends ServiceImpl<CostAnnualBudgetNonOperatingIndirectCostMapper, CostAnnualBudgetNonOperatingIndirectCost> implements ICostAnnualBudgetNonOperatingIndirectCostService {
	
	@Autowired
	private CostAnnualBudgetNonOperatingIndirectCostMapper costAnnualBudgetNonOperatingIndirectCostMapper;
	
	@Override
	public List<CostAnnualBudgetNonOperatingIndirectCost> selectByMainId(String mainId) {
		return costAnnualBudgetNonOperatingIndirectCostMapper.selectByMainId(mainId);
	}
}
