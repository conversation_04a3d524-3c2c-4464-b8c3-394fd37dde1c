package com.cdkit.modules.cm.infrastructure.procurement.service.impl;

import com.cdkit.modules.cm.infrastructure.procurement.entity.CostPurchaseOrderLedger;
import com.cdkit.modules.cm.infrastructure.procurement.mapper.CostPurchaseOrderLedgerMapper;
import com.cdkit.modules.cm.infrastructure.procurement.service.ICostPurchaseOrderLedgerService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 采购订单台账
 * @Author: cdkit-boot
 * @Date:   2025-07-09
 * @Version: V1.0
 */
@Service
public class CostPurchaseOrderLedgerServiceImpl extends ServiceImpl<CostPurchaseOrderLedgerMapper, CostPurchaseOrderLedger> implements ICostPurchaseOrderLedgerService {

}
