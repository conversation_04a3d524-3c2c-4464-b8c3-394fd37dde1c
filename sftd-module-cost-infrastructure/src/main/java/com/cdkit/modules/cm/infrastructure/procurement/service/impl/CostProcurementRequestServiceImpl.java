package com.cdkit.modules.cm.infrastructure.procurement.service.impl;


import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementRequest;
import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementRequestDetail;
import com.cdkit.modules.cm.infrastructure.procurement.mapper.CostProcurementRequestDetailMapper;
import com.cdkit.modules.cm.infrastructure.procurement.mapper.CostProcurementRequestMapper;
import com.cdkit.modules.cm.infrastructure.procurement.service.ICostProcurementRequestService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;
import java.util.Collection;

/**
 * @Description: 采购申请
 * @Author: sunhzh
 * @Date:   2025-07-24
 * @Version: V1.0
 */
@Service
public class CostProcurementRequestServiceImpl extends ServiceImpl<CostProcurementRequestMapper, CostProcurementRequest> implements ICostProcurementRequestService {

	@Autowired
	private CostProcurementRequestMapper costProcurementRequestMapper;
	@Autowired
	private CostProcurementRequestDetailMapper costProcurementRequestDetailMapper;
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(CostProcurementRequest costProcurementRequest, List<CostProcurementRequestDetail> costProcurementRequestDetailList) {
		costProcurementRequestMapper.insert(costProcurementRequest);
		if(costProcurementRequestDetailList!=null && costProcurementRequestDetailList.size()>0) {
			for(CostProcurementRequestDetail entity:costProcurementRequestDetailList) {
				//外键设置
				entity.setRequestId(costProcurementRequest.getId());
				costProcurementRequestDetailMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(CostProcurementRequest costProcurementRequest,List<CostProcurementRequestDetail> costProcurementRequestDetailList) {
		costProcurementRequestMapper.updateById(costProcurementRequest);
		
		//1.先删除子表数据
		costProcurementRequestDetailMapper.deleteByMainId(costProcurementRequest.getId());
		
		//2.子表数据重新插入
		if(costProcurementRequestDetailList!=null && costProcurementRequestDetailList.size()>0) {
			for(CostProcurementRequestDetail entity:costProcurementRequestDetailList) {
				//外键设置
				entity.setRequestId(costProcurementRequest.getId());
				costProcurementRequestDetailMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		costProcurementRequestDetailMapper.deleteByMainId(id);
		costProcurementRequestMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			costProcurementRequestDetailMapper.deleteByMainId(id.toString());
			costProcurementRequestMapper.deleteById(id);
		}
	}
	
}
