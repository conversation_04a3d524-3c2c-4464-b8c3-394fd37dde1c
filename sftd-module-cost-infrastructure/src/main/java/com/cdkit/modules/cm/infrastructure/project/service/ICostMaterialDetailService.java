package com.cdkit.modules.cm.infrastructure.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.cm.infrastructure.project.entity.CostMaterialDetail;

import java.util.List;

/**
 * @Description: 原料明细
 * @Author: cdkit-boot
 * @Date:   2025-07-22
 * @Version: V1.0
 */
public interface ICostMaterialDetailService extends IService<CostMaterialDetail> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<CostMaterialDetail>
	 */
	public List<CostMaterialDetail> selectByMainId(String mainId);
}
