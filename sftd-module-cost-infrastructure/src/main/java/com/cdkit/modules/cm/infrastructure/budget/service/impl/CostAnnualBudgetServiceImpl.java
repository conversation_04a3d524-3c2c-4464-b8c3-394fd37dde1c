package com.cdkit.modules.cm.infrastructure.budget.service.impl;


import com.cdkit.modules.cm.infrastructure.budget.entity.*;
import com.cdkit.modules.cm.infrastructure.budget.mapper.*;
import com.cdkit.modules.cm.infrastructure.budget.service.ICostAnnualBudgetService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;
import java.util.Collection;

/**
 * @Description: 年度总预算
 * @Author: sunhzh
 * @Date:   2025-07-30
 * @Version: V1.0
 */
@Service
public class CostAnnualBudgetServiceImpl extends ServiceImpl<CostAnnualBudgetMapper, CostAnnualBudget> implements ICostAnnualBudgetService {

	@Autowired
	private CostAnnualBudgetMapper costAnnualBudgetMapper;
	@Autowired
	private CostAnnualBudgetDetailMapper costAnnualBudgetDetailMapper;
	@Autowired
	private CostAnnualBudgetCenterIndirectCostMapper costAnnualBudgetCenterIndirectCostMapper;
	@Autowired
	private CostAnnualBudgetNonOperatingIndirectCostMapper costAnnualBudgetNonOperatingIndirectCostMapper;
	@Autowired
	private CostAnnualBudgetComprehensiveIndirectCostMapper costAnnualBudgetComprehensiveIndirectCostMapper;
	
}
