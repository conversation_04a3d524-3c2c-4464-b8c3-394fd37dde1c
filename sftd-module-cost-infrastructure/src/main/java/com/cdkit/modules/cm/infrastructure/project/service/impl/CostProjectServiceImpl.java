package com.cdkit.modules.cm.infrastructure.project.service.impl;


import com.cdkit.modules.cm.infrastructure.project.entity.CostProject;
import com.cdkit.modules.cm.infrastructure.project.entity.CostProjectWorkload;
import com.cdkit.modules.cm.infrastructure.project.mapper.CostProjectMapper;
import com.cdkit.modules.cm.infrastructure.project.mapper.CostProjectWorkloadMapper;
import com.cdkit.modules.cm.infrastructure.project.service.ICostProjectService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;
import java.util.Collection;

/**
 * @Description: 市场项目台账
 * @Author: cdkit-boot
 * @Date:   2025-07-14
 * @Version: V1.0
 */
@Service
public class CostProjectServiceImpl extends ServiceImpl<CostProjectMapper, CostProject> implements ICostProjectService {

	@Autowired
	private CostProjectMapper costProjectMapper;
	@Autowired
	private CostProjectWorkloadMapper costProjectWorkloadMapper;
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(CostProject costProject, List<CostProjectWorkload> costProjectWorkloadList) {
		costProjectMapper.insert(costProject);
		if(costProjectWorkloadList!=null && costProjectWorkloadList.size()>0) {
			for(CostProjectWorkload entity:costProjectWorkloadList) {
				//外键设置
				entity.setProjectId(costProject.getId());
				costProjectWorkloadMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(CostProject costProject,List<CostProjectWorkload> costProjectWorkloadList) {
		costProjectMapper.updateById(costProject);
		
		//1.先删除子表数据
		costProjectWorkloadMapper.deleteByMainId(costProject.getId());
		
		//2.子表数据重新插入
		if(costProjectWorkloadList!=null && costProjectWorkloadList.size()>0) {
			for(CostProjectWorkload entity:costProjectWorkloadList) {
				//外键设置
				entity.setProjectId(costProject.getId());
				costProjectWorkloadMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		costProjectWorkloadMapper.deleteByMainId(id);
		costProjectMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			costProjectWorkloadMapper.deleteByMainId(id.toString());
			costProjectMapper.deleteById(id);
		}
	}
	
}
