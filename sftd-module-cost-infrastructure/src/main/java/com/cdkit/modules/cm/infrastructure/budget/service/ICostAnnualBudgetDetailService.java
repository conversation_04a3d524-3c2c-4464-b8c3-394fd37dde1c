package com.cdkit.modules.cm.infrastructure.budget.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostAnnualBudgetDetail;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostAnnualBudgetDetailDirectCost;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 项目年度预算
 * @Author: cdkit-boot
 * @Date:   2025-07-30
 * @Version: V1.0
 */
public interface ICostAnnualBudgetDetailService extends IService<CostAnnualBudgetDetail> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<CostAnnualBudgetDetail>
	 */
	public List<CostAnnualBudgetDetail> selectByMainId(String mainId);

	/**
	 * 添加一对多
	 *
	 * @param costAnnualBudgetDetail
	 * @param costAnnualBudgetDetailDirectCostList
	 */
	public void saveMain(CostAnnualBudgetDetail costAnnualBudgetDetail,List<CostAnnualBudgetDetailDirectCost> costAnnualBudgetDetailDirectCostList) ;

	/**
	 * 修改一对多
	 *
	 * @param costAnnualBudgetDetail
	 * @param costAnnualBudgetDetailDirectCostList
	 */
	public void updateMain(CostAnnualBudgetDetail costAnnualBudgetDetail,List<CostAnnualBudgetDetailDirectCost> costAnnualBudgetDetailDirectCostList);


	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);

	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);
}
