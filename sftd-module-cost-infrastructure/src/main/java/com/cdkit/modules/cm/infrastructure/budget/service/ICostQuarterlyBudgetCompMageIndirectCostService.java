package com.cdkit.modules.cm.infrastructure.budget.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostQuarterlyBudgetCompMageIndirectCost;

import java.util.List;

/**
 * @Description: 季度预算-预算科目明细综合管理间接成本表
 * @Author: cdkit-boot
 * @Date:   2025-08-12
 * @Version: V1.0
 */
public interface ICostQuarterlyBudgetCompMageIndirectCostService extends IService<CostQuarterlyBudgetCompMageIndirectCost> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<CostQuarterlyBudgetCompMageIndirectCost>
	 */
	public List<CostQuarterlyBudgetCompMageIndirectCost> selectByMainId(String mainId);
}
