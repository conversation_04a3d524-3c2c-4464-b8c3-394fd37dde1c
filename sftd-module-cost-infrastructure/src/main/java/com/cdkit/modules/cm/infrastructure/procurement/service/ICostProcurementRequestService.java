package com.cdkit.modules.cm.infrastructure.procurement.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementRequest;
import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementRequestDetail;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 采购申请
 * @Author: sunhzh
 * @Date:   2025-07-24
 * @Version: V1.0
 */
public interface ICostProcurementRequestService extends IService<CostProcurementRequest> {

	/**
	 * 添加一对多
	 *
	 * @param costProcurementRequest
	 * @param costProcurementRequestDetailList
	 */
	public void saveMain(CostProcurementRequest costProcurementRequest,List<CostProcurementRequestDetail> costProcurementRequestDetailList) ;
	
	/**
	 * 修改一对多
	 *
   * @param costProcurementRequest
   * @param costProcurementRequestDetailList
	 */
	public void updateMain(CostProcurementRequest costProcurementRequest,List<CostProcurementRequestDetail> costProcurementRequestDetailList);
	
	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);
	
}
