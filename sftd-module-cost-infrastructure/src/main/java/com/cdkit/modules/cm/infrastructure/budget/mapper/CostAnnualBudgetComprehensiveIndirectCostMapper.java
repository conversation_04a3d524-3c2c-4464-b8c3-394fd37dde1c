package com.cdkit.modules.cm.infrastructure.budget.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostAnnualBudgetComprehensiveIndirectCost;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 其他成本-综合管理间接成本
 * @Author: sunhzh
 * @Date:   2025-07-30
 * @Version: V1.0
 */
public interface CostAnnualBudgetComprehensiveIndirectCostMapper extends BaseMapper<CostAnnualBudgetComprehensiveIndirectCost> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<CostAnnualBudgetComprehensiveIndirectCost>
   */
	public List<CostAnnualBudgetComprehensiveIndirectCost> selectByMainId(@Param("mainId") String mainId);
}
