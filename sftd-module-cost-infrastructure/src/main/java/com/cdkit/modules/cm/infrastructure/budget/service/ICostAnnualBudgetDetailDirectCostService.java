package com.cdkit.modules.cm.infrastructure.budget.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostAnnualBudgetDetailDirectCost;

import java.util.List;

/**
 * @Description: 年度总预算项目直接成本明细
 * @Author: cdkit-boot
 * @Date:   2025-08-04
 * @Version: V1.0
 */
public interface ICostAnnualBudgetDetailDirectCostService extends IService<CostAnnualBudgetDetailDirectCost> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<CostAnnualBudgetDetailDirectCost>
	 */
	public List<CostAnnualBudgetDetailDirectCost> selectByMainId(String mainId);
}
