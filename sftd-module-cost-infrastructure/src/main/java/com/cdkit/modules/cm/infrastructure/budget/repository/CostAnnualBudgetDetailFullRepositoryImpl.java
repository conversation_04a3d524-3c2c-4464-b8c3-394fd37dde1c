package com.cdkit.modules.cm.infrastructure.budget.repository;

import com.cdkit.modules.cm.domain.budget.repository.CostAnnualBudgetDetailFullRepository;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostAnnualBudgetDetail;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostAnnualBudgetDetailDirectCost;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostAnnualBudgetCenterIndirectCost;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostAnnualBudgetComprehensiveIndirectCost;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostAnnualBudgetNonOperatingIndirectCost;
import com.cdkit.modules.cm.infrastructure.budget.service.ICostAnnualBudgetDetailService;
import com.cdkit.modules.cm.infrastructure.budget.service.ICostAnnualBudgetDetailDirectCostService;
import com.cdkit.modules.cm.infrastructure.budget.service.ICostAnnualBudgetCenterIndirectCostService;
import com.cdkit.modules.cm.infrastructure.budget.service.ICostAnnualBudgetComprehensiveIndirectCostService;
import com.cdkit.modules.cm.infrastructure.budget.service.ICostAnnualBudgetNonOperatingIndirectCostService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 项目年度预算详情完整信息仓储实现
 * <AUTHOR>
 * @date 2025-08-07
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class CostAnnualBudgetDetailFullRepositoryImpl implements CostAnnualBudgetDetailFullRepository {

    private final ICostAnnualBudgetDetailService costAnnualBudgetDetailService;
    private final ICostAnnualBudgetDetailDirectCostService costAnnualBudgetDetailDirectCostService;
    private final ICostAnnualBudgetCenterIndirectCostService costAnnualBudgetCenterIndirectCostService;
    private final ICostAnnualBudgetComprehensiveIndirectCostService costAnnualBudgetComprehensiveIndirectCostService;
    private final ICostAnnualBudgetNonOperatingIndirectCostService costAnnualBudgetNonOperatingIndirectCostService;

    @Override
    public CostAnnualBudgetDetailFullInfo queryDetailFullById(String budgetDetailId) {
        if (budgetDetailId == null || budgetDetailId.trim().isEmpty()) {
            log.warn("项目年度预算明细ID为空，返回null");
            return null;
        }

        log.info("开始查询项目年度预算完整详情，budgetDetailId: {}", budgetDetailId);

        try {
            // 1. 查询主表数据
            CostAnnualBudgetDetail mainDetail = costAnnualBudgetDetailService.getById(budgetDetailId);
            if (mainDetail == null) {
                log.warn("项目年度预算明细不存在，budgetDetailId: {}", budgetDetailId);
                return null;
            }

            // 2. 查询各个子表数据
            List<CostAnnualBudgetDetailDirectCost> directCostList = 
                costAnnualBudgetDetailDirectCostService.selectByMainId(budgetDetailId);
            
            List<CostAnnualBudgetCenterIndirectCost> centerIndirectCostList = 
                costAnnualBudgetCenterIndirectCostService.selectByMainId(budgetDetailId);
            
            List<CostAnnualBudgetComprehensiveIndirectCost> comprehensiveIndirectCostList = 
                costAnnualBudgetComprehensiveIndirectCostService.selectByMainId(budgetDetailId);
            
            List<CostAnnualBudgetNonOperatingIndirectCost> nonOperatingIndirectCostList = 
                costAnnualBudgetNonOperatingIndirectCostService.selectByMainId(budgetDetailId);

            // 3. 组装完整详情信息
            CostAnnualBudgetDetailFullInfo fullInfo = new CostAnnualBudgetDetailFullInfo();

            // 复制主表数据 - 使用BeanUtils复制基础字段
            BeanUtils.copyProperties(mainDetail, fullInfo);

            // 手动设置字段名不匹配的字段
            fullInfo.setIndirectCostTotal(mainDetail.getIndirectCostTotal());
            fullInfo.setCenterIndirectCostTotal(mainDetail.getCenterIndirectCostTotal());
            fullInfo.setNonOperatingCenterIndirectCostTotal(mainDetail.getNonOperatingCenterIndirectCostTotal());
            fullInfo.setComprehensiveManagementIndirectCostTotal(mainDetail.getComprehensiveManagementIndirectCostTotal());
            fullInfo.setNetProfit(mainDetail.getNetProfit());
            fullInfo.setNetProfitRate(mainDetail.getNetProfitRate());
            fullInfo.setGrossProfit(mainDetail.getGrossProfit());
            fullInfo.setGrossProfitRate(mainDetail.getGrossProfitRate());
            fullInfo.setMarginalProfit(mainDetail.getMarginalProfit());
            fullInfo.setMarginalProfitRate(mainDetail.getMarginalProfitRate());

            // 如果利润相关字段为空，需要计算间接成本总额和各种利润
            if (fullInfo.getIndirectCostTotal() == null || fullInfo.getNetProfit() == null ||
                fullInfo.getGrossProfit() == null || fullInfo.getMarginalProfit() == null) {

                // 计算间接成本总额
                calculateIndirectCostTotal(fullInfo, centerIndirectCostList, comprehensiveIndirectCostList, nonOperatingIndirectCostList);

                // 计算各种利润和利润率
                calculateProfitFields(fullInfo);
            }
            
            // 转换子表数据
            if (directCostList != null && !directCostList.isEmpty()) {
                List<DirectCostItem> directCostItems = directCostList.stream()
                    .map(this::convertToDirectCostItem)
                    .collect(Collectors.toList());
                fullInfo.setDirectCostList(directCostItems);
            }
            
            if (centerIndirectCostList != null && !centerIndirectCostList.isEmpty()) {
                List<CenterIndirectCostItem> centerIndirectCostItems = centerIndirectCostList.stream()
                    .map(this::convertToCenterIndirectCostItem)
                    .collect(Collectors.toList());
                fullInfo.setCenterIndirectCostList(centerIndirectCostItems);
            }
            
            if (comprehensiveIndirectCostList != null && !comprehensiveIndirectCostList.isEmpty()) {
                List<ComprehensiveIndirectCostItem> comprehensiveIndirectCostItems = comprehensiveIndirectCostList.stream()
                    .map(this::convertToComprehensiveIndirectCostItem)
                    .collect(Collectors.toList());
                fullInfo.setComprehensiveIndirectCostList(comprehensiveIndirectCostItems);
            }
            
            if (nonOperatingIndirectCostList != null && !nonOperatingIndirectCostList.isEmpty()) {
                List<NonOperatingIndirectCostItem> nonOperatingIndirectCostItems = nonOperatingIndirectCostList.stream()
                    .map(this::convertToNonOperatingIndirectCostItem)
                    .collect(Collectors.toList());
                fullInfo.setNonOperatingIndirectCostList(nonOperatingIndirectCostItems);
            }

            log.info("查询项目年度预算完整详情成功，budgetDetailId: {}, 项目名称: {}, 直接成本数量: {}, 本中心间接成本数量: {}, 综合间接成本数量: {}, 非经营间接成本数量: {}",
                    budgetDetailId, fullInfo.getProjectName(),
                    directCostList != null ? directCostList.size() : 0,
                    centerIndirectCostList != null ? centerIndirectCostList.size() : 0,
                    comprehensiveIndirectCostList != null ? comprehensiveIndirectCostList.size() : 0,
                    nonOperatingIndirectCostList != null ? nonOperatingIndirectCostList.size() : 0);

            return fullInfo;

        } catch (Exception e) {
            log.error("查询项目年度预算完整详情失败，budgetDetailId: {}", budgetDetailId, e);
            throw new RuntimeException("查询项目年度预算完整详情失败：" + e.getMessage(), e);
        }
    }

    /**
     * 转换直接成本实体为Domain对象
     */
    private DirectCostItem convertToDirectCostItem(CostAnnualBudgetDetailDirectCost entity) {
        DirectCostItem item = new DirectCostItem();
        BeanUtils.copyProperties(entity, item);
        return item;
    }

    /**
     * 转换本中心间接成本实体为Domain对象
     */
    private CenterIndirectCostItem convertToCenterIndirectCostItem(CostAnnualBudgetCenterIndirectCost entity) {
        CenterIndirectCostItem item = new CenterIndirectCostItem();
        BeanUtils.copyProperties(entity, item);
        return item;
    }

    /**
     * 转换综合间接成本实体为Domain对象
     */
    private ComprehensiveIndirectCostItem convertToComprehensiveIndirectCostItem(CostAnnualBudgetComprehensiveIndirectCost entity) {
        ComprehensiveIndirectCostItem item = new ComprehensiveIndirectCostItem();
        BeanUtils.copyProperties(entity, item);
        return item;
    }

    /**
     * 转换非经营间接成本实体为Domain对象
     */
    private NonOperatingIndirectCostItem convertToNonOperatingIndirectCostItem(CostAnnualBudgetNonOperatingIndirectCost entity) {
        NonOperatingIndirectCostItem item = new NonOperatingIndirectCostItem();
        BeanUtils.copyProperties(entity, item);
        return item;
    }

    /**
     * 计算间接成本总额
     * 间接成本总额 = 本中心间接成本总额 + 非经营中心间接成本总额 + 综合管理间接成本总额
     */
    private void calculateIndirectCostTotal(CostAnnualBudgetDetailFullInfo fullInfo,
                                          List<CostAnnualBudgetCenterIndirectCost> centerIndirectCostList,
                                          List<CostAnnualBudgetComprehensiveIndirectCost> comprehensiveIndirectCostList,
                                          List<CostAnnualBudgetNonOperatingIndirectCost> nonOperatingIndirectCostList) {

        // 计算本中心间接成本总额
        BigDecimal centerTotal = BigDecimal.ZERO;
        if (centerIndirectCostList != null && !centerIndirectCostList.isEmpty()) {
            centerTotal = centerIndirectCostList.stream()
                    .map(CostAnnualBudgetCenterIndirectCost::getBudgetAmount)
                    .filter(amount -> amount != null)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        fullInfo.setCenterIndirectCostTotal(centerTotal);

        // 计算综合管理间接成本总额
        BigDecimal comprehensiveTotal = BigDecimal.ZERO;
        if (comprehensiveIndirectCostList != null && !comprehensiveIndirectCostList.isEmpty()) {
            comprehensiveTotal = comprehensiveIndirectCostList.stream()
                    .map(CostAnnualBudgetComprehensiveIndirectCost::getBudgetAmount)
                    .filter(amount -> amount != null)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        fullInfo.setComprehensiveManagementIndirectCostTotal(comprehensiveTotal);

        // 计算非经营中心间接成本总额
        BigDecimal nonOperatingTotal = BigDecimal.ZERO;
        if (nonOperatingIndirectCostList != null && !nonOperatingIndirectCostList.isEmpty()) {
            nonOperatingTotal = nonOperatingIndirectCostList.stream()
                    .map(CostAnnualBudgetNonOperatingIndirectCost::getBudgetAmount)
                    .filter(amount -> amount != null)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        fullInfo.setNonOperatingCenterIndirectCostTotal(nonOperatingTotal);

        // 计算间接成本总额
        BigDecimal indirectCostTotal = centerTotal.add(comprehensiveTotal).add(nonOperatingTotal);
        fullInfo.setIndirectCostTotal(indirectCostTotal);
    }

    /**
     * 计算各种利润和利润率
     */
    private void calculateProfitFields(CostAnnualBudgetDetailFullInfo fullInfo) {
        BigDecimal revenueBudget = fullInfo.getRevenueBudget();
        BigDecimal directCostBudget = fullInfo.getDirectCostBudget();
        BigDecimal indirectCostTotal = fullInfo.getIndirectCostTotal();
        BigDecimal centerIndirectCostTotal = fullInfo.getCenterIndirectCostTotal();

        if (revenueBudget == null || directCostBudget == null) {
            return;
        }

        // 1. 计算边际利润 = 收入预算总额 - 直接成本总额
        BigDecimal marginalProfit = revenueBudget.subtract(directCostBudget)
                .setScale(2, java.math.RoundingMode.HALF_UP);
        fullInfo.setMarginalProfit(marginalProfit);

        // 2. 计算边际利润率 = 边际利润 / 收入预算总额 × 100
        if (revenueBudget.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal marginalProfitRate = marginalProfit
                    .divide(revenueBudget, 6, java.math.RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"))
                    .setScale(2, java.math.RoundingMode.HALF_UP);
            fullInfo.setMarginalProfitRate(marginalProfitRate);
        }

        // 3. 计算毛利润 = 收入预算总额 - 直接成本总额 - 本中心间接成本总额
        BigDecimal grossProfit = marginalProfit;
        if (centerIndirectCostTotal != null) {
            grossProfit = grossProfit.subtract(centerIndirectCostTotal);
        }
        grossProfit = grossProfit.setScale(2, java.math.RoundingMode.HALF_UP);
        fullInfo.setGrossProfit(grossProfit);

        // 4. 计算毛利润率 = 毛利润 / 收入预算总额 × 100
        if (revenueBudget.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal grossProfitRate = grossProfit
                    .divide(revenueBudget, 6, java.math.RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"))
                    .setScale(2, java.math.RoundingMode.HALF_UP);
            fullInfo.setGrossProfitRate(grossProfitRate);
        }

        // 5. 计算净利润 = 收入预算总额 - 直接成本总额 - 间接成本总额
        BigDecimal netProfit = marginalProfit;
        if (indirectCostTotal != null) {
            netProfit = netProfit.subtract(indirectCostTotal);
        }
        netProfit = netProfit.setScale(2, java.math.RoundingMode.HALF_UP);
        fullInfo.setNetProfit(netProfit);

        // 6. 计算净利润率 = 净利润 / 收入预算总额 × 100
        if (revenueBudget.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal netProfitRate = netProfit
                    .divide(revenueBudget, 6, java.math.RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"))
                    .setScale(2, java.math.RoundingMode.HALF_UP);
            fullInfo.setNetProfitRate(netProfitRate);
        }
    }
}
