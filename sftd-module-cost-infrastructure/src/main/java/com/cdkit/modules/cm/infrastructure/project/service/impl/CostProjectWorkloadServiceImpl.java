package com.cdkit.modules.cm.infrastructure.project.service.impl;


import com.cdkit.modules.cm.infrastructure.project.entity.CostProjectWorkload;
import com.cdkit.modules.cm.infrastructure.project.mapper.CostProjectWorkloadMapper;
import com.cdkit.modules.cm.infrastructure.project.service.ICostProjectWorkloadService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 工作量动态
 * @Author: cdkit-boot
 * @Date:   2025-07-14
 * @Version: V1.0
 */
@Service
public class CostProjectWorkloadServiceImpl extends ServiceImpl<CostProjectWorkloadMapper, CostProjectWorkload> implements ICostProjectWorkloadService {
	
	@Autowired
	private CostProjectWorkloadMapper costProjectWorkloadMapper;
	
	@Override
	public List<CostProjectWorkload> selectByMainId(String mainId) {
		return costProjectWorkloadMapper.selectByMainId(mainId);
	}
}
