package com.cdkit.modules.cm.infrastructure.budget.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.cdkit.modules.cm.domain.budget.model.dto.CenterCostImportDTO;
import com.cdkit.modules.cm.domain.budget.model.dto.CenterCostSubjectSummaryDTO;
import com.cdkit.modules.cm.domain.budget.repository.CenterCostImportRepository;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostAnnualBudgetCenterCostImport;
import com.cdkit.modules.cm.infrastructure.budget.mapper.CostAnnualBudgetCenterCostImportMapper;
import com.cdkit.modules.cm.infrastructure.budget.service.ICostAnnualBudgetCenterCostImportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 中心间接成本导入数据仓储实现
 * 实现Domain层定义的Repository接口，封装对Infrastructure层的访问
 * 
 * <AUTHOR>
 * @date 2025-08-06
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class CenterCostImportRepositoryImpl implements CenterCostImportRepository {

    private final ICostAnnualBudgetCenterCostImportService centerCostImportService;
    private final CostAnnualBudgetCenterCostImportMapper centerCostImportMapper;

    @Override
    public int saveBatch(List<CenterCostImportDTO> importDataList) {
        if (importDataList == null || importDataList.isEmpty()) {
            log.warn("导入数据列表为空");
            return 0;
        }

        log.info("开始批量保存中心间接成本导入数据，数量: {}", importDataList.size());

        // 转换DTO为Infrastructure层的Entity
        List<CostAnnualBudgetCenterCostImport> entityList = importDataList.stream()
                .map(this::convertToEntity)
                .collect(Collectors.toList());

        // 调用Infrastructure层的Service进行批量保存
        boolean success = centerCostImportService.saveBatch(entityList);
        
        if (success) {
            log.info("批量保存中心间接成本导入数据成功，数量: {}", entityList.size());
            return entityList.size();
        } else {
            log.error("批量保存中心间接成本导入数据失败");
            return 0;
        }
    }

    @Override
    public int deleteByBudgetCode(String budgetCode) {
        if (budgetCode == null || budgetCode.trim().isEmpty()) {
            log.warn("预算编号为空，无需删除导入数据");
            return 0;
        }

        log.info("开始删除预算编号为 {} 的导入数据", budgetCode);

        try {
            // 使用MyBatis-Plus的条件删除
            int deletedCount = centerCostImportService.lambdaUpdate()
                    .eq(CostAnnualBudgetCenterCostImport::getBudgetCode, budgetCode)
                    .remove() ? 1 : 0;

            log.info("删除预算编号为 {} 的导入数据完成，删除数量: {}", budgetCode, deletedCount);
            return deletedCount;
        } catch (Exception e) {
            log.error("删除预算编号为 {} 的导入数据失败", budgetCode, e);
            throw new RuntimeException("删除导入数据失败：" + e.getMessage(), e);
        }
    }

    @Override
    public int deleteByBudgetCodeAndTemplateType(String budgetCode, String templateType) {
        if (budgetCode == null || budgetCode.trim().isEmpty()) {
            log.warn("预算编号为空，无需删除导入数据");
            return 0;
        }
        if (templateType == null || templateType.trim().isEmpty()) {
            log.warn("模版类型为空，无需删除导入数据");
            return 0;
        }

        log.info("开始删除预算编号为 {} 且模版类型为 {} 的导入数据", budgetCode, templateType);

        try {
            // 使用MyBatis-Plus的条件删除
            int deletedCount = centerCostImportService.lambdaUpdate()
                    .eq(CostAnnualBudgetCenterCostImport::getBudgetCode, budgetCode)
                    .eq(CostAnnualBudgetCenterCostImport::getTemplateType, templateType)
                    .remove() ? 1 : 0;

            log.info("删除预算编号为 {} 且模版类型为 {} 的导入数据完成，删除数量: {}", budgetCode, templateType, deletedCount);
            return deletedCount;
        } catch (Exception e) {
            log.error("删除预算编号为 {} 且模版类型为 {} 的导入数据失败", budgetCode, templateType, e);
            throw new RuntimeException("删除导入数据失败：" + e.getMessage(), e);
        }
    }

    @Override
    public List<CenterCostImportDTO> findByBudgetCode(String budgetCode) {
        if (!StringUtils.hasText(budgetCode)) {
            log.warn("预算编号为空，返回空列表");
            return new ArrayList<>();
        }

        log.info("查询预算编号为 {} 的导入数据", budgetCode);

        try {
            // 根据预算编号查询导入数据
            LambdaQueryWrapper<CostAnnualBudgetCenterCostImport> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CostAnnualBudgetCenterCostImport::getBudgetCode, budgetCode);

            List<CostAnnualBudgetCenterCostImport> entityList = centerCostImportService.list(queryWrapper);

            if (entityList == null || entityList.isEmpty()) {
                log.info("未找到预算编号为 {} 的导入数据", budgetCode);
                return new ArrayList<>();
            }

            // 转换为DTO
            List<CenterCostImportDTO> dtoList = entityList.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            log.info("查询预算编号为 {} 的导入数据完成，数量: {}", budgetCode, dtoList.size());
            return dtoList;

        } catch (Exception e) {
            log.error("查询预算编号为 {} 的导入数据失败", budgetCode, e);
            throw new RuntimeException("查询导入数据失败：" + e.getMessage(), e);
        }
    }

    @Override
    public int copyImportDataByBudgetCode(String originalBudgetCode, String newBudgetCode) {
        if (!StringUtils.hasText(originalBudgetCode) || !StringUtils.hasText(newBudgetCode)) {
            log.warn("原预算编号或新预算编号为空，无法复制导入数据");
            return 0;
        }

        log.info("开始复制间接成本导入数据，原预算编号: {}, 新预算编号: {}", originalBudgetCode, newBudgetCode);

        try {
            // 1. 查询原预算的导入数据
            List<CenterCostImportDTO> originalDataList = findByBudgetCode(originalBudgetCode);

            if (originalDataList.isEmpty()) {
                log.info("原预算编号 {} 没有导入数据，无需复制", originalBudgetCode);
                return 0;
            }

            // 2. 复制数据并更新预算编号
            List<CenterCostImportDTO> newDataList = originalDataList.stream()
                    .map(originalData -> CenterCostImportDTO.builder()
                            .budgetCode(newBudgetCode)  // 更新为新预算编号
                            .center(originalData.getCenter())
                            .templateType(originalData.getTemplateType())
                            .subjectCode(originalData.getSubjectCode())
                            .subjectName(originalData.getSubjectName())
                            .subjectDescription(originalData.getSubjectDescription())
                            .costAmount(originalData.getCostAmount())
                            .remark(originalData.getRemark())
                            .build())
                    .collect(Collectors.toList());

            // 3. 批量保存复制的数据
            int savedCount = saveBatch(newDataList);

            log.info("复制间接成本导入数据完成，原预算编号: {}, 新预算编号: {}, 复制数量: {}",
                    originalBudgetCode, newBudgetCode, savedCount);

            return savedCount;

        } catch (Exception e) {
            log.error("复制间接成本导入数据失败，原预算编号: {}, 新预算编号: {}",
                    originalBudgetCode, newBudgetCode, e);
            throw new RuntimeException("复制导入数据失败：" + e.getMessage(), e);
        }
    }

    @Override
    public List<CenterCostSubjectSummaryDTO> querySubjectSummaryByBudgetCodeAndTemplateType(String budgetCode, String templateType) {
        log.info("开始查询科目汇总数据，预算编码: {}, 模版类型: {}", budgetCode, templateType);

        try {
            // 调用Mapper的科目汇总查询方法
            List<CenterCostSubjectSummaryDTO> result = centerCostImportMapper.selectSubjectSummaryByBudgetCodeAndTemplateType(budgetCode, templateType);

            log.info("查询科目汇总数据完成，预算编码: {}, 模版类型: {}, 记录数: {}", budgetCode, templateType, result.size());
            return result;

        } catch (Exception e) {
            log.error("查询科目汇总数据失败，预算编码: {}, 模版类型: {}", budgetCode, templateType, e);
            throw new RuntimeException("查询科目汇总数据失败：" + e.getMessage(), e);
        }
    }

    /**
     * 将DTO转换为Infrastructure层的Entity
     * 
     * @param dto 领域传输对象
     * @return Infrastructure层实体
     */
    private CostAnnualBudgetCenterCostImport convertToEntity(CenterCostImportDTO dto) {
        CostAnnualBudgetCenterCostImport entity = new CostAnnualBudgetCenterCostImport();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    /**
     * 将Infrastructure层的Entity转换为DTO
     * 
     * @param entity Infrastructure层实体
     * @return 领域传输对象
     */
    private CenterCostImportDTO convertToDTO(CostAnnualBudgetCenterCostImport entity) {
        CenterCostImportDTO dto = new CenterCostImportDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
}
