package com.cdkit.modules.cm.infrastructure.budget.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostQuarterlyBudgetRevenueDetail;

import java.util.List;

/**
 * @Description: 季度预算收入明细表
 * @Author: cdkit-boot
 * @Date:   2025-08-12
 * @Version: V1.0
 */
public interface ICostQuarterlyBudgetRevenueDetailService extends IService<CostQuarterlyBudgetRevenueDetail> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<CostQuarterlyBudgetRevenueDetail>
	 */
	public List<CostQuarterlyBudgetRevenueDetail> selectByMainId(String mainId);
}
