package com.cdkit.modules.cm.infrastructure.budget.service.impl;


import com.cdkit.modules.cm.infrastructure.budget.entity.CostQuarterlyBudgetCompMageIndirectCost;
import com.cdkit.modules.cm.infrastructure.budget.mapper.CostQuarterlyBudgetCompMageIndirectCostMapper;
import com.cdkit.modules.cm.infrastructure.budget.service.ICostQuarterlyBudgetCompMageIndirectCostService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 季度预算-预算科目明细综合管理间接成本表
 * @Author: cdkit-boot
 * @Date:   2025-08-12
 * @Version: V1.0
 */
@Service
public class CostQuarterlyBudgetCompMageIndirectCostServiceImpl extends ServiceImpl<CostQuarterlyBudgetCompMageIndirectCostMapper, CostQuarterlyBudgetCompMageIndirectCost> implements ICostQuarterlyBudgetCompMageIndirectCostService {
	
	@Autowired
	private CostQuarterlyBudgetCompMageIndirectCostMapper costQuarterlyBudgetCompMageIndirectCostMapper;
	
	@Override
	public List<CostQuarterlyBudgetCompMageIndirectCost> selectByMainId(String mainId) {
		return costQuarterlyBudgetCompMageIndirectCostMapper.selectByMainId(mainId);
	}
}
