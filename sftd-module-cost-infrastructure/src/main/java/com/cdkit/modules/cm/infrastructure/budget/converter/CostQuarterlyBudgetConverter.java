package com.cdkit.modules.cm.infrastructure.budget.converter;

import com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetEntity;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostQuarterlyBudget;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * 季度预算转换器
 * 负责领域实体和基础设施实体之间的转换
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Component
public class CostQuarterlyBudgetConverter {

    /**
     * 领域实体转换为基础设施实体
     *
     * @param domainEntity 领域实体
     * @return 基础设施实体
     */
    public CostQuarterlyBudget toInfrastructure(CostQuarterlyBudgetEntity domainEntity) {
        if (domainEntity == null) {
            return null;
        }

        CostQuarterlyBudget infraEntity = new CostQuarterlyBudget();
        BeanUtils.copyProperties(domainEntity, infraEntity);
        return infraEntity;
    }

    /**
     * 基础设施实体转换为领域实体
     *
     * @param infraEntity 基础设施实体
     * @return 领域实体
     */
    public CostQuarterlyBudgetEntity toDomain(CostQuarterlyBudget infraEntity) {
        if (infraEntity == null) {
            return null;
        }

        CostQuarterlyBudgetEntity domainEntity = new CostQuarterlyBudgetEntity();
        BeanUtils.copyProperties(infraEntity, domainEntity);
        return domainEntity;
    }
}
