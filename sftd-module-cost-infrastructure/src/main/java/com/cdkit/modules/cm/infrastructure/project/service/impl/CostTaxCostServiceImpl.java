package com.cdkit.modules.cm.infrastructure.project.service.impl;


import com.cdkit.modules.cm.infrastructure.project.entity.CostTaxCost;
import com.cdkit.modules.cm.infrastructure.project.mapper.CostTaxCostMapper;
import com.cdkit.modules.cm.infrastructure.project.service.ICostTaxCostService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 税金及附加明细
 * @Author: cdkit-boot
 * @Date:   2025-07-18
 * @Version: V1.0
 */
@Service
public class CostTaxCostServiceImpl extends ServiceImpl<CostTaxCostMapper, CostTaxCost> implements ICostTaxCostService {
	
	@Autowired
	private CostTaxCostMapper costTaxCostMapper;
	
	@Override
	public List<CostTaxCost> selectByMainId(String mainId) {
		return costTaxCostMapper.selectByMainId(mainId);
	}
}
