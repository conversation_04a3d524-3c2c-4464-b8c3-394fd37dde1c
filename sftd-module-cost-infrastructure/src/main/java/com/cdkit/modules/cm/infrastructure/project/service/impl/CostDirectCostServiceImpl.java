package com.cdkit.modules.cm.infrastructure.project.service.impl;


import com.cdkit.modules.cm.infrastructure.project.entity.CostDirectCost;
import com.cdkit.modules.cm.infrastructure.project.mapper.CostDirectCostMapper;
import com.cdkit.modules.cm.infrastructure.project.service.ICostDirectCostService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 直接成本明细
 * @Author: cdkit-boot
 * @Date:   2025-07-18
 * @Version: V1.0
 */
@Service
public class CostDirectCostServiceImpl extends ServiceImpl<CostDirectCostMapper, CostDirectCost> implements ICostDirectCostService {
	
	@Autowired
	private CostDirectCostMapper costDirectCostMapper;
	
	@Override
	public List<CostDirectCost> selectByMainId(String mainId) {
		return costDirectCostMapper.selectByMainId(mainId);
	}
}
