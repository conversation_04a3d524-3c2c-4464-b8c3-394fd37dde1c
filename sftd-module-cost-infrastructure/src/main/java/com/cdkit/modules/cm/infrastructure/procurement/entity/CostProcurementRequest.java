package com.cdkit.modules.cm.infrastructure.procurement.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.cdkitframework.poi.excel.annotation.Excel;
import com.cdkit.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;


/**
 * @Description: 采购申请
 * @Author: sunhzh
 * @Date:   2025-07-24
 * @Version: V1.0
 */
@Schema(description="cost_procurement_request对象")
@Data
@TableName("cost_procurement_request")
public class CostProcurementRequest implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键ID*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private String id;
	/**采购申请编号*/
	@Excel(name = "采购申请编号", width = 15)
    @Schema(description = "采购申请编号")
    private String requestCode;
	/**物料编码*/
	@Excel(name = "物料编码", width = 15)
    @Schema(description = "物料编码")
    private String materialCode;
	/**物料名称*/
	@Excel(name = "物料名称", width = 15)
    @Schema(description = "物料名称")
    private String materialName;
	/**审批状态：待提交、审批中、已通过*/
	@Excel(name = "审批状态：待提交、审批中、已通过", width = 15, dicCode = "cost_approval_status")
    @Dict(dicCode = "cost_approval_status")
    @Schema(description = "审批状态：待提交、审批中、已通过")
    private String approvalStatus;
	/**本次采购量*/
	@Excel(name = "本次采购量", width = 15)
    @Schema(description = "本次采购量")
    private Integer purchaseQuantity;
	/**单位*/
	@Excel(name = "单位", width = 15)
    @Schema(description = "单位")
    private String unit;
	/**单价*/
	@Excel(name = "单价", width = 15)
    @Schema(description = "单价")
    private java.math.BigDecimal unitPrice;
	/**含税总价*/
	@Excel(name = "含税总价", width = 15)
    @Schema(description = "含税总价")
    private java.math.BigDecimal totalAmountInclTax;
	/**不含税总价*/
	@Excel(name = "不含税总价", width = 15)
    @Schema(description = "不含税总价")
    private java.math.BigDecimal totalAmountExclTax;
	/**税额*/
	@Excel(name = "税额", width = 15)
    @Schema(description = "税额")
    private java.math.BigDecimal taxAmount;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @Schema(description = "备注")
    private String remark;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**租户id*/
	@Excel(name = "租户id", width = 15)
    @Schema(description = "租户id")
    private Integer tenantId;
	/**删除标识 0:未删除 1:删除*/
	@Excel(name = "删除标识 0:未删除 1:删除", width = 15)
    @Schema(description = "删除标识 0:未删除 1:删除")
    @TableLogic
    private Integer delFlag;
	/**所属部门*/
    @Schema(description = "所属部门")
    private String sysOrgCode;
}
