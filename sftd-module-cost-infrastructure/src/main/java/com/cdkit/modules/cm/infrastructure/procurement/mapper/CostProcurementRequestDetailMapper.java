package com.cdkit.modules.cm.infrastructure.procurement.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementRequestDetail;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 采购申请明细
 * @Author: sunhzh
 * @Date:   2025-07-24
 * @Version: V1.0
 */
public interface CostProcurementRequestDetailMapper extends BaseMapper<CostProcurementRequestDetail> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<CostProcurementRequestDetail>
   */
	public List<CostProcurementRequestDetail> selectByMainId(@Param("mainId") String mainId);
}
