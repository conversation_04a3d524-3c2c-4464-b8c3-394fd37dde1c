package com.cdkit.modules.cm.infrastructure.adapters.plm;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;

import com.cdkit.modules.cm.domain.gateway.plm.ProductFileGateway;
import com.cdkit.modules.cm.domain.gateway.plm.entity.ProductFileTreeEntity;
import com.cdkit.modules.cm.domain.gateway.plm.entity.ProductRecipeInfo;
import com.cdkit.plm.api.IPlmBaseApi;
import com.cdkit.plm.vo.ProductFileVO;
import com.cdkit.plm.vo.ProductTreeDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 产品档案外部服务实现类
 * 调用外部产品档案模块的Feign接口
 * <AUTHOR>
 * @date 2025/07/18
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ProductFileFeignAdapter implements ProductFileGateway {

    private final IPlmBaseApi plmBaseApi;

    @Override
    public List<ProductFileTreeEntity> getProductFileTree(String productCode) {
        try {
            log.info("调用IPlmBaseApi.getProductFile - productCode: {}", productCode);

            if (StrUtil.isEmpty(productCode)) {
                log.warn("产品编码为空，返回空列表");
                return new ArrayList<>();
            }

            // 调用PLM的getProductFile接口，直接返回ProductFileVO
            ProductFileVO productFile = plmBaseApi.getProductFile(productCode);

            if (productFile == null) {
                log.warn("调用PLM接口返回数据为空 - productCode: {}", productCode);
                return new ArrayList<>();
            }

            if (productFile.getProductBom() != null) {
                // 转换ProductTreeDTO为ProductFileTreeEntity
                ProductFileTreeEntity rootEntity = convertToEntity(productFile.getProductBom());
                List<ProductFileTreeEntity> treeList = new ArrayList<>();
                treeList.add(rootEntity);
                log.info("成功获取产品档案树结构 - productCode: {}", productCode);
                return treeList;
            }

            log.warn("PLM返回的产品BOM为空 - productCode: {}", productCode);
            return new ArrayList<>();

        } catch (Exception e) {
            log.error("调用IPlmBaseApi.getProductFile失败 - productCode: {}", productCode, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<String> getLastNodeMaterialCodes(String productCode) {
        try {
            log.info("获取产品档案树最后节点物料编码 - productCode: {}", productCode);

            List<ProductFileTreeEntity> treeList = getProductFileTree(productCode);
            List<String> materialCodes = new ArrayList<>();

            if (CollectionUtil.isNotEmpty(treeList)) {
                // 遍历树结构，收集所有叶子节点的物料编码
                for (ProductFileTreeEntity tree : treeList) {
                    collectLastNodeMaterialCodes(tree, materialCodes);
                }
            }

            log.info("成功获取{}个最后节点物料编码 - productCode: {}", materialCodes.size(), productCode);
            return materialCodes;

        } catch (Exception e) {
            log.error("获取最后节点物料编码失败 - productCode: {}", productCode, e);
            return new ArrayList<>();
        }
    }

    @Override
    public ProductRecipeInfo getProductRecipeInfo(String productCode) {
        try {
            log.info("获取产品配方信息 - productCode: {}", productCode);

            List<ProductFileTreeEntity> treeList = getProductFileTree(productCode);

            if (CollectionUtil.isEmpty(treeList)) {
                log.warn("产品档案树为空 - productCode: {}", productCode);
                return null;
            }

            // 获取第一层信息（根节点）
            ProductFileTreeEntity firstLevel = treeList.get(0);

            // 收集最底层物料信息
            List<ProductFileTreeEntity> bottomLevelMaterials = new ArrayList<>();
            List<String> bottomLevelMaterialCodes = new ArrayList<>();

            collectBottomLevelMaterials(firstLevel, bottomLevelMaterials, bottomLevelMaterialCodes);

            // 构建配方信息
            ProductRecipeInfo recipeInfo = new ProductRecipeInfo()
                    .setRecipeName(firstLevel.getName()) // 第一层的name作为配方名称
                    .setRecipeCode(firstLevel.getCode()) // 第一层的materialCode作为配方编号
                    .setFirstLevelInfo(firstLevel)
                    .setBottomLevelMaterials(bottomLevelMaterials)
                    .setBottomLevelMaterialCodes(bottomLevelMaterialCodes);

            log.info("成功获取产品配方信息 - productCode: {}, 配方名称: {}, 配方编号: {}, 底层物料数量: {}",
                    productCode, recipeInfo.getRecipeName(), recipeInfo.getRecipeCode(), bottomLevelMaterials.size());

            return recipeInfo;

        } catch (Exception e) {
            log.error("获取产品配方信息失败 - productCode: {}", productCode, e);
            return null;
        }
    }

    /**
     * 转换PLM的ProductTreeDTO为领域实体ProductFileTreeEntity
     *
     * @param plmProductTree PLM的产品树DTO
     * @return 领域实体
     */
    private ProductFileTreeEntity convertToEntity(ProductTreeDTO plmProductTree) {
        if (plmProductTree == null) {
            return null;
        }

        ProductFileTreeEntity entity = BeanUtil.copyProperties(plmProductTree, ProductFileTreeEntity.class);

        // 转换子节点
        if (CollectionUtil.isNotEmpty(plmProductTree.getChildren())) {
            List<ProductFileTreeEntity> childrenEntities = new ArrayList<>();
            for (ProductTreeDTO child : plmProductTree.getChildren()) {
                ProductFileTreeEntity childEntity = convertToEntity(child);
                if (childEntity != null) {
                    childrenEntities.add(childEntity);
                }
            }
            entity.setChildren(childrenEntities);
        }

        return entity;
    }

    /**
     * 递归收集树结构中所有叶子节点的物料编码
     *
     * @param node 当前节点
     * @param materialCodes 物料编码列表
     */
    private void collectLastNodeMaterialCodes(ProductFileTreeEntity node, List<String> materialCodes) {
        if (node == null) {
            return;
        }

        // 如果是叶子节点且有物料编码，则添加到列表中
        if (node.isLastNode() && StrUtil.isNotEmpty(node.getMaterialCode())) {
            materialCodes.add(node.getMaterialCode());
            log.debug("收集到叶子节点物料编码: {}", node.getMaterialCode());
        }

        // 递归处理子节点
        if (CollectionUtil.isNotEmpty(node.getChildren())) {
            for (ProductFileTreeEntity child : node.getChildren()) {
                collectLastNodeMaterialCodes(child, materialCodes);
            }
        }
    }

    /**
     * 递归收集树结构中所有叶子节点的完整物料信息
     *
     * @param node 当前节点
     * @param bottomLevelMaterials 底层物料实体列表
     * @param bottomLevelMaterialCodes 底层物料编码列表
     */
    private void collectBottomLevelMaterials(ProductFileTreeEntity node,
                                           List<ProductFileTreeEntity> bottomLevelMaterials,
                                           List<String> bottomLevelMaterialCodes) {
        if (node == null) {
            return;
        }

        // 如果是叶子节点，则添加到列表中
        if (node.isLastNode()) {
            bottomLevelMaterials.add(node);
            if (StrUtil.isNotEmpty(node.getMaterialCode())) {
                bottomLevelMaterialCodes.add(node.getMaterialCode());
            }
            log.debug("收集到底层物料: {} - {}", node.getMaterialCode(), node.getMaterialName());
        }

        // 递归处理子节点
        if (CollectionUtil.isNotEmpty(node.getChildren())) {
            for (ProductFileTreeEntity child : node.getChildren()) {
                collectBottomLevelMaterials(child, bottomLevelMaterials, bottomLevelMaterialCodes);
            }
        }
    }
}
