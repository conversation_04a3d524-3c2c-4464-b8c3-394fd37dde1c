package com.cdkit.modules.cm.infrastructure.project.service.impl;


import com.cdkit.modules.cm.infrastructure.project.entity.CostProjectPlanDetail;
import com.cdkit.modules.cm.infrastructure.project.mapper.CostProjectPlanDetailMapper;
import com.cdkit.modules.cm.infrastructure.project.service.ICostProjectPlanDetailService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 项目计划明细
 * @Author: cdkit-boot
 * @Date:   2025-07-18
 * @Version: V1.0
 */
@Service
public class CostProjectPlanDetailServiceImpl extends ServiceImpl<CostProjectPlanDetailMapper, CostProjectPlanDetail> implements ICostProjectPlanDetailService {
	
	@Autowired
	private CostProjectPlanDetailMapper costProjectPlanDetailMapper;
	
	@Override
	public List<CostProjectPlanDetail> selectByMainId(String mainId) {
		return costProjectPlanDetailMapper.selectByMainId(mainId);
	}
}
