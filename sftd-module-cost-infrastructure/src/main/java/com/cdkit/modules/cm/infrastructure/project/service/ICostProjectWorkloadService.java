package com.cdkit.modules.cm.infrastructure.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.cm.infrastructure.project.entity.CostProjectWorkload;

import java.util.List;

/**
 * @Description: 工作量动态
 * @Author: cdkit-boot
 * @Date:   2025-07-14
 * @Version: V1.0
 */
public interface ICostProjectWorkloadService extends IService<CostProjectWorkload> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<CostProjectWorkload>
	 */
	public List<CostProjectWorkload> selectByMainId(String mainId);
}
