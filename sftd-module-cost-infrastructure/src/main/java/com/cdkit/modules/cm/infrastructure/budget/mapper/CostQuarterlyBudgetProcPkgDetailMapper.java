package com.cdkit.modules.cm.infrastructure.budget.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostQuarterlyBudgetProcPkgDetail;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 采办包明细表
 * @Author: cdkit-boot
 * @Date:   2025-08-12
 * @Version: V1.0
 */
public interface CostQuarterlyBudgetProcPkgDetailMapper extends BaseMapper<CostQuarterlyBudgetProcPkgDetail> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<CostQuarterlyBudgetProcPkgDetail>
   */
	public List<CostQuarterlyBudgetProcPkgDetail> selectByMainId(@Param("mainId") String mainId);
}
