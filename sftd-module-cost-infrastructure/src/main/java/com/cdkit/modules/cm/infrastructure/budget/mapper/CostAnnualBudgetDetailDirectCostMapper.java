package com.cdkit.modules.cm.infrastructure.budget.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostAnnualBudgetDetailDirectCost;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 年度总预算项目直接成本明细
 * @Author: cdkit-boot
 * @Date:   2025-08-04
 * @Version: V1.0
 */
public interface CostAnnualBudgetDetailDirectCostMapper extends BaseMapper<CostAnnualBudgetDetailDirectCost> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<CostAnnualBudgetDetailDirectCost>
   */
	public List<CostAnnualBudgetDetailDirectCost> selectByMainId(@Param("mainId") String mainId);
}
