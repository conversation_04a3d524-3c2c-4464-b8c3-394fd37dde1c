package com.cdkit.modules.cm.infrastructure.project.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.cdkitframework.poi.excel.annotation.Excel;
import com.cdkit.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;


/**
 * @Description: 项目计划
 * @Author: cdkit-boot
 * @Date:   2025-07-18
 * @Version: V1.0
 */
@Schema(description="cost_project_plan对象")
@Data
@TableName("cost_project_plan")
public class CostProjectPlan implements Serializable {
    private static final long serialVersionUID = 1L;

	/**UUID主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "UUID主键")
    private String id;
	/**计划编号(JH+8位日期+3位流水)*/
	@Excel(name = "计划编号(JH+8位日期+3位流水)", width = 15)
    @Schema(description = "计划编号(JH+8位日期+3位流水)")
    private String planCode;
	/**计划名称*/
	@Excel(name = "计划名称", width = 15)
    @Schema(description = "计划名称")
    private String planName;
    /**关联父计划id*/
    @Excel(name = "关联父计划id", width = 15)
    @Schema(description = "关联父计划id")
    private String parentPlanId;
    /**年度预算ID*/
    @Excel(name = "年度预算ID", width = 15)
    @Schema(description = "年度预算ID")
    private String annualBudgetId;
    /**项目类型（market_project市场项目、non_market_project非市场项目）*/
    @Excel(name = "项目类型", width = 15, dicCode = "cost_project_type")
    @Dict(dicCode = "cost_project_type")
    @Schema(description = "项目类型（market_project市场项目、non_market_project非市场项目）")
    private String projectType;
	/**状态(PENDING_SUBMIT/APPROVING/LOCKED)*/
	@Excel(name = "状态(PENDING_SUBMIT/APPROVING/LOCKED)", width = 15, dicCode = "cost_project_plan_status")
    @Dict(dicCode = "cost_project_plan_status")
    @Schema(description = "状态(PENDING_SUBMIT/APPROVING/LOCKED)")
    private String projectPlanStatus;
	/**项目编号*/
	@Excel(name = "项目编号", width = 15)
    @Schema(description = "项目编号")
    private String projectCode;
	/**项目名称*/
	@Excel(name = "项目名称", width = 15)
    @Schema(description = "项目名称")
    private String projectName;
	/**计划类型(年度计划/季度计划 ANNUAL/QUARTERLY)*/
	@Excel(name = "计划类型(年度计划/季度计划 ANNUAL/QUARTERLY)", width = 15, dicCode = "cost_plan_type")
    @Dict(dicCode = "cost_plan_type")
    @Schema(description = "计划类型(年度计划/季度计划 ANNUAL/QUARTERLY)")
    private String planType;
	/**中心*/
	@Excel(name = "中心", width = 15)
    @Schema(description = "中心")
    private String center;
	/**项目组*/
	@Excel(name = "项目组", width = 15)
    @Schema(description = "项目组")
    private String projectGroup;
	/**合同模式*/
	@Excel(name = "合同模式", width = 15, dicCode = "cost_contact_mode")
    @Dict(dicCode = "cost_contact_mode")
    @Schema(description = "合同模式")
    private String contractMode;
	/**合同编号*/
	@Excel(name = "合同编号", width = 15)
    @Schema(description = "合同编号")
    private String contractCode;
	/**合同名称*/
	@Excel(name = "合同名称", width = 15)
    @Schema(description = "合同名称")
    private String contractName;
	/**合同/预估收入(税后万元)*/
	@Excel(name = "合同/预估收入(税后万元)", width = 15)
    @Schema(description = "合同/预估收入(税后万元)")
    private java.math.BigDecimal contractRevenue;
	/**直接成本小计*/
	@Excel(name = "直接成本小计", width = 15)
    @Schema(description = "直接成本小计")
    private java.math.BigDecimal directCostTotal;
	/**其他成本小计*/
	@Excel(name = "其他成本小计", width = 15)
    @Schema(description = "其他成本小计")
    private java.math.BigDecimal otherCostTotal;
	/**税金及附加小计*/
	@Excel(name = "税金及附加小计", width = 15)
    @Schema(description = "税金及附加小计")
    private java.math.BigDecimal taxCostTotal;
	/**成本总计*/
	@Excel(name = "成本总计", width = 15)
    @Schema(description = "成本总计")
    private java.math.BigDecimal costTotal;
	/**项目利润(万元)*/
	@Excel(name = "项目利润(万元)", width = 15)
    @Schema(description = "项目利润(万元)")
    private java.math.BigDecimal projectProfit;
	/**利润率(%)*/
	@Excel(name = "利润率(%)", width = 15)
    @Schema(description = "利润率(%)")
    private java.math.BigDecimal profitMargin;
    /**
     * 工作流绑定wiid
     */
    @Schema(description = "工作流绑定wiid")
    private String wiid;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**租户ID*/
	@Excel(name = "租户ID", width = 15)
    @Schema(description = "租户ID")
    private Integer tenantId;
	/**删除标识 0:未删除 1:删除*/
	@Excel(name = "删除标识 0:未删除 1:删除", width = 15)
    @Schema(description = "删除标识 0:未删除 1:删除")
    @TableLogic
    private Integer delFlag;
	/**所属部门代码*/
    @Schema(description = "所属部门代码")
    private String sysOrgCode;
}
