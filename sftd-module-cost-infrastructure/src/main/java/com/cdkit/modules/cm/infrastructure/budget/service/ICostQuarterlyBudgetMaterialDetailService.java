package com.cdkit.modules.cm.infrastructure.budget.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostQuarterlyBudgetMaterialDetail;

import java.util.List;

/**
 * @Description: 原材料明细表
 * @Author: cdkit-boot
 * @Date:   2025-08-12
 * @Version: V1.0
 */
public interface ICostQuarterlyBudgetMaterialDetailService extends IService<CostQuarterlyBudgetMaterialDetail> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<CostQuarterlyBudgetMaterialDetail>
	 */
	public List<CostQuarterlyBudgetMaterialDetail> selectByMainId(String mainId);
}
