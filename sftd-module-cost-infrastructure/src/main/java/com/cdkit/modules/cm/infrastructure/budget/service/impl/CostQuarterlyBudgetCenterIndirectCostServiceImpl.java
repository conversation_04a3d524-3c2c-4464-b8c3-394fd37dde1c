package com.cdkit.modules.cm.infrastructure.budget.service.impl;


import com.cdkit.modules.cm.infrastructure.budget.entity.CostQuarterlyBudgetCenterIndirectCost;
import com.cdkit.modules.cm.infrastructure.budget.mapper.CostQuarterlyBudgetCenterIndirectCostMapper;
import com.cdkit.modules.cm.infrastructure.budget.service.ICostQuarterlyBudgetCenterIndirectCostService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 季度预算-预算科目明细本中心间接成本表
 * @Author: cdkit-boot
 * @Date:   2025-08-12
 * @Version: V1.0
 */
@Service
public class CostQuarterlyBudgetCenterIndirectCostServiceImpl extends ServiceImpl<CostQuarterlyBudgetCenterIndirectCostMapper, CostQuarterlyBudgetCenterIndirectCost> implements ICostQuarterlyBudgetCenterIndirectCostService {
	
	@Autowired
	private CostQuarterlyBudgetCenterIndirectCostMapper costQuarterlyBudgetCenterIndirectCostMapper;
	
	@Override
	public List<CostQuarterlyBudgetCenterIndirectCost> selectByMainId(String mainId) {
		return costQuarterlyBudgetCenterIndirectCostMapper.selectByMainId(mainId);
	}
}
