package com.cdkit.modules.cm.infrastructure.budget.service.impl;


import com.cdkit.modules.cm.infrastructure.budget.entity.CostAnnualBudgetCenterCostImport;
import com.cdkit.modules.cm.infrastructure.budget.mapper.CostAnnualBudgetCenterCostImportMapper;
import com.cdkit.modules.cm.infrastructure.budget.service.ICostAnnualBudgetCenterCostImportService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 年度预算中心间接成本导入数据表
 * @Author: cdkit-boot
 * @Date:   2025-08-05
 * @Version: V1.0
 */
@Service
public class CostAnnualBudgetCenterCostImportServiceImpl extends ServiceImpl<CostAnnualBudgetCenterCostImportMapper, CostAnnualBudgetCenterCostImport> implements ICostAnnualBudgetCenterCostImportService {

}
