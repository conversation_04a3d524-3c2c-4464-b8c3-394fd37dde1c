package com.cdkit.modules.cm.infrastructure.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.cm.infrastructure.project.entity.CostProjectPlanDetail;

import java.util.List;

/**
 * @Description: 项目计划明细
 * @Author: cdkit-boot
 * @Date:   2025-07-18
 * @Version: V1.0
 */
public interface ICostProjectPlanDetailService extends IService<CostProjectPlanDetail> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<CostProjectPlanDetail>
	 */
	public List<CostProjectPlanDetail> selectByMainId(String mainId);
}
