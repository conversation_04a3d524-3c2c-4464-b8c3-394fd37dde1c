package com.cdkit.modules.cm.infrastructure.budget.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.cdkitframework.poi.excel.annotation.Excel;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.UnsupportedEncodingException;

/**
 * @Description: 季度预算收入明细表
 * @Author: cdkit-boot
 * @Date:   2025-08-12
 * @Version: V1.0
 */
@Schema(description="cost_quarterly_budget_revenue_detail对象")
@Data
@TableName("cost_quarterly_budget_revenue_detail")
public class CostQuarterlyBudgetRevenueDetail implements Serializable {
    private static final long serialVersionUID = 1L;

	/**UUID主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "UUID主键")
    private String id;
	/**关联季度预算主表ID*/
    @Schema(description = "关联季度预算主表ID")
    private String quarterlyBudgetId;
	/**产品名称*/
	@Excel(name = "产品名称", width = 15)
    @Schema(description = "产品名称")
    private String productName;
	/**单位*/
	@Excel(name = "单位", width = 15)
    @Schema(description = "单位")
    private String unit;
	/**合同类型(lump_sum-总价合同/rate-费率合同)*/
	@Excel(name = "合同类型(lump_sum-总价合同/rate-费率合同)", width = 15)
    @Schema(description = "合同类型(lump_sum-总价合同/rate-费率合同)")
    private String contractType;
	/**产品数量*/
	@Excel(name = "产品数量", width = 15)
    @Schema(description = "产品数量")
    private java.math.BigDecimal productQuantity;
	/**预计年处理量（水/油）*/
	@Excel(name = "预计年处理量（水/油）", width = 15)
    @Schema(description = "预计年处理量（水/油）")
    private java.math.BigDecimal estimatedAnnualProcessVolume;
	/**单价（元）*/
	@Excel(name = "单价（元）", width = 15)
    @Schema(description = "单价（元）")
    private java.math.BigDecimal unitPrice;
	/**年度应收预算（元）*/
	@Excel(name = "年度应收预算（元）", width = 15)
    @Schema(description = "年度应收预算（元）")
    private java.math.BigDecimal annualReceivableBudget;
	/**编制依据*/
	@Excel(name = "编制依据", width = 15)
    @Schema(description = "编制依据")
    private String compilationBasis;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @Schema(description = "备注")
    private String remark;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**租户ID*/
	@Excel(name = "租户ID", width = 15)
    @Schema(description = "租户ID")
    private Integer tenantId;
	/**删除标识 0:未删除 1:删除*/
	@Excel(name = "删除标识 0:未删除 1:删除", width = 15)
    @Schema(description = "删除标识 0:未删除 1:删除")
    @TableLogic
    private Integer delFlag;
	/**所属部门代码*/
    @Schema(description = "所属部门代码")
    private String sysOrgCode;
}
