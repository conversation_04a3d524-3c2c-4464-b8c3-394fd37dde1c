package com.cdkit.modules.cm.infrastructure.project.service.impl;


import com.cdkit.modules.cm.infrastructure.project.entity.*;
import com.cdkit.modules.cm.infrastructure.project.mapper.*;
import com.cdkit.modules.cm.infrastructure.project.service.ICostProjectPlanService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;
import java.util.Collection;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @Description: 项目计划
 * @Author: cdkit-boot
 * @Date:   2025-07-18
 * @Version: V1.0
 */
@Service
public class CostProjectPlanServiceImpl extends ServiceImpl<CostProjectPlanMapper, CostProjectPlan> implements ICostProjectPlanService {

	@Autowired
	private CostProjectPlanMapper costProjectPlanMapper;
	@Autowired
	private CostProjectPlanDetailMapper costProjectPlanDetailMapper;
	@Autowired
	private CostDirectCostMapper costDirectCostMapper;
	@Autowired
	private CostOtherCostMapper costOtherCostMapper;
	@Autowired
	private CostTaxCostMapper costTaxCostMapper;
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(CostProjectPlan costProjectPlan, List<CostProjectPlanDetail> costProjectPlanDetailList, List<CostDirectCost> costDirectCostList, List<CostOtherCost> costOtherCostList, List<CostTaxCost> costTaxCostList) {
		costProjectPlanMapper.insert(costProjectPlan);
		if(costProjectPlanDetailList!=null && costProjectPlanDetailList.size()>0) {
			for(CostProjectPlanDetail entity:costProjectPlanDetailList) {
				//外键设置
				entity.setPlanId(costProjectPlan.getId());
				costProjectPlanDetailMapper.insert(entity);
			}
		}
		if(costDirectCostList!=null && costDirectCostList.size()>0) {
			for(CostDirectCost entity:costDirectCostList) {
				//外键设置
				entity.setPlanId(costProjectPlan.getId());
				costDirectCostMapper.insert(entity);
			}
		}
		if(costOtherCostList!=null && costOtherCostList.size()>0) {
			for(CostOtherCost entity:costOtherCostList) {
				//外键设置
				entity.setPlanId(costProjectPlan.getId());
				costOtherCostMapper.insert(entity);
			}
		}
		if(costTaxCostList!=null && costTaxCostList.size()>0) {
			for(CostTaxCost entity:costTaxCostList) {
				//外键设置
				entity.setPlanId(costProjectPlan.getId());
				costTaxCostMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(CostProjectPlan costProjectPlan,List<CostProjectPlanDetail> costProjectPlanDetailList,List<CostDirectCost> costDirectCostList,List<CostOtherCost> costOtherCostList,List<CostTaxCost> costTaxCostList) {
		costProjectPlanMapper.updateById(costProjectPlan);
		
		//1.先删除子表数据
		costProjectPlanDetailMapper.deleteByMainId(costProjectPlan.getId());
		costDirectCostMapper.deleteByMainId(costProjectPlan.getId());
		costOtherCostMapper.deleteByMainId(costProjectPlan.getId());
		costTaxCostMapper.deleteByMainId(costProjectPlan.getId());
		
		//2.子表数据重新插入
		if(costProjectPlanDetailList!=null && costProjectPlanDetailList.size()>0) {
			for(CostProjectPlanDetail entity:costProjectPlanDetailList) {
				//外键设置
				entity.setPlanId(costProjectPlan.getId());
				costProjectPlanDetailMapper.insert(entity);
			}
		}
		if(costDirectCostList!=null && costDirectCostList.size()>0) {
			for(CostDirectCost entity:costDirectCostList) {
				//外键设置
				entity.setPlanId(costProjectPlan.getId());
				costDirectCostMapper.insert(entity);
			}
		}
		if(costOtherCostList!=null && costOtherCostList.size()>0) {
			for(CostOtherCost entity:costOtherCostList) {
				//外键设置
				entity.setPlanId(costProjectPlan.getId());
				costOtherCostMapper.insert(entity);
			}
		}
		if(costTaxCostList!=null && costTaxCostList.size()>0) {
			for(CostTaxCost entity:costTaxCostList) {
				//外键设置
				entity.setPlanId(costProjectPlan.getId());
				costTaxCostMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		costProjectPlanDetailMapper.deleteByMainId(id);
		costDirectCostMapper.deleteByMainId(id);
		costOtherCostMapper.deleteByMainId(id);
		costTaxCostMapper.deleteByMainId(id);
		costProjectPlanMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			costProjectPlanDetailMapper.deleteByMainId(id.toString());
			costDirectCostMapper.deleteByMainId(id.toString());
			costOtherCostMapper.deleteByMainId(id.toString());
			costTaxCostMapper.deleteByMainId(id.toString());
			costProjectPlanMapper.deleteById(id);
		}
	}

}