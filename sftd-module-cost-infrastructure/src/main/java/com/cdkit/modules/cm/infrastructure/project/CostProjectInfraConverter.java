package com.cdkit.modules.cm.infrastructure.project;

import cn.hutool.core.bean.BeanUtil;
import com.cdkit.modules.cm.domain.project.mode.entity.CostProjectEntity;
import com.cdkit.modules.cm.infrastructure.project.entity.CostProject;

import java.util.List;

/**
 * 项目基础设施转换器
 * <AUTHOR>
 * @date 2025/07/14
 */
public class CostProjectInfraConverter {

    /**
     * 领域实体转基础设施实体
     */
    public static CostProject convert(CostProjectEntity entity) {
        if (entity == null) {
            return null;
        }
        return BeanUtil.copyProperties(entity, CostProject.class);
    }

    /**
     * 基础设施实体转领域实体
     */
    public static CostProjectEntity convert(CostProject entity) {
        if (entity == null) {
            return null;
        }
        return BeanUtil.copyProperties(entity, CostProjectEntity.class);
    }

    /**
     * 基础设施实体列表转领域实体列表
     */
    public static List<CostProjectEntity> convertList(List<CostProject> entityList) {
        return BeanUtil.copyToList(entityList, CostProjectEntity.class);
    }

    /**
     * 领域实体列表转基础设施实体列表
     */
    public static List<CostProject> convertToInfraList(List<CostProjectEntity> entityList) {
        return BeanUtil.copyToList(entityList, CostProject.class);
    }
}
