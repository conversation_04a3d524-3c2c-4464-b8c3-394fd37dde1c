package com.cdkit.modules.cm.infrastructure.budget.mapper;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostAnnualBudgetDetail;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 项目年度预算
 * @Author: sunhzh
 * @Date:   2025-07-30
 * @Version: V1.0
 */
public interface CostAnnualBudgetDetailMapper extends BaseMapper<CostAnnualBudgetDetail> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<CostAnnualBudgetDetail>
   */
	public List<CostAnnualBudgetDetail> selectByMainId(@Param("mainId") String mainId);

	/**
	 * 根据项目编号查询项目年度预算信息
	 * 关联查询年度预算主表和明细表，获取完整的预算信息
	 *
	 * @param projectCode 项目编号
	 * @return 项目年度预算信息列表
	 */
	public List<Map<String, Object>> selectProjectBudgetByProjectCode(@Param("projectCode") String projectCode);
}
