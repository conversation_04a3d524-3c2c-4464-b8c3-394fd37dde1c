package com.cdkit.modules.cm.infrastructure.budget.service.impl;

import com.cdkit.modules.cm.infrastructure.budget.entity.CostQuarterlyBudgetRevenueDetail;
import com.cdkit.modules.cm.infrastructure.budget.mapper.CostQuarterlyBudgetRevenueDetailMapper;
import com.cdkit.modules.cm.infrastructure.budget.service.ICostQuarterlyBudgetRevenueDetailService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 季度预算收入明细表
 * @Author: cdkit-boot
 * @Date:   2025-08-12
 * @Version: V1.0
 */
@Service
public class CostQuarterlyBudgetRevenueDetailServiceImpl extends ServiceImpl<CostQuarterlyBudgetRevenueDetailMapper, CostQuarterlyBudgetRevenueDetail> implements ICostQuarterlyBudgetRevenueDetailService {
	
	@Autowired
	private CostQuarterlyBudgetRevenueDetailMapper costQuarterlyBudgetRevenueDetailMapper;
	
	@Override
	public List<CostQuarterlyBudgetRevenueDetail> selectByMainId(String mainId) {
		return costQuarterlyBudgetRevenueDetailMapper.selectByMainId(mainId);
	}
}
