package com.cdkit.modules.cm.infrastructure.budget.service.impl;


import com.cdkit.modules.cm.infrastructure.budget.entity.CostQuarterlyBudgetNonOptCenterIndirectCost;
import com.cdkit.modules.cm.infrastructure.budget.mapper.CostQuarterlyBudgetNonOptCenterIndirectCostMapper;
import com.cdkit.modules.cm.infrastructure.budget.service.ICostQuarterlyBudgetNonOptCenterIndirectCostService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 季度预算-预算科目明细非经营中心间接成本表
 * @Author: cdkit-boot
 * @Date:   2025-08-12
 * @Version: V1.0
 */
@Service
public class CostQuarterlyBudgetNonOptCenterIndirectCostServiceImpl extends ServiceImpl<CostQuarterlyBudgetNonOptCenterIndirectCostMapper, CostQuarterlyBudgetNonOptCenterIndirectCost> implements ICostQuarterlyBudgetNonOptCenterIndirectCostService {
	
	@Autowired
	private CostQuarterlyBudgetNonOptCenterIndirectCostMapper costQuarterlyBudgetNonOptCenterIndirectCostMapper;
	
	@Override
	public List<CostQuarterlyBudgetNonOptCenterIndirectCost> selectByMainId(String mainId) {
		return costQuarterlyBudgetNonOptCenterIndirectCostMapper.selectByMainId(mainId);
	}
}
