package com.cdkit.modules.cm.infrastructure.project.service.impl;


import com.cdkit.modules.cm.infrastructure.project.entity.CostOtherCost;
import com.cdkit.modules.cm.infrastructure.project.mapper.CostOtherCostMapper;
import com.cdkit.modules.cm.infrastructure.project.service.ICostOtherCostService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 其他成本明细
 * @Author: cdkit-boot
 * @Date:   2025-07-18
 * @Version: V1.0
 */
@Service
public class CostOtherCostServiceImpl extends ServiceImpl<CostOtherCostMapper, CostOtherCost> implements ICostOtherCostService {
	
	@Autowired
	private CostOtherCostMapper costOtherCostMapper;
	
	@Override
	public List<CostOtherCost> selectByMainId(String mainId) {
		return costOtherCostMapper.selectByMainId(mainId);
	}
}
