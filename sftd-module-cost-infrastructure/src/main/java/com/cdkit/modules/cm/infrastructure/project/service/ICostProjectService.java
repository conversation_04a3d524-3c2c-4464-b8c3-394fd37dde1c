package com.cdkit.modules.cm.infrastructure.project.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.cm.infrastructure.project.entity.CostProject;
import com.cdkit.modules.cm.infrastructure.project.entity.CostProjectWorkload;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 市场项目台账
 * @Author: cdkit-boot
 * @Date:   2025-07-14
 * @Version: V1.0
 */
public interface ICostProjectService extends IService<CostProject> {

	/**
	 * 添加一对多
	 *
	 * @param costProject
	 * @param costProjectWorkloadList
	 */
	public void saveMain(CostProject costProject,List<CostProjectWorkload> costProjectWorkloadList) ;
	
	/**
	 * 修改一对多
	 *
   * @param costProject
   * @param costProjectWorkloadList
	 */
	public void updateMain(CostProject costProject,List<CostProjectWorkload> costProjectWorkloadList);
	
	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);
	
}
