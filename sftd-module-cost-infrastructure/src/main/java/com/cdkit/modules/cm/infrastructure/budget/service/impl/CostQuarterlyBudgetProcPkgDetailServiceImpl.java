package com.cdkit.modules.cm.infrastructure.budget.service.impl;


import com.cdkit.modules.cm.infrastructure.budget.entity.CostQuarterlyBudgetProcPkgDetail;
import com.cdkit.modules.cm.infrastructure.budget.mapper.CostQuarterlyBudgetProcPkgDetailMapper;
import com.cdkit.modules.cm.infrastructure.budget.service.ICostQuarterlyBudgetProcPkgDetailService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 采办包明细表
 * @Author: cdkit-boot
 * @Date:   2025-08-12
 * @Version: V1.0
 */
@Service
public class CostQuarterlyBudgetProcPkgDetailServiceImpl extends ServiceImpl<CostQuarterlyBudgetProcPkgDetailMapper, CostQuarterlyBudgetProcPkgDetail> implements ICostQuarterlyBudgetProcPkgDetailService {
	
	@Autowired
	private CostQuarterlyBudgetProcPkgDetailMapper costQuarterlyBudgetProcPkgDetailMapper;
	
	@Override
	public List<CostQuarterlyBudgetProcPkgDetail> selectByMainId(String mainId) {
		return costQuarterlyBudgetProcPkgDetailMapper.selectByMainId(mainId);
	}
}
