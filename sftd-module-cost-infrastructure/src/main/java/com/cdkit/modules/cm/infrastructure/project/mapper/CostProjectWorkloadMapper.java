package com.cdkit.modules.cm.infrastructure.project.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdkit.modules.cm.infrastructure.project.entity.CostProjectWorkload;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 工作量动态
 * @Author: cdkit-boot
 * @Date:   2025-07-14
 * @Version: V1.0
 */
public interface CostProjectWorkloadMapper extends BaseMapper<CostProjectWorkload> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<CostProjectWorkload>
   */
	public List<CostProjectWorkload> selectByMainId(@Param("mainId") String mainId);
}
