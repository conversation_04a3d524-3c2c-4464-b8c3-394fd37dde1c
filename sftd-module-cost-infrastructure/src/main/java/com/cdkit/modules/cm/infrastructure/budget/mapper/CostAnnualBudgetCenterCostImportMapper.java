package com.cdkit.modules.cm.infrastructure.budget.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.modules.cm.domain.budget.model.dto.CenterCostSubjectSummaryDTO;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostAnnualBudgetCenterCostImport;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 年度预算中心间接成本导入数据表
 * @Author: cdkit-boot
 * @Date:   2025-08-05
 * @Version: V1.0
 */
public interface CostAnnualBudgetCenterCostImportMapper extends BaseMapper<CostAnnualBudgetCenterCostImport> {

    /**
     * 根据预算编码和模版类型查询科目汇总数据
     * 按科目编码和名称分组，汇总金额
     *
     * @param budgetCode 预算编码
     * @param templateType 模版类型
     * @return 科目汇总列表
     */
    List<CenterCostSubjectSummaryDTO> selectSubjectSummaryByBudgetCodeAndTemplateType(
            @Param("budgetCode") String budgetCode,
            @Param("templateType") String templateType);
}
