package com.cdkit.modules.cm.infrastructure.budget.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostQuarterlyBudgetMaterialDetail;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 原材料明细表
 * @Author: cdkit-boot
 * @Date:   2025-08-12
 * @Version: V1.0
 */
public interface CostQuarterlyBudgetMaterialDetailMapper extends BaseMapper<CostQuarterlyBudgetMaterialDetail> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<CostQuarterlyBudgetMaterialDetail>
   */
	public List<CostQuarterlyBudgetMaterialDetail> selectByMainId(@Param("mainId") String mainId);
}
