package com.cdkit.modules.cm.infrastructure.budget.service.impl;


import com.cdkit.modules.cm.infrastructure.budget.entity.CostQuarterlyBudgetSubjectDirectCost;
import com.cdkit.modules.cm.infrastructure.budget.mapper.CostQuarterlyBudgetSubjectDirectCostMapper;
import com.cdkit.modules.cm.infrastructure.budget.service.ICostQuarterlyBudgetSubjectDirectCostService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 预算科目明细直接成本表
 * @Author: cdkit-boot
 * @Date:   2025-08-12
 * @Version: V1.0
 */
@Service
public class CostQuarterlyBudgetSubjectDirectCostServiceImpl extends ServiceImpl<CostQuarterlyBudgetSubjectDirectCostMapper, CostQuarterlyBudgetSubjectDirectCost> implements ICostQuarterlyBudgetSubjectDirectCostService {
	
	@Autowired
	private CostQuarterlyBudgetSubjectDirectCostMapper costQuarterlyBudgetSubjectDirectCostMapper;
	
	@Override
	public List<CostQuarterlyBudgetSubjectDirectCost> selectByMainId(String mainId) {
		return costQuarterlyBudgetSubjectDirectCostMapper.selectByMainId(mainId);
	}
}
