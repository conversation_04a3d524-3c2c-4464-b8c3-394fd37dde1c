package com.cdkit.modules.cm.infrastructure.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.cm.infrastructure.project.entity.CostTaxCost;

import java.util.List;

/**
 * @Description: 税金及附加明细
 * @Author: cdkit-boot
 * @Date:   2025-07-18
 * @Version: V1.0
 */
public interface ICostTaxCostService extends IService<CostTaxCost> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<CostTaxCost>
	 */
	public List<CostTaxCost> selectByMainId(String mainId);
}
