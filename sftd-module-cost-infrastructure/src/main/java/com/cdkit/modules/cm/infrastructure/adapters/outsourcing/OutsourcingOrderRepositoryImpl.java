package com.cdkit.modules.cm.infrastructure.adapters.outsourcing;

import com.cdkit.modules.cm.domain.gateway.outsourcing.OutsourcingExternalGateway;
import com.cdkit.modules.cm.domain.outsourcing.entity.OutsourcingOrderEntity;
import com.cdkit.modules.cm.domain.outsourcing.repository.OutsourcingOrderRepository;
import com.cdkit.modules.cm.domain.project.repository.CostProjectPlanRepository;
import com.cdkit.modules.cm.domain.project.mode.entity.CostProjectPlanEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 外委单据仓储实现
 * 通过外委外部服务网关调用外委服务
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class OutsourcingOrderRepositoryImpl implements OutsourcingOrderRepository {

    private final OutsourcingExternalGateway outsourcingExternalGateway;
    private final CostProjectPlanRepository costProjectPlanRepository;

    @Override
    public OutsourcingOrderEntity save(OutsourcingOrderEntity entity) {
        log.info("保存外委单据，单据ID: {}", entity.getId());
        // 通过外委服务保存，这里简化处理，实际应该调用addOutsourcingOrder
        return entity;
    }

    @Override
    public OutsourcingOrderEntity findById(String id) {
        log.info("查询外委单据，单据ID: {}", id);
        return outsourcingExternalGateway.checkOutsourcingOrderStatus(id);
    }

    @Override
    public List<OutsourcingOrderEntity> findByPlanId(String planId) {
        log.info("查询项目计划相关的外委单据，项目计划ID: {}", planId);

        // 根据planId获取planCode
        String planCode = getPlanCodeByPlanId(planId);
        if (!StringUtils.hasText(planCode)) {
            log.warn("未找到项目计划编码，项目计划ID: {}", planId);
            return List.of();
        }

        return outsourcingExternalGateway.queryBySourceBillNumberAll(planCode);
    }

    @Override
    public BigDecimal getTransferredAmountByPlanIdAndProduct(String planId, String productName) {
        log.info("查询产品已转量，项目计划ID: {}, 产品名称: {}", planId, productName);

        // 根据planId获取planCode
        String planCode = getPlanCodeByPlanId(planId);
        if (!StringUtils.hasText(planCode)) {
            log.warn("未找到项目计划编码，项目计划ID: {}", planId);
            return BigDecimal.ZERO;
        }

        // 通过外委服务查询，这里需要从批量查询结果中提取单个产品的数据
        Map<String, BigDecimal> resultMap = outsourcingExternalGateway
                .batchGetTransferredAmountByProducts(planCode, List.of(productName));

        return resultMap.getOrDefault(productName, BigDecimal.ZERO);
    }

    @Override
    public BigDecimal getTransferredAmountByPlanIdAndMaterial(String planId, String materialCode) {
        log.info("查询物料已转量，项目计划ID: {}, 物料编码: {}", planId, materialCode);

        // 根据planId获取planCode
        String planCode = getPlanCodeByPlanId(planId);
        if (!StringUtils.hasText(planCode)) {
            log.warn("未找到项目计划编码，项目计划ID: {}", planId);
            return BigDecimal.ZERO;
        }

        // 通过外委服务查询，这里需要从批量查询结果中提取单个物料的数据
        Map<String, BigDecimal> resultMap = outsourcingExternalGateway
                .batchGetTransferredAmountByMaterials(planCode, List.of(materialCode));

        return resultMap.getOrDefault(materialCode, BigDecimal.ZERO);
    }

    @Override
    public Map<String, BigDecimal> batchGetTransferredAmountByProducts(String planId, List<String> productNames) {
        log.info("批量查询产品已转量，项目计划ID: {}, 产品数量: {}", planId, productNames.size());

        // 根据planId获取planCode
        String planCode = getPlanCodeByPlanId(planId);
        if (!StringUtils.hasText(planCode)) {
            log.warn("未找到项目计划编码，项目计划ID: {}", planId);
            return Map.of();
        }

        return outsourcingExternalGateway.batchGetTransferredAmountByProducts(planCode, productNames);
    }

    @Override
    public Map<String, BigDecimal> batchGetTransferredAmountByMaterials(String planId, List<String> materialCodes) {
        log.info("批量查询物料已转量，项目计划ID: {}, 物料数量: {}", planId, materialCodes.size());

        // 根据planId获取planCode
        String planCode = getPlanCodeByPlanId(planId);
        if (!StringUtils.hasText(planCode)) {
            log.warn("未找到项目计划编码，项目计划ID: {}", planId);
            return Map.of();
        }

        return outsourcingExternalGateway.batchGetTransferredAmountByMaterials(planCode, materialCodes);
    }

    @Override
    public OutsourcingOrderEntity updateById(OutsourcingOrderEntity entity) {
        log.info("更新外委单据，单据ID: {}", entity.getId());
        // 外委服务通常不支持直接更新，这里预留接口
        return entity;
    }

    @Override
    public boolean deleteById(String id) {
        log.info("删除外委单据，单据ID: {}", id);
        return outsourcingExternalGateway.cancelOutsourcingOrder(id);
    }

    /**
     * 调用外委服务生成外委单据
     */
    public String generateOutsourcingOrder(String planId, List<OutsourcingOrderEntity> orderItems) {
        log.info("生成外委单据，项目计划ID: {}, 外委项目数量: {}", planId, orderItems.size());
        return outsourcingExternalGateway.addOutsourcingOrder(orderItems);
    }

    /**
     * 查询是否有关联外委单
     */
    public boolean hasRelatedOutsourcingOrders(String planId) {
        log.info("查询是否有关联外委单，项目计划ID: {}", planId);

        // 根据planId获取planCode
        String planCode = getPlanCodeByPlanId(planId);
        if (!StringUtils.hasText(planCode)) {
            log.warn("未找到项目计划编码，项目计划ID: {}", planId);
            return false;
        }

        List<OutsourcingOrderEntity> orders = outsourcingExternalGateway.queryBySourceBillNumberAll(planCode);
        return orders != null && !orders.isEmpty();
    }

    /**
     * 根据项目计划ID获取项目计划编码
     *
     * @param planId 项目计划ID
     * @return 项目计划编码（planCode）
     */
    private String getPlanCodeByPlanId(String planId) {
        if (!StringUtils.hasText(planId)) {
            return null;
        }

        try {
            CostProjectPlanEntity planEntity = costProjectPlanRepository.getDomainById(planId);
            if (planEntity != null) {
                String planCode = planEntity.getPlanCode();
                log.debug("获取项目计划编码成功，项目计划ID: {}, 项目计划编码: {}", planId, planCode);
                return planCode;
            } else {
                log.warn("未找到项目计划，项目计划ID: {}", planId);
                return null;
            }
        } catch (Exception e) {
            log.error("获取项目计划编码失败，项目计划ID: {}", planId, e);
            return null;
        }
    }
}
