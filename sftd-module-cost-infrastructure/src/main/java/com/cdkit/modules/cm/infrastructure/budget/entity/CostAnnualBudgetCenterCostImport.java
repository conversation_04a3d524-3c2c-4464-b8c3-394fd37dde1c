package com.cdkit.modules.cm.infrastructure.budget.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.cdkitframework.poi.excel.annotation.Excel;
import com.cdkit.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 年度预算中心间接成本导入数据表
 * @Author: cdkit-boot
 * @Date:   2025-08-05
 * @Version: V1.0
 */
@Data
@TableName("cost_annual_budget_center_cost_import")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="cost_annual_budget_center_cost_import对象")
public class CostAnnualBudgetCenterCostImport implements Serializable {
    private static final long serialVersionUID = 1L;

	/**UUID主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "UUID主键")
    private String id;
	/**总预算编号(关联年度总预算)*/
	@Excel(name = "总预算编号(关联年度总预算)", width = 15)
    @Schema(description = "总预算编号(关联年度总预算)")
    private String budgetCode;
	/**中心名称*/
	@Excel(name = "中心名称", width = 15)
    @Schema(description = "中心名称")
    private String center;
	/**成本类型()*/
	@Excel(name = "模版类型(本中心间接成本导入模板this_center_indirect_cost_template;非经营中心间接成本导入模板non_operational_center_indirect_cost_template;综合管理间接成本导入模板general_admin_indirect_cost_template)", width = 15)
    @Schema(description = "模版类型")
    private String templateType;
	/**预算科目编码*/
	@Excel(name = "预算科目编码", width = 15)
    @Schema(description = "预算科目编码")
    private String subjectCode;
	/**预算科目名称*/
	@Excel(name = "预算科目名称", width = 15)
    @Schema(description = "预算科目名称")
    private String subjectName;
    /**科目释义*/
    @Excel(name = "科目释义", width = 15)
    @Schema(description = "科目释义")
    private String subjectDescription;
	/**成本金额(元)*/
	@Excel(name = "成本金额(元)", width = 15)
    @Schema(description = "成本金额(元)")
    private BigDecimal costAmount;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @Schema(description = "备注")
    private String remark;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**租户ID*/
	@Excel(name = "租户ID", width = 15)
    @Schema(description = "租户ID")
    private Integer tenantId;
	/**删除标识 0:未删除 1:删除*/
	@Excel(name = "删除标识 0:未删除 1:删除", width = 15)
    @Schema(description = "删除标识 0:未删除 1:删除")
    @TableLogic
    private Integer delFlag;
	/**所属部门代码*/
    @Schema(description = "所属部门代码")
    private String sysOrgCode;
}
