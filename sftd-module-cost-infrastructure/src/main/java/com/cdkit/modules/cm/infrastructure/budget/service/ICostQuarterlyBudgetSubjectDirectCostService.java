package com.cdkit.modules.cm.infrastructure.budget.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostQuarterlyBudgetSubjectDirectCost;

import java.util.List;

/**
 * @Description: 预算科目明细直接成本表
 * @Author: cdkit-boot
 * @Date:   2025-08-12
 * @Version: V1.0
 */
public interface ICostQuarterlyBudgetSubjectDirectCostService extends IService<CostQuarterlyBudgetSubjectDirectCost> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<CostQuarterlyBudgetSubjectDirectCost>
	 */
	public List<CostQuarterlyBudgetSubjectDirectCost> selectByMainId(String mainId);
}
