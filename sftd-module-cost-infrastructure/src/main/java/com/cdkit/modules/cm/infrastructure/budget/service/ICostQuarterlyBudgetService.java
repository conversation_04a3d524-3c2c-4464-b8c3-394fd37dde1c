package com.cdkit.modules.cm.infrastructure.budget.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.cm.infrastructure.budget.entity.*;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 季度预算主表
 * @Author: cdkit-boot
 * @Date:   2025-08-12
 * @Version: V1.0
 */
public interface ICostQuarterlyBudgetService extends IService<CostQuarterlyBudget> {

	/**
	 * 添加一对多
	 *
	 * @param costQuarterlyBudget
	 * @param costQuarterlyBudgetProcPkgDetailList
	 * @param costQuarterlyBudgetMaterialDetailList
	 * @param costQuarterlyBudgetSubjectDirectCostList
	 * @param costQuarterlyBudgetCenterIndirectCostList
	 * @param costQuarterlyBudgetCompMageIndirectCostList
	 * @param costQuarterlyBudgetNonOptCenterIndirectCostList
	 * @param costQuarterlyBudgetRevenueDetailList
	 */
	public void saveMain(CostQuarterlyBudget costQuarterlyBudget, List<CostQuarterlyBudgetProcPkgDetail> costQuarterlyBudgetProcPkgDetailList, List<CostQuarterlyBudgetMaterialDetail> costQuarterlyBudgetMaterialDetailList, List<CostQuarterlyBudgetSubjectDirectCost> costQuarterlyBudgetSubjectDirectCostList, List<CostQuarterlyBudgetCenterIndirectCost> costQuarterlyBudgetCenterIndirectCostList, List<CostQuarterlyBudgetCompMageIndirectCost> costQuarterlyBudgetCompMageIndirectCostList, List<CostQuarterlyBudgetNonOptCenterIndirectCost> costQuarterlyBudgetNonOptCenterIndirectCostList, List<CostQuarterlyBudgetRevenueDetail> costQuarterlyBudgetRevenueDetailList) ;
	
	/**
	 * 修改一对多
	 *
   * @param costQuarterlyBudget
   * @param costQuarterlyBudgetProcPkgDetailList
   * @param costQuarterlyBudgetMaterialDetailList
   * @param costQuarterlyBudgetSubjectDirectCostList
   * @param costQuarterlyBudgetCenterIndirectCostList
   * @param costQuarterlyBudgetCompMageIndirectCostList
   * @param costQuarterlyBudgetNonOptCenterIndirectCostList
   * @param costQuarterlyBudgetRevenueDetailList
	 */
	public void updateMain(CostQuarterlyBudget costQuarterlyBudget,List<CostQuarterlyBudgetProcPkgDetail> costQuarterlyBudgetProcPkgDetailList,List<CostQuarterlyBudgetMaterialDetail> costQuarterlyBudgetMaterialDetailList,List<CostQuarterlyBudgetSubjectDirectCost> costQuarterlyBudgetSubjectDirectCostList,List<CostQuarterlyBudgetCenterIndirectCost> costQuarterlyBudgetCenterIndirectCostList,List<CostQuarterlyBudgetCompMageIndirectCost> costQuarterlyBudgetCompMageIndirectCostList,List<CostQuarterlyBudgetNonOptCenterIndirectCost> costQuarterlyBudgetNonOptCenterIndirectCostList,List<CostQuarterlyBudgetRevenueDetail> costQuarterlyBudgetRevenueDetailList);
	
	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);
	
}
