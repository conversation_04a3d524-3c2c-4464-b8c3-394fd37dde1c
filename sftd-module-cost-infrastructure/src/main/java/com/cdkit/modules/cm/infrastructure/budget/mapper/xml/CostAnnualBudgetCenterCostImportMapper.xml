<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdkit.modules.cm.infrastructure.budget.mapper.CostAnnualBudgetCenterCostImportMapper">

    <!-- 根据预算编码和模版类型查询科目汇总数据 -->
    <select id="selectSubjectSummaryByBudgetCodeAndTemplateType"
            resultType="com.cdkit.modules.cm.domain.budget.model.dto.CenterCostSubjectSummaryDTO">
        SELECT
            subject_code AS subjectCode,
            subject_name AS subjectName,
            subject_description AS subjectDescription,
            SUM(cost_amount) AS totalAmount,
            template_type AS templateType
        FROM cost_annual_budget_center_cost_import
        WHERE del_flag = 0
          AND budget_code = #{budgetCode}
          AND template_type = #{templateType}
        GROUP BY subject_code, subject_name, subject_description, template_type
        ORDER BY subject_code ASC
    </select>

</mapper>