package com.cdkit.modules.cm.infrastructure.adapters.material;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.common.api.vo.Result;
import com.cdkit.md.api.IMdMaterialApi;
import com.cdkit.md.entity.MdMaterialPage;
import com.cdkit.modules.cm.domain.gateway.material.MaterialExternalGateway;
import com.cdkit.modules.cm.domain.gateway.material.entity.MaterialDetailEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 材料外部服务实现类
 * 调用外部材料管理模块的Feign接口
 * <AUTHOR>
 * @date 2025/07/16
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MaterialExternalFeignAdapter implements MaterialExternalGateway {

    private final IMdMaterialApi mdMaterialApi;

    @Override
    public Page<MaterialDetailEntity> listByPage(String materialCode, String materialName, Integer pageNo, Integer pageSize) {
        try {
            log.info("调用IMdMaterialApi.listByPage - materialCode: {}, materialName: {}, pageNo: {}, pageSize: {}", 
                    materialCode, materialName, pageNo, pageSize);
            MdMaterialPage materialPage=new MdMaterialPage();
            if(StrUtil.isNotEmpty(materialCode))materialPage.setMaterialCode(materialCode);
            if(StrUtil.isNotEmpty(materialName))materialPage.setMaterialName(materialName);
            materialPage.setPageNo(pageNo);
            materialPage.setPageSize(pageSize);
            // 调用外部材料管理模块的分页接口
            Page<MdMaterialPage> mdMaterialPage = mdMaterialApi.listByPage(materialPage);
            Page<MaterialDetailEntity> materialDetailPage = new Page<>(mdMaterialPage.getCurrent(), mdMaterialPage.getSize(), mdMaterialPage.getTotal());
            List<MaterialDetailEntity> pageList=new ArrayList<>();
            if(CollectionUtil.isNotEmpty(mdMaterialPage.getRecords())) {
                pageList = BeanUtil.copyToList(mdMaterialPage.getRecords(), MaterialDetailEntity.class);
            }
            materialDetailPage.setRecords(pageList);
            return materialDetailPage;
        } catch (Exception e) {
            log.error("调用IMdMaterialApi.listByPage失败", e);
            return new Page<>();
        }
    }
}
