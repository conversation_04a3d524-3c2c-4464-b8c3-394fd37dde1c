package com.cdkit.modules.cm.infrastructure.budget.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.cm.infrastructure.budget.entity.*;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 年度总预算
 * @Author: cdkit-boot
 * @Date:   2025-07-30
 * @Version: V1.0
 */
public interface ICostAnnualBudgetService extends IService<CostAnnualBudget> {
	
}
