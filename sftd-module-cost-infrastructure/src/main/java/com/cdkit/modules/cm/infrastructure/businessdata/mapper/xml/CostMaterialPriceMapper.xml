<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdkit.modules.cm.infrastructure.businessdata.mapper.CostMaterialPriceMapper">

    <!-- 根据物料编码获取最新的材料单价记录 -->
    <select id="getLatestByMaterialCode" parameterType="java.lang.String"
            resultType="com.cdkit.modules.cm.infrastructure.businessdata.entity.CostMaterialPrice">
        SELECT * FROM cost_material_price
        WHERE material_code = #{materialCode}
        AND del_flag = 0
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 根据物料编码查询在指定时间段内生效的记录 -->
    <select id="findEffectiveInPeriod"
            resultType="com.cdkit.modules.cm.infrastructure.businessdata.entity.CostMaterialPrice">
        SELECT * FROM cost_material_price
        WHERE material_code = #{materialCode}
        AND status = 'in_effect'
        AND effective_date &lt;= #{endDate}
        AND expiration_date &gt;= #{startDate}
        AND del_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 批量更新状态为已失效 -->
    <update id="batchSetExpired">
        UPDATE cost_material_price
        SET status = 'expired',
            update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND del_flag = 0
    </update>

    <!-- 查询需要更新状态的记录 -->
    <select id="findNeedUpdateStatus"
            resultType="com.cdkit.modules.cm.infrastructure.businessdata.entity.CostMaterialPrice">
        SELECT * FROM cost_material_price
        WHERE status != 'pending_submit'
        AND del_flag = 0
        AND (
            (status = 'not_effective' AND effective_date &lt;= NOW())
            OR
            (status = 'in_effect' AND expiration_date &lt; NOW())
        )
    </select>

</mapper>