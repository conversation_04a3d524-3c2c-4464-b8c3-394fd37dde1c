package com.cdkit.modules.cm.infrastructure.budget.service.impl;


import com.cdkit.modules.cm.infrastructure.budget.entity.CostAnnualBudgetDetail;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostAnnualBudgetDetailDirectCost;
import com.cdkit.modules.cm.infrastructure.budget.mapper.CostAnnualBudgetDetailDirectCostMapper;
import com.cdkit.modules.cm.infrastructure.budget.mapper.CostAnnualBudgetDetailMapper;
import com.cdkit.modules.cm.infrastructure.budget.service.ICostAnnualBudgetDetailService;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Description: 项目年度预算
 * @Author: sunhzh
 * @Date:   2025-07-30
 * @Version: V1.0
 */
@Service
public class CostAnnualBudgetDetailServiceImpl extends ServiceImpl<CostAnnualBudgetDetailMapper, CostAnnualBudgetDetail> implements ICostAnnualBudgetDetailService {
	
	@Autowired
	private CostAnnualBudgetDetailMapper costAnnualBudgetDetailMapper;

	@Autowired
	private CostAnnualBudgetDetailDirectCostMapper costAnnualBudgetDetailDirectCostMapper;

	@Override
	public List<CostAnnualBudgetDetail> selectByMainId(String mainId) {
		return costAnnualBudgetDetailMapper.selectByMainId(mainId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(CostAnnualBudgetDetail costAnnualBudgetDetail, List<CostAnnualBudgetDetailDirectCost> costAnnualBudgetDetailDirectCostList) {
		costAnnualBudgetDetail.setId(null);
		costAnnualBudgetDetailMapper.insert(costAnnualBudgetDetail);
		if(costAnnualBudgetDetailDirectCostList!=null && costAnnualBudgetDetailDirectCostList.size()>0) {
			for(CostAnnualBudgetDetailDirectCost entity:costAnnualBudgetDetailDirectCostList) {
				//外键设置
				entity.setId(null);
				entity.setBudgetDetailId(costAnnualBudgetDetail.getId());
				costAnnualBudgetDetailDirectCostMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(CostAnnualBudgetDetail costAnnualBudgetDetail,List<CostAnnualBudgetDetailDirectCost> costAnnualBudgetDetailDirectCostList) {
		costAnnualBudgetDetailMapper.updateById(costAnnualBudgetDetail);

		//1.先删除子表数据
		costAnnualBudgetDetailDirectCostMapper.deleteByMainId(costAnnualBudgetDetail.getId());

		//2.子表数据重新插入
		if(costAnnualBudgetDetailDirectCostList!=null && costAnnualBudgetDetailDirectCostList.size()>0) {
			for(CostAnnualBudgetDetailDirectCost entity:costAnnualBudgetDetailDirectCostList) {
				//外键设置
				entity.setBudgetDetailId(costAnnualBudgetDetail.getId());
				costAnnualBudgetDetailDirectCostMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		costAnnualBudgetDetailDirectCostMapper.deleteByMainId(id);
		costAnnualBudgetDetailMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			costAnnualBudgetDetailDirectCostMapper.deleteByMainId(id.toString());
			costAnnualBudgetDetailMapper.deleteById(id);
		}
	}
}
