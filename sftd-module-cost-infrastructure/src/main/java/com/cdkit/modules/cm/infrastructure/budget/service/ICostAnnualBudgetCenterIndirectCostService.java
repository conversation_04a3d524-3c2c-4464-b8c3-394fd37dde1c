package com.cdkit.modules.cm.infrastructure.budget.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostAnnualBudgetCenterIndirectCost;

import java.util.List;

/**
 * @Description: 其他成本-本中心间接成本
 * @Author: cdkit-boot
 * @Date:   2025-07-30
 * @Version: V1.0
 */
public interface ICostAnnualBudgetCenterIndirectCostService extends IService<CostAnnualBudgetCenterIndirectCost> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<CostAnnualBudgetCenterIndirectCost>
	 */
	public List<CostAnnualBudgetCenterIndirectCost> selectByMainId(String mainId);
}
