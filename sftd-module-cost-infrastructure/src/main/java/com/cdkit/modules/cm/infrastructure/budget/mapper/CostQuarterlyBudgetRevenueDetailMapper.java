package com.cdkit.modules.cm.infrastructure.budget.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostQuarterlyBudgetRevenueDetail;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 季度预算收入明细表
 * @Author: cdkit-boot
 * @Date:   2025-08-12
 * @Version: V1.0
 */
public interface CostQuarterlyBudgetRevenueDetailMapper extends BaseMapper<CostQuarterlyBudgetRevenueDetail> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<CostQuarterlyBudgetRevenueDetail>
   */
	public List<CostQuarterlyBudgetRevenueDetail> selectByMainId(@Param("mainId") String mainId);
}
