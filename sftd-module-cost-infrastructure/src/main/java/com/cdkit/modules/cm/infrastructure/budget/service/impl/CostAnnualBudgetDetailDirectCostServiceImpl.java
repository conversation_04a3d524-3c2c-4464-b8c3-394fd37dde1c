package com.cdkit.modules.cm.infrastructure.budget.service.impl;


import com.cdkit.modules.cm.infrastructure.budget.entity.CostAnnualBudgetDetailDirectCost;
import com.cdkit.modules.cm.infrastructure.budget.mapper.CostAnnualBudgetDetailDirectCostMapper;
import com.cdkit.modules.cm.infrastructure.budget.service.ICostAnnualBudgetDetailDirectCostService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 年度总预算项目直接成本明细
 * @Author: cdkit-boot
 * @Date:   2025-08-04
 * @Version: V1.0
 */
@Service
public class CostAnnualBudgetDetailDirectCostServiceImpl extends ServiceImpl<CostAnnualBudgetDetailDirectCostMapper, CostAnnualBudgetDetailDirectCost> implements ICostAnnualBudgetDetailDirectCostService {
	
	@Autowired
	private CostAnnualBudgetDetailDirectCostMapper costAnnualBudgetDetailDirectCostMapper;
	
	@Override
	public List<CostAnnualBudgetDetailDirectCost> selectByMainId(String mainId) {
		return costAnnualBudgetDetailDirectCostMapper.selectByMainId(mainId);
	}
}
