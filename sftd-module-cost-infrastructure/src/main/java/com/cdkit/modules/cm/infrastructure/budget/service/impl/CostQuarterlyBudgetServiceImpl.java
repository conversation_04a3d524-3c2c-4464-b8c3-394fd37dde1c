package com.cdkit.modules.cm.infrastructure.budget.service.impl;


import com.cdkit.modules.cm.infrastructure.budget.entity.*;
import com.cdkit.modules.cm.infrastructure.budget.mapper.*;
import com.cdkit.modules.cm.infrastructure.budget.service.ICostQuarterlyBudgetService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;
import java.util.Collection;

/**
 * @Description: 季度预算主表
 * @Author: cdkit-boot
 * @Date:   2025-08-12
 * @Version: V1.0
 */
@Service
public class CostQuarterlyBudgetServiceImpl extends ServiceImpl<CostQuarterlyBudgetMapper, CostQuarterlyBudget> implements ICostQuarterlyBudgetService {

	@Autowired
	private CostQuarterlyBudgetMapper costQuarterlyBudgetMapper;
	@Autowired
	private CostQuarterlyBudgetProcPkgDetailMapper costQuarterlyBudgetProcPkgDetailMapper;
	@Autowired
	private CostQuarterlyBudgetMaterialDetailMapper costQuarterlyBudgetMaterialDetailMapper;
	@Autowired
	private CostQuarterlyBudgetSubjectDirectCostMapper costQuarterlyBudgetSubjectDirectCostMapper;
	@Autowired
	private CostQuarterlyBudgetCenterIndirectCostMapper costQuarterlyBudgetCenterIndirectCostMapper;
	@Autowired
	private CostQuarterlyBudgetCompMageIndirectCostMapper costQuarterlyBudgetCompMageIndirectCostMapper;
	@Autowired
	private CostQuarterlyBudgetNonOptCenterIndirectCostMapper costQuarterlyBudgetNonOptCenterIndirectCostMapper;
	@Autowired
	private CostQuarterlyBudgetRevenueDetailMapper costQuarterlyBudgetRevenueDetailMapper;
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(CostQuarterlyBudget costQuarterlyBudget, List<CostQuarterlyBudgetProcPkgDetail> costQuarterlyBudgetProcPkgDetailList, List<CostQuarterlyBudgetMaterialDetail> costQuarterlyBudgetMaterialDetailList, List<CostQuarterlyBudgetSubjectDirectCost> costQuarterlyBudgetSubjectDirectCostList, List<CostQuarterlyBudgetCenterIndirectCost> costQuarterlyBudgetCenterIndirectCostList, List<CostQuarterlyBudgetCompMageIndirectCost> costQuarterlyBudgetCompMageIndirectCostList, List<CostQuarterlyBudgetNonOptCenterIndirectCost> costQuarterlyBudgetNonOptCenterIndirectCostList, List<CostQuarterlyBudgetRevenueDetail> costQuarterlyBudgetRevenueDetailList) {
		costQuarterlyBudgetMapper.insert(costQuarterlyBudget);
		if(costQuarterlyBudgetProcPkgDetailList!=null && costQuarterlyBudgetProcPkgDetailList.size()>0) {
			for(CostQuarterlyBudgetProcPkgDetail entity:costQuarterlyBudgetProcPkgDetailList) {
				//外键设置
				entity.setQuarterlyBudgetId(costQuarterlyBudget.getId());
				costQuarterlyBudgetProcPkgDetailMapper.insert(entity);
			}
		}
		if(costQuarterlyBudgetMaterialDetailList!=null && costQuarterlyBudgetMaterialDetailList.size()>0) {
			for(CostQuarterlyBudgetMaterialDetail entity:costQuarterlyBudgetMaterialDetailList) {
				//外键设置
				entity.setQuarterlyBudgetId(costQuarterlyBudget.getId());
				costQuarterlyBudgetMaterialDetailMapper.insert(entity);
			}
		}
		if(costQuarterlyBudgetSubjectDirectCostList!=null && costQuarterlyBudgetSubjectDirectCostList.size()>0) {
			for(CostQuarterlyBudgetSubjectDirectCost entity:costQuarterlyBudgetSubjectDirectCostList) {
				//外键设置
				entity.setQuarterlyBudgetId(costQuarterlyBudget.getId());
				costQuarterlyBudgetSubjectDirectCostMapper.insert(entity);
			}
		}
		if(costQuarterlyBudgetCenterIndirectCostList!=null && costQuarterlyBudgetCenterIndirectCostList.size()>0) {
			for(CostQuarterlyBudgetCenterIndirectCost entity:costQuarterlyBudgetCenterIndirectCostList) {
				//外键设置
				entity.setQuarterlyBudgetId(costQuarterlyBudget.getId());
				costQuarterlyBudgetCenterIndirectCostMapper.insert(entity);
			}
		}
		if(costQuarterlyBudgetCompMageIndirectCostList!=null && costQuarterlyBudgetCompMageIndirectCostList.size()>0) {
			for(CostQuarterlyBudgetCompMageIndirectCost entity:costQuarterlyBudgetCompMageIndirectCostList) {
				//外键设置
				entity.setQuarterlyBudgetId(costQuarterlyBudget.getId());
				costQuarterlyBudgetCompMageIndirectCostMapper.insert(entity);
			}
		}
		if(costQuarterlyBudgetNonOptCenterIndirectCostList!=null && costQuarterlyBudgetNonOptCenterIndirectCostList.size()>0) {
			for(CostQuarterlyBudgetNonOptCenterIndirectCost entity:costQuarterlyBudgetNonOptCenterIndirectCostList) {
				//外键设置
				entity.setQuarterlyBudgetId(costQuarterlyBudget.getId());
				costQuarterlyBudgetNonOptCenterIndirectCostMapper.insert(entity);
			}
		}
		if(costQuarterlyBudgetRevenueDetailList!=null && costQuarterlyBudgetRevenueDetailList.size()>0) {
			for(CostQuarterlyBudgetRevenueDetail entity:costQuarterlyBudgetRevenueDetailList) {
				//外键设置
				entity.setQuarterlyBudgetId(costQuarterlyBudget.getId());
				costQuarterlyBudgetRevenueDetailMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(CostQuarterlyBudget costQuarterlyBudget,List<CostQuarterlyBudgetProcPkgDetail> costQuarterlyBudgetProcPkgDetailList,List<CostQuarterlyBudgetMaterialDetail> costQuarterlyBudgetMaterialDetailList,List<CostQuarterlyBudgetSubjectDirectCost> costQuarterlyBudgetSubjectDirectCostList,List<CostQuarterlyBudgetCenterIndirectCost> costQuarterlyBudgetCenterIndirectCostList,List<CostQuarterlyBudgetCompMageIndirectCost> costQuarterlyBudgetCompMageIndirectCostList,List<CostQuarterlyBudgetNonOptCenterIndirectCost> costQuarterlyBudgetNonOptCenterIndirectCostList,List<CostQuarterlyBudgetRevenueDetail> costQuarterlyBudgetRevenueDetailList) {
		costQuarterlyBudgetMapper.updateById(costQuarterlyBudget);
		
		//1.先删除子表数据
		costQuarterlyBudgetProcPkgDetailMapper.deleteByMainId(costQuarterlyBudget.getId());
		costQuarterlyBudgetMaterialDetailMapper.deleteByMainId(costQuarterlyBudget.getId());
		costQuarterlyBudgetSubjectDirectCostMapper.deleteByMainId(costQuarterlyBudget.getId());
		costQuarterlyBudgetCenterIndirectCostMapper.deleteByMainId(costQuarterlyBudget.getId());
		costQuarterlyBudgetCompMageIndirectCostMapper.deleteByMainId(costQuarterlyBudget.getId());
		costQuarterlyBudgetNonOptCenterIndirectCostMapper.deleteByMainId(costQuarterlyBudget.getId());
		costQuarterlyBudgetRevenueDetailMapper.deleteByMainId(costQuarterlyBudget.getId());
		
		//2.子表数据重新插入
		if(costQuarterlyBudgetProcPkgDetailList!=null && costQuarterlyBudgetProcPkgDetailList.size()>0) {
			for(CostQuarterlyBudgetProcPkgDetail entity:costQuarterlyBudgetProcPkgDetailList) {
				//外键设置
				entity.setQuarterlyBudgetId(costQuarterlyBudget.getId());
				costQuarterlyBudgetProcPkgDetailMapper.insert(entity);
			}
		}
		if(costQuarterlyBudgetMaterialDetailList!=null && costQuarterlyBudgetMaterialDetailList.size()>0) {
			for(CostQuarterlyBudgetMaterialDetail entity:costQuarterlyBudgetMaterialDetailList) {
				//外键设置
				entity.setQuarterlyBudgetId(costQuarterlyBudget.getId());
				costQuarterlyBudgetMaterialDetailMapper.insert(entity);
			}
		}
		if(costQuarterlyBudgetSubjectDirectCostList!=null && costQuarterlyBudgetSubjectDirectCostList.size()>0) {
			for(CostQuarterlyBudgetSubjectDirectCost entity:costQuarterlyBudgetSubjectDirectCostList) {
				//外键设置
				entity.setQuarterlyBudgetId(costQuarterlyBudget.getId());
				costQuarterlyBudgetSubjectDirectCostMapper.insert(entity);
			}
		}
		if(costQuarterlyBudgetCenterIndirectCostList!=null && costQuarterlyBudgetCenterIndirectCostList.size()>0) {
			for(CostQuarterlyBudgetCenterIndirectCost entity:costQuarterlyBudgetCenterIndirectCostList) {
				//外键设置
				entity.setQuarterlyBudgetId(costQuarterlyBudget.getId());
				costQuarterlyBudgetCenterIndirectCostMapper.insert(entity);
			}
		}
		if(costQuarterlyBudgetCompMageIndirectCostList!=null && costQuarterlyBudgetCompMageIndirectCostList.size()>0) {
			for(CostQuarterlyBudgetCompMageIndirectCost entity:costQuarterlyBudgetCompMageIndirectCostList) {
				//外键设置
				entity.setQuarterlyBudgetId(costQuarterlyBudget.getId());
				costQuarterlyBudgetCompMageIndirectCostMapper.insert(entity);
			}
		}
		if(costQuarterlyBudgetNonOptCenterIndirectCostList!=null && costQuarterlyBudgetNonOptCenterIndirectCostList.size()>0) {
			for(CostQuarterlyBudgetNonOptCenterIndirectCost entity:costQuarterlyBudgetNonOptCenterIndirectCostList) {
				//外键设置
				entity.setQuarterlyBudgetId(costQuarterlyBudget.getId());
				costQuarterlyBudgetNonOptCenterIndirectCostMapper.insert(entity);
			}
		}
		if(costQuarterlyBudgetRevenueDetailList!=null && costQuarterlyBudgetRevenueDetailList.size()>0) {
			for(CostQuarterlyBudgetRevenueDetail entity:costQuarterlyBudgetRevenueDetailList) {
				//外键设置
				entity.setQuarterlyBudgetId(costQuarterlyBudget.getId());
				costQuarterlyBudgetRevenueDetailMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		costQuarterlyBudgetProcPkgDetailMapper.deleteByMainId(id);
		costQuarterlyBudgetMaterialDetailMapper.deleteByMainId(id);
		costQuarterlyBudgetSubjectDirectCostMapper.deleteByMainId(id);
		costQuarterlyBudgetCenterIndirectCostMapper.deleteByMainId(id);
		costQuarterlyBudgetCompMageIndirectCostMapper.deleteByMainId(id);
		costQuarterlyBudgetNonOptCenterIndirectCostMapper.deleteByMainId(id);
		costQuarterlyBudgetRevenueDetailMapper.deleteByMainId(id);
		costQuarterlyBudgetMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			costQuarterlyBudgetProcPkgDetailMapper.deleteByMainId(id.toString());
			costQuarterlyBudgetMaterialDetailMapper.deleteByMainId(id.toString());
			costQuarterlyBudgetSubjectDirectCostMapper.deleteByMainId(id.toString());
			costQuarterlyBudgetCenterIndirectCostMapper.deleteByMainId(id.toString());
			costQuarterlyBudgetCompMageIndirectCostMapper.deleteByMainId(id.toString());
			costQuarterlyBudgetNonOptCenterIndirectCostMapper.deleteByMainId(id.toString());
			costQuarterlyBudgetRevenueDetailMapper.deleteByMainId(id.toString());
			costQuarterlyBudgetMapper.deleteById(id);
		}
	}
	
}
