<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.cdkitframework.boot</groupId>
        <artifactId>sftd-module-cost-budget</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>sftd-module-cost-infrastructure</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.cdkitframework.boot</groupId>
            <artifactId>cdkit-boot-starter3-cloud</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cdkitframework.boot</groupId>
            <artifactId>did-rule</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cdkitframework.boot</groupId>
            <artifactId>sftd-module-cost-domain</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.cnooc.sftd</groupId>
            <artifactId>sftd-module-md-api</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.cnooc.sftd</groupId>
            <artifactId>sftd-module-plm-api</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.cnooc.sftd</groupId>
            <artifactId>sftd-module-wms-api</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>


    </dependencies>

</project>