<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.cdkitframework.boot</groupId>
        <artifactId>cdkit-boot-dependencies</artifactId>
        <version>3.7.1-SNAPSHOT</version>
    </parent>

    <modules>
        <module>sftd-module-cost-api</module>
        <module>sftd-module-cost-application</module>
        <module>sftd-module-cost-domain</module>
        <module>sftd-module-cost-infrastructure</module>
        <module>sftd-module-cost-performance</module>
        <module>sftd-module-cost-starter</module>
    </modules>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>sftd-module-cost-budget</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>

    <properties>
        <revision>0.0.1-SNAPSHOT</revision>

        <maven.test.skip>true</maven.test.skip>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>3.5.6</version>
            </dependency>
            <dependency>
                <groupId>com.cdkitframework.boot</groupId>
                <artifactId>cdkit-boot-base-core</artifactId>
                <version>3.7.0</version>
            </dependency>
            <dependency>
                <groupId>com.cdkitframework.boot</groupId>
                <artifactId>cdkit-system-cloud-api</artifactId>
                <version>3.7.0</version>
            </dependency>
            <dependency>
                <groupId>com.cdkitframework.boot</groupId>
                <artifactId>did-rule</artifactId>
                <version>1.0.0</version>
            </dependency>
            <!-- 引入cdkit-boot-starter3-cloud依赖 -->
            <dependency>
                <groupId>com.cdkitframework.boot</groupId>
                <artifactId>cdkit-boot-starter3-cloud</artifactId>
                <version>3.7.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba.cloud</groupId>
                        <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.cloud</groupId>
                        <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <profiles>
        <profile>
            <id>cse</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <config.cse.enable>true</config.cse.enable>
                <config.nacos.enable>false</config.nacos.enable>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.huaweicloud</groupId>
                    <artifactId>spring-cloud-starter-huawei-service-engine</artifactId>
                    <version>1.11.7-2022.0.x</version>
                    <exclusions>
                        <exclusion>
                            <groupId>com.huaweicloud</groupId>
                            <artifactId>spring-cloud-starter-huawei-swagger</artifactId>
                        </exclusion>
                    </exclusions>
                </dependency>
            </dependencies>
        </profile>
    </profiles>

    <repositories>
        <repository>
            <id>alimaven</id>
            <name>alimaven Repository</name>
            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
        </repository>
        <repository>
            <id>zngc</id>
            <name>zngc Repository</name>
            <url>http://nexus.zngc.com/repository/maven-public/</url>
        </repository>
    </repositories>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.7.0</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
            </plugin>
        </plugins>

        <resources>
            <resource>
                <directory>src/main/java</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.*</include>
                </includes>
            </resource>
        </resources>
    </build>

</project>