package com.cdkit.modules.cm.application.schedule;

import com.cdkit.config.mybatis.TenantIgnoreContext;
import com.cdkit.modules.cm.application.project.ProjectApplication;
import com.cdkit.modules.cm.application.schedule.config.ScheduleProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

/**
 * 项目定时任务
 * 负责处理项目相关的定时任务，如年度项目自动关闭
 * 
 * <AUTHOR>
 * @date 2025/07/14
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ProjectScheduleTask {

    private final ProjectApplication projectApplication;
    private final ScheduleProperties scheduleProperties;

    /**
     * 自动关闭上一年度的执行中项目
     * 每年1月1日凌晨1点执行
     * 将上一年度的执行中项目状态更新为已关闭
     */
    @Scheduled(cron = "#{@scheduleProperties.projectAutoCloseCron}")
    @ConditionalOnProperty(name = "cost.schedule.enable-project-auto-close", havingValue = "true", matchIfMissing = true)
    public void closeLastYearProjects() {
        //忽略租户
        TenantIgnoreContext.setTenantIgnore(true);
        // 获取上一年度
        int lastYear = LocalDate.now().getYear() - 1;
        String lastYearStr = String.valueOf(lastYear);
        
        log.info("开始执行自动关闭{}年度执行中项目的定时任务", lastYearStr);
        
        try {
            int closedCount = projectApplication.closeInExecutionProjectsByYear(lastYearStr);
            log.info("自动关闭{}年度执行中项目定时任务执行完成，共关闭{}个项目", lastYearStr, closedCount);
        } catch (Exception e) {
            log.error("自动关闭{}年度执行中项目定时任务执行失败", lastYearStr, e);
        }
    }

    /**
     * 测试用定时任务 - 每天凌晨2点执行一次
     * 用于测试定时任务功能，可在生产环境中移除
     * 注意：此方法仅用于测试，生产环境应注释掉或删除
     */
    @Scheduled(cron = "#{@scheduleProperties.testTaskCron}")
    @ConditionalOnProperty(name = "cost.schedule.enable-test-task", havingValue = "true")
    public void testScheduleTask() {
        log.info("测试定时任务执行开始");
        
        // 获取当前年度
        int currentYear = LocalDate.now().getYear();
        String currentYearStr = String.valueOf(currentYear);
        
        try {
            // 查询当前年度执行中的项目数量，但不执行关闭操作
            log.info("当前是{}年，测试查询执行中的项目", currentYearStr);
        } catch (Exception e) {
            log.error("测试定时任务执行失败", e);
        }
        
        log.info("测试定时任务执行结束");
    }
}
