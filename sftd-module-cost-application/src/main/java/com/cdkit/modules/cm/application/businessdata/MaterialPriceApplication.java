package com.cdkit.modules.cm.application.businessdata;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.common.exception.CdkitCloudException;
import com.cdkit.common.page.OrderParam;
import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.cdkit.modules.cm.domain.gateway.material.entity.MaterialDetailDTO;
import com.cdkit.modules.cm.domain.gateway.material.entity.MaterialDetailEntity;
import com.cdkit.modules.cm.domain.businessdata.model.entity.CostMaterialPriceEntity;
import com.cdkit.modules.cm.domain.businessdata.repository.CostMaterialPriceRepository;
import com.cdkit.modules.cm.domain.gateway.material.MaterialExternalGateway;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 材料单价应用服务
 * <AUTHOR>
 * @date 2025/07/16
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MaterialPriceApplication {

    private final CostMaterialPriceRepository costMaterialPriceRepository;
    private final MaterialExternalGateway materialExternalService;

    /**
     * 分页列表查询
     *
     * @param queryEntity 查询条件
     * @param pageNo    页码
     * @param pageSize  每页数量
     * @return 查询结果
     */
    public PageRes<CostMaterialPriceEntity> queryPageList(CostMaterialPriceEntity queryEntity, Integer pageNo, Integer pageSize) {
        PageReq pageReq = new PageReq();
        pageReq.setCurrent((long) pageNo);
        pageReq.setSize((long) pageSize);

        // 按照创建时间倒序
        OrderParam createTimeParam = new OrderParam();
        createTimeParam.setField("create_time");
        createTimeParam.setOrder("desc");

        pageReq.setOrderParam(Arrays.asList(createTimeParam));

        return costMaterialPriceRepository.queryPageList(queryEntity, pageReq);
    }

    /**
     * 新增材料单价
     *
     * @param entity 材料单价实体
     * @return 保存后的实体
     */
    @Transactional(rollbackFor = Exception.class)
    public CostMaterialPriceEntity add(CostMaterialPriceEntity entity) {
        // 参数校验
        validateMaterialPrice(entity);

        // 初始化默认值
        entity.initDefaults();

        // 设置创建时间
        Date now = new Date();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);

        // 计算税价
        calculatePrice(entity);

        // 保存
        return costMaterialPriceRepository.save(entity);
    }

    /**
     * 编辑材料单价
     *
     * @param entity 材料单价实体
     * @return 更新后的实体
     */
    @Transactional(rollbackFor = Exception.class)
    public CostMaterialPriceEntity edit(CostMaterialPriceEntity entity) {
        // 参数校验
        if (!StringUtils.hasText(entity.getId())) {
            throw new CdkitCloudException("ID不能为空");
        }

        // 查询原记录
        CostMaterialPriceEntity existingEntity = costMaterialPriceRepository.findById(entity.getId());
        if (existingEntity == null) {
            throw new CdkitCloudException("记录不存在");
        }

        // 权限校验
        if (!existingEntity.canEdit()) {
            throw new CdkitCloudException("当前状态不允许编辑");
        }

        // 参数校验
        validateMaterialPrice(entity);

        // 设置更新时间
        entity.setUpdateTime(new Date());

        // 计算税价
        calculatePrice(entity);

        // 更新
        return costMaterialPriceRepository.updateById(entity);
    }

    /**
     * 删除材料单价
     *
     * @param id 主键ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        if (!StringUtils.hasText(id)) {
            throw new CdkitCloudException("ID不能为空");
        }

        // 查询记录
        CostMaterialPriceEntity entity = costMaterialPriceRepository.findById(id);
        if (entity == null) {
            throw new CdkitCloudException("记录不存在");
        }

        // 权限校验
        if (!entity.canDelete()) {
            throw new CdkitCloudException("当前状态不允许删除");
        }

        // 删除
        costMaterialPriceRepository.deleteById(id);
    }

    /**
     * 批量删除材料单价
     *
     * @param ids ID列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteBatch(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new CdkitCloudException("ID列表不能为空");
        }

        // 查询所有记录并校验权限
        for (String id : ids) {
            CostMaterialPriceEntity entity = costMaterialPriceRepository.findById(id);
            if (entity == null) {
                throw new CdkitCloudException("记录不存在：" + id);
            }
            if (!entity.canDelete()) {
                throw new CdkitCloudException("记录状态不允许删除：" + entity.getMaterialName());
            }
        }

        // 批量删除
        costMaterialPriceRepository.deleteByIds(ids);
    }

    /**
     * 提交材料单价
     *
     * @param id 主键ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void submit(String id) {
        if (!StringUtils.hasText(id)) {
            throw new CdkitCloudException("ID不能为空");
        }

        // 查询记录
        CostMaterialPriceEntity entity = costMaterialPriceRepository.findById(id);
        if (entity == null) {
            throw new CdkitCloudException("记录不存在");
        }

        // 权限校验
        if (!entity.canSubmit()) {
            throw new CdkitCloudException("当前状态不允许提交");
        }

        // 提交
        entity.submit();

        // 如果提交后状态为生效中，需要处理同期数据
        if (entity.isInEffect()) {
            handleSamePeriodData(entity);
        }

        // 更新
        entity.setUpdateTime(new Date());
        costMaterialPriceRepository.updateById(entity);
    }

    /**
     * 根据ID查询
     *
     * @param id 主键ID
     * @return 材料单价实体
     */
    public CostMaterialPriceEntity queryById(String id) {
        if (!StringUtils.hasText(id)) {
            throw new CdkitCloudException("ID不能为空");
        }
        return costMaterialPriceRepository.findById(id);
    }

    /**
     * 获取物料最新价格数据（用于新增时回显）
     *
     * @param materialCode 物料编码
     * @return 最新价格数据
     */
    public CostMaterialPriceEntity getLatestPriceForEcho(String materialCode) {
        if (!StringUtils.hasText(materialCode)) {
            return null;
        }
        return costMaterialPriceRepository.getLatestByMaterialCode(materialCode);
    }

    /**
     * 参数校验
     */
    private void validateMaterialPrice(CostMaterialPriceEntity entity) {
        if (!StringUtils.hasText(entity.getMaterialName())) {
            throw new CdkitCloudException("物料名称不能为空");
        }
        if (entity.getTaxUnitPrice() == null && entity.getNonTaxUnitPrice() == null) {
            throw new CdkitCloudException("含税单价和不含税单价至少填写一个");
        }
        if (entity.getTaxRate() == null || entity.getTaxRate().compareTo(BigDecimal.ZERO) < 0) {
            throw new CdkitCloudException("税率不能为空且不能小于0");
        }
    }

    /**
     * 计算税价
     */
    private void calculatePrice(CostMaterialPriceEntity entity) {
        if (entity.getTaxUnitPrice() != null && entity.getNonTaxUnitPrice() == null) {
            // 根据含税单价计算不含税单价
            entity.calculateNonTaxPrice();
        } else if (entity.getNonTaxUnitPrice() != null && entity.getTaxUnitPrice() == null) {
            // 根据不含税单价计算含税单价
            entity.calculateTaxPrice();
        }
    }

    /**
     * 处理同期数据（同一物料同期只能有一条生效数据）
     */
    private void handleSamePeriodData(CostMaterialPriceEntity currentEntity) {
        if (!StringUtils.hasText(currentEntity.getMaterialCode())) {
            return;
        }

        // 构建查询时间段：如果当前记录失效日期为空，使用一个很远的未来日期作为结束日期
        Date queryEndDate = currentEntity.getExpirationDate();
        if (queryEndDate == null) {
            // 失效日期为空时，使用一个很远的未来日期（如2099年）进行查询
            queryEndDate = new Date(4102444800000L); // 2099-12-31
        }

        // 查询同一物料在同期内生效的其他记录
        List<CostMaterialPriceEntity> effectiveList = costMaterialPriceRepository
                .findEffectiveInPeriod(currentEntity.getMaterialCode(),
                        currentEntity.getEffectiveDate(),
                        queryEndDate);

        // 过滤掉当前记录，找出需要设置为失效的记录（创建时间早的）
        List<String> expiredIds = effectiveList.stream()
                .filter(entity -> !entity.getId().equals(currentEntity.getId()))
                .filter(entity -> entity.getCreateTime().before(currentEntity.getCreateTime())) // 创建时间早的
                .map(CostMaterialPriceEntity::getId)
                .collect(Collectors.toList());

        // 批量设置为已失效
        if (!expiredIds.isEmpty()) {
            costMaterialPriceRepository.batchSetExpired(expiredIds);
            log.info("材料单价同期数据处理：物料编码={}, 设置失效记录数={}, 当前记录失效日期={}",
                    currentEntity.getMaterialCode(), expiredIds.size(),
                    currentEntity.getExpirationDate() == null ? "永久生效" : currentEntity.getExpirationDate());
        }
    }

    /**
     * 材料选择分页查询
     * 调用外部材料管理模块的API
     *
     * @param materialCode 物料编码
     * @param materialName 物料名称
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @return 查询结果
     */
    public Page<MaterialDetailDTO> selectMaterialByPage(String materialCode, String materialName, Integer pageNo, Integer pageSize) {
        Page<MaterialDetailEntity> materialDetailEntityPage = materialExternalService.listByPage(materialCode, materialName, pageNo, pageSize);
        Page<MaterialDetailDTO> materialDetailDTOPage=new Page<>();
        materialDetailDTOPage.setCurrent(materialDetailEntityPage.getCurrent());
        materialDetailDTOPage.setSize(materialDetailEntityPage.getSize());
        materialDetailDTOPage.setTotal(materialDetailEntityPage.getTotal());
        materialDetailDTOPage.setRecords(BeanUtil.copyToList(materialDetailEntityPage.getRecords(), MaterialDetailDTO.class));
        return materialDetailDTOPage;
    }

    /**
     * 获取导出数据列表
     *
     * @param queryEntity 查询条件
     * @return 导出数据列表
     */
    public List<CostMaterialPriceEntity> getExportList(CostMaterialPriceEntity queryEntity) {
        try {
            log.info("开始获取材料单价导出数据，查询条件：{}", queryEntity);

            // 调用仓储层获取所有符合条件的数据（不分页）
            List<CostMaterialPriceEntity> exportList = costMaterialPriceRepository.queryList(queryEntity);

            // 确保返回的列表不为null
            if (exportList == null) {
                exportList = Arrays.asList();
            }

            log.info("成功获取{}条材料单价导出数据", exportList.size());
            return exportList;
        } catch (Exception e) {
            log.error("获取材料单价导出数据失败", e);
            // 即使出现异常，也返回空列表而不是抛出异常，这样可以导出空Excel
            log.warn("获取导出数据失败，返回空列表以支持导出空Excel");
            return Arrays.asList();
        }
    }
}
