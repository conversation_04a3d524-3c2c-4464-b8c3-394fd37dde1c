package com.cdkit.modules.cm.application.procurement;
import com.cdkit.common.page.OrderParam;
import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.cdkit.modules.cm.api.common.ExcelImportResult;
import com.cdkit.modules.cm.domain.procurement.entity.CostPurchaseOrderLedgerEntity;
import com.cdkit.modules.cm.domain.procurement.repository.CostPurchaseOrderLedgerRepository;
import com.cdkit.modules.cm.application.procurement.util.HutoolExcelImportUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.util.Arrays;
import java.util.ArrayList;
import java.util.List;

/**
 * 采购订单台账 应用层
 * <AUTHOR>
 * @date 2025/07/10
 */
@Slf4j
@Service
public class PurchaseOrderLedgerApplication {
    private final CostPurchaseOrderLedgerRepository costPurchaseOrderLedgerRepository;

    public PurchaseOrderLedgerApplication(CostPurchaseOrderLedgerRepository costPurchaseOrderLedgerRepository) {
        this.costPurchaseOrderLedgerRepository = costPurchaseOrderLedgerRepository;
    }


    /**
     * 分页列表查询
     *
     * @param queryLedger 查询条件
     * @param pageNo    页码
     * @param pageSize  每页数量
     * @return 查询结果
     */
    public PageRes<CostPurchaseOrderLedgerEntity> queryPageList(CostPurchaseOrderLedgerEntity queryLedger, Integer pageNo, Integer pageSize) {
        PageReq pageReq = new PageReq();
        pageReq.setCurrent((long) pageNo);
        pageReq.setSize((long) pageSize);

        // 按照交货时间正序
        OrderParam createTimeParam = new OrderParam();
        createTimeParam.setField("create_time");
        createTimeParam.setOrder("asc");

        pageReq.setOrderParam(Arrays.asList(createTimeParam));
        return costPurchaseOrderLedgerRepository.page(queryLedger, pageReq);
    }

    /**
     * Excel导入采购订单台账数据
     *
     * @param request HTTP请求
     * @return 导入结果
     */
    public ExcelImportResult importExcel(HttpServletRequest request) {
        long startTime = System.currentTimeMillis();

        try {
            MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
            MultipartFile file = multipartRequest.getFile("file");

            if (file == null || file.isEmpty()) {
                return ExcelImportResult.builder()
                        .totalCount(0)
                        .successCount(0)
                        .failCount(0)
                        .allSuccess(false)
                        .errorMessages(Arrays.asList("上传文件为空"))
                        .duration(System.currentTimeMillis() - startTime)
                        .build();
            }

            // 使用自定义Excel导入工具解析Excel
            List<CostPurchaseOrderLedgerEntity> successList;
            List<String> errorMessages = new ArrayList<>();

            try {
                successList = HutoolExcelImportUtil.importExcel(
                        file.getInputStream(), file.getOriginalFilename());
                log.info("使用Hutool Excel解析成功，共解析到{}条数据", successList.size());
            } catch (Exception e) {
                log.error("Hutool Excel解析失败", e);
                errorMessages.add("Excel文件解析失败：" + e.getMessage());
                successList = new ArrayList<>();
            }

            // 数据验证
            if (successList != null && !successList.isEmpty()) {
                List<CostPurchaseOrderLedgerEntity> validList = new ArrayList<>();
                for (int i = 0; i < successList.size(); i++) {
                    CostPurchaseOrderLedgerEntity domain = successList.get(i);
                    List<String> rowErrors = validateEntity(domain, i + 2); // 第2行开始是数据行

                    if (rowErrors.isEmpty()) {
                        validList.add(domain);
                    } else {
                        errorMessages.addAll(rowErrors);
                    }
                }
                successList = validList;
            }

            int successCount = 0;
            int failCount = errorMessages.size();

            // 批量保存成功的数据
            if (successList != null && !successList.isEmpty()) {
                try {
                    costPurchaseOrderLedgerRepository.batchSave(successList);
                    successCount = successList.size();
                    log.info("成功导入采购订单台账数据 {} 条", successCount);
                } catch (Exception e) {
                    log.error("批量保存采购订单台账数据失败", e);
                    errorMessages.add("批量保存数据失败：" + e.getMessage());
                    failCount += successList.size();
                    successCount = 0;
                }
            }

            int totalCount = successCount + failCount;
            boolean allSuccess = failCount == 0 && totalCount > 0;

            return ExcelImportResult.builder()
                    .totalCount(totalCount)
                    .successCount(successCount)
                    .failCount(failCount)
                    .allSuccess(allSuccess)
                    .errorMessages(errorMessages)
                    .duration(System.currentTimeMillis() - startTime)
                    .build();

        } catch (Exception e) {
            log.error("Excel导入失败", e);
            return ExcelImportResult.builder()
                    .totalCount(0)
                    .successCount(0)
                    .failCount(1)
                    .allSuccess(false)
                    .errorMessages(Arrays.asList("Excel导入失败：" + e.getMessage()))
                    .duration(System.currentTimeMillis() - startTime)
                    .build();
        }
    }

    public List<CostPurchaseOrderLedgerEntity> list(String ids){
        return costPurchaseOrderLedgerRepository.list(ids);
    }

    /**
     * 验证实体数据
     *
     * @param entity 实体对象
     * @param rowNum 行号
     * @return 错误信息列表
     */
    private List<String> validateEntity(CostPurchaseOrderLedgerEntity entity, int rowNum) {
        List<String> errors = new ArrayList<>();

        // 检查必填字段
        if (entity.getOrderName() == null || entity.getOrderName().trim().isEmpty()) {
            errors.add("第" + rowNum + "行：订单名称不能为空");
        }

        if (entity.getMaterialName() == null || entity.getMaterialName().trim().isEmpty()) {
            errors.add("第" + rowNum + "行：原材料/服务名称不能为空");
        }

        if (entity.getSupplier() == null || entity.getSupplier().trim().isEmpty()) {
            errors.add("第" + rowNum + "行：供应商不能为空");
        }

        // 检查数值字段
        if (entity.getQuantity() != null && entity.getQuantity().compareTo(java.math.BigDecimal.ZERO) <= 0) {
            errors.add("第" + rowNum + "行：数量必须大于0");
        }

        if (entity.getBudgetUnitPrice() != null && entity.getBudgetUnitPrice().compareTo(java.math.BigDecimal.ZERO) < 0) {
            errors.add("第" + rowNum + "行：预算单价不能为负数");
        }

        if (entity.getOrderUnitPrice() != null && entity.getOrderUnitPrice().compareTo(java.math.BigDecimal.ZERO) < 0) {
            errors.add("第" + rowNum + "行：订单单价不能为负数");
        }

        // 检查日期字段
        if (entity.getSignTime() != null && entity.getPlannedDeliveryDate() != null) {
            if (entity.getSignTime().after(entity.getPlannedDeliveryDate())) {
                errors.add("第" + rowNum + "行：签订时间不能晚于计划交货日期");
            }
        }

        return errors;
    }

}
