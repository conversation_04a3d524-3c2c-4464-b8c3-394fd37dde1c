package com.cdkit.modules.cm.application.businessdata;

import com.cdkit.modules.cm.domain.businessdata.model.entity.CostBudgetSubjectEntity;
import com.cdkit.modules.cm.domain.businessdata.repository.CostBudgetSubjectRepository;
import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.cdkit.common.page.OrderParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * 预算科目管理应用服务
 * <AUTHOR>
 * @date 2025-07-31
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BudgetSubjectApplication {

    private final CostBudgetSubjectRepository costBudgetSubjectRepository;

    /**
     * 分页查询预算科目列表
     *
     * @param queryEntity 查询条件
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @return 分页结果
     */
    public PageRes<CostBudgetSubjectEntity> queryPageList(CostBudgetSubjectEntity queryEntity, Integer pageNo, Integer pageSize) {
        PageReq pageReq = new PageReq();
        pageReq.setCurrent((long) pageNo);
        pageReq.setSize((long) pageSize);

        // 按照排序号升序，创建时间倒序
        OrderParam sortOrderParam = new OrderParam();
        sortOrderParam.setField("sort_order");
        sortOrderParam.setOrder("asc");
        
        OrderParam createTimeParam = new OrderParam();
        createTimeParam.setField("create_time");
        createTimeParam.setOrder("desc");
        
        pageReq.setOrderParam(Arrays.asList(sortOrderParam, createTimeParam));

        return costBudgetSubjectRepository.queryPageList(queryEntity, pageReq);
    }

    /**
     * 根据ID查询预算科目详情
     *
     * @param id 预算科目ID
     * @return 预算科目详情
     */
    public CostBudgetSubjectEntity queryById(String id) {
        if (!StringUtils.hasText(id)) {
            throw new IllegalArgumentException("预算科目ID不能为空");
        }
        return costBudgetSubjectRepository.findById(id);
    }

    /**
     * 新增预算科目
     *
     * @param entity 预算科目实体
     * @return 预算科目ID
     */
    public String add(CostBudgetSubjectEntity entity) {
        if (entity == null) {
            throw new IllegalArgumentException("预算科目数据不能为空");
        }

        // 验证必填字段
        validateRequiredFields(entity);

        // 验证科目编码唯一性
        if (StringUtils.hasText(entity.getSubjectCode())) {
            CostBudgetSubjectEntity existingEntity = costBudgetSubjectRepository.findBySubjectCode(entity.getSubjectCode());
            if (existingEntity != null) {
                throw new IllegalArgumentException("预算科目编码已存在：" + entity.getSubjectCode());
            }
        }

        CostBudgetSubjectEntity savedEntity = costBudgetSubjectRepository.save(entity);
        log.info("新增预算科目成功，科目编码: {}, 科目名称: {}, ID: {}", 
                entity.getSubjectCode(), entity.getSubjectName(), savedEntity.getId());
        
        return savedEntity.getId();
    }

    /**
     * 编辑预算科目
     *
     * @param entity 预算科目实体
     * @return 预算科目ID
     */
    public String edit(CostBudgetSubjectEntity entity) {
        if (entity == null) {
            throw new IllegalArgumentException("预算科目数据不能为空");
        }

        if (!StringUtils.hasText(entity.getId())) {
            throw new IllegalArgumentException("预算科目ID不能为空");
        }

        // 验证记录是否存在
        CostBudgetSubjectEntity existingEntity = costBudgetSubjectRepository.findById(entity.getId());
        if (existingEntity == null) {
            throw new IllegalArgumentException("预算科目不存在，ID: " + entity.getId());
        }

        // 验证必填字段
        validateRequiredFields(entity);

        // 验证科目编码唯一性（排除当前记录）
        if (StringUtils.hasText(entity.getSubjectCode())) {
            if (costBudgetSubjectRepository.existsBySubjectCodeAndIdNot(entity.getSubjectCode(), entity.getId())) {
                throw new IllegalArgumentException("预算科目编码已存在：" + entity.getSubjectCode());
            }
        }

        CostBudgetSubjectEntity updatedEntity = costBudgetSubjectRepository.updateById(entity);
        log.info("编辑预算科目成功，科目编码: {}, 科目名称: {}, ID: {}",
                entity.getSubjectCode(), entity.getSubjectName(), entity.getId());

        return updatedEntity.getId();
    }

    /**
     * 根据ID删除预算科目
     *
     * @param id 预算科目ID
     */
    public void delete(String id) {
        if (!StringUtils.hasText(id)) {
            throw new IllegalArgumentException("预算科目ID不能为空");
        }

        // 验证记录是否存在
        CostBudgetSubjectEntity existingEntity = costBudgetSubjectRepository.findById(id);
        if (existingEntity == null) {
            throw new IllegalArgumentException("预算科目不存在，ID: " + id);
        }

        // 验证是否可以删除
        if (!existingEntity.canDelete()) {
            throw new IllegalArgumentException("启用状态的预算科目不允许删除，科目编码: " + existingEntity.getSubjectCode());
        }

        costBudgetSubjectRepository.deleteById(id);
        log.info("删除预算科目成功，科目编码: {}, 科目名称: {}, ID: {}",
                existingEntity.getSubjectCode(), existingEntity.getSubjectName(), id);
    }

    /**
     * 批量删除预算科目
     *
     * @param ids 预算科目ID列表
     */
    public void deleteBatch(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new IllegalArgumentException("预算科目ID列表不能为空");
        }

        // 验证每个记录是否可以删除
        for (String id : ids) {
            CostBudgetSubjectEntity existingEntity = costBudgetSubjectRepository.findById(id);
            if (existingEntity == null) {
                throw new IllegalArgumentException("预算科目不存在，ID: " + id);
            }
            if (!existingEntity.canDelete()) {
                throw new IllegalArgumentException("启用状态的预算科目不允许删除，科目编码: " + existingEntity.getSubjectCode());
            }
        }

        costBudgetSubjectRepository.deleteBatchByIds(ids);
        log.info("批量删除预算科目成功，删除数量: {}", ids.size());
    }

    /**
     * 查询所有启用状态的预算科目
     *
     * @return 预算科目列表
     */
    public List<CostBudgetSubjectEntity> listEnabled() {
        return costBudgetSubjectRepository.findAllEnabled();
    }

    /**
     * 验证必填字段
     */
    private void validateRequiredFields(CostBudgetSubjectEntity entity) {
        if (!StringUtils.hasText(entity.getSubjectCode())) {
            throw new IllegalArgumentException("预算科目编码不能为空");
        }
        if (!StringUtils.hasText(entity.getSubjectName())) {
            throw new IllegalArgumentException("预算科目名称不能为空");
        }
        if (!StringUtils.hasText(entity.getSubjectStatus())) {
            throw new IllegalArgumentException("预算科目状态不能为空");
        }
        if (entity.getSortOrder() == null) {
            throw new IllegalArgumentException("排序号不能为空");
        }
    }
}
