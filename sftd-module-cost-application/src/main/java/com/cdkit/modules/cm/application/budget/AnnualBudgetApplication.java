package com.cdkit.modules.cm.application.budget;

import com.cdkit.common.page.OrderParam;
import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.cdkit.modules.cm.domain.budget.mode.entity.CostAnnualBudgetEntity;
import com.cdkit.modules.cm.domain.budget.model.dto.CenterCostImportDTO;
import com.cdkit.modules.cm.domain.budget.model.dto.CenterCostSubjectSummaryDTO;
import com.cdkit.modules.cm.domain.budget.repository.CostAnnualBudgetRepository;
import com.cdkit.modules.cm.domain.budget.repository.CostAnnualBudgetDetailRepository;
import com.cdkit.modules.cm.domain.budget.repository.CenterCostImportRepository;
import com.cdkit.modules.cm.domain.budget.service.ProjectBudgetQueryService;
import com.cdkit.modules.cm.domain.budget.repository.IndirectCostRepository;
import com.cdkit.modules.cm.domain.budget.repository.CostAnnualBudgetDetailFullRepository;
import com.cdkit.modules.cm.domain.budget.service.AnnualBudgetDetailDomainService;
import com.cdkit.modules.cm.domain.budget.service.IndirectCostAllocationService;
import com.cdkit.modules.cm.domain.budget.service.BudgetStatisticsCalculationService;
import com.cdkit.modules.cm.domain.businessdata.model.entity.CostBudgetSubjectEntity;
import com.cdkit.modules.cm.domain.businessdata.repository.CostBudgetSubjectRepository;
import com.cdkit.modules.cm.domain.project.mode.entity.CostProjectPlanEntity;
import com.cdkit.modules.cm.domain.project.repository.CostProjectPlanRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 年度总预算应用服务
 * <AUTHOR>
 * @date 2025-07-30
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AnnualBudgetApplication {

    private final CostAnnualBudgetRepository costAnnualBudgetRepository;
    private final CostAnnualBudgetDetailRepository costAnnualBudgetDetailRepository;
    private final AnnualBudgetDetailDomainService annualBudgetDetailDomainService;
    private final CostBudgetSubjectRepository costBudgetSubjectRepository;
    private final CostProjectPlanRepository costProjectPlanRepository;
    private final IndirectCostAllocationService indirectCostAllocationService;
    private final IndirectCostRepository indirectCostRepository;
    private final CostAnnualBudgetDetailFullRepository costAnnualBudgetDetailFullRepository;
    private final CenterCostImportRepository centerCostImportRepository;
    private final BudgetStatisticsCalculationService budgetStatisticsCalculationService;
    private final ProjectBudgetQueryService projectBudgetQueryService;

    /**
     * 分页查询年度总预算列表
     *
     * @param queryEntity 查询条件
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @return 分页结果
     */
    public PageRes<CostAnnualBudgetEntity> queryPageList(CostAnnualBudgetEntity queryEntity, Integer pageNo, Integer pageSize) {
        PageReq pageReq = new PageReq();
        pageReq.setCurrent((long) pageNo);
        pageReq.setSize((long) pageSize);

        // 按照创建时间倒序
        OrderParam createTimeParam = new OrderParam();
        createTimeParam.setField("create_time");
        createTimeParam.setOrder("desc");
        pageReq.setOrderParam(Arrays.asList(createTimeParam));

        return costAnnualBudgetRepository.queryPageList(queryEntity, pageReq);
    }

    /**
     * 根据ID查询年度总预算详情
     *
     * @param id 年度总预算ID
     * @return 年度总预算详情
     */
    public CostAnnualBudgetEntity queryById(String id) {
        if (!StringUtils.hasText(id)) {
            throw new IllegalArgumentException("年度总预算ID不能为空");
        }
        return costAnnualBudgetRepository.findById(id);
    }

    /**
     * 根据ID查询年度总预算详情（包含明细数据）
     *
     * @param id 年度总预算ID
     * @return 年度总预算详情和明细数据
     */
    public CostAnnualBudgetEntity queryDetailById(String id) {
        if (!StringUtils.hasText(id)) {
            throw new IllegalArgumentException("年度总预算ID不能为空");
        }

        // 查询主表数据
        CostAnnualBudgetEntity entity = costAnnualBudgetRepository.findById(id);
        if (entity == null) {
            return null;
        }

        // 查询明细数据
        List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList =
            costAnnualBudgetDetailRepository.findByBudgetId(id);

        log.info("查询年度总预算详情成功，ID: {}, 预算编号: {}, 明细数量: {}",
                id, entity.getBudgetCode(), budgetDetailList != null ? budgetDetailList.size() : 0);

        return entity;
    }

    /**
     * 根据ID查询年度总预算明细数据
     *
     * @param id 年度总预算ID
     * @return 明细数据列表
     */
    public List<CostAnnualBudgetEntity.BudgetDetailInfo> queryBudgetDetailsByBudgetId(String id) {
        if (!StringUtils.hasText(id)) {
            throw new IllegalArgumentException("年度总预算ID不能为空");
        }

        return costAnnualBudgetDetailRepository.findByBudgetId(id);
    }

    /**
     * 新增年度总预算
     *
     * @param entity 年度总预算实体
     * @return 年度总预算ID
     */
    public String add(CostAnnualBudgetEntity entity) {
        if (entity == null) {
            throw new IllegalArgumentException("年度总预算数据不能为空");
        }

        // 验证必填字段
        validateRequiredFields(entity);

        // 验证预算编号唯一性
        if (StringUtils.hasText(entity.getBudgetCode())) {
            CostAnnualBudgetEntity existingEntity = costAnnualBudgetRepository.findByBudgetCode(entity.getBudgetCode());
            if (existingEntity != null) {
                throw new IllegalArgumentException("预算编号已存在：" + entity.getBudgetCode());
            }
        }


        CostAnnualBudgetEntity savedEntity = costAnnualBudgetRepository.save(entity);
        log.info("新增年度总预算成功，ID: {}, 预算编号: {}", savedEntity.getId(), savedEntity.getBudgetCode());
        
        return savedEntity.getId();
    }

    /**
     * 编辑年度总预算
     *
     * @param entity 年度总预算实体
     * @return 年度总预算ID
     */
    public String edit(CostAnnualBudgetEntity entity) {
        if (entity == null || !StringUtils.hasText(entity.getId())) {
            throw new IllegalArgumentException("年度总预算数据或ID不能为空");
        }

        // 验证记录是否存在
        CostAnnualBudgetEntity existingEntity = costAnnualBudgetRepository.findById(entity.getId());
        if (existingEntity == null) {
            throw new IllegalArgumentException("年度总预算不存在，ID: " + entity.getId());
        }

        // 验证是否可以修改
        if (!existingEntity.canModify()) {
            throw new IllegalArgumentException("当前状态不允许修改，状态: " + existingEntity.getBudgetStatus());
        }

        // 验证必填字段
        validateRequiredFields(entity);

        // 验证预算编号唯一性（排除自己）
        if (StringUtils.hasText(entity.getBudgetCode()) && !entity.getBudgetCode().equals(existingEntity.getBudgetCode())) {
            CostAnnualBudgetEntity duplicateEntity = costAnnualBudgetRepository.findByBudgetCode(entity.getBudgetCode());
            if (duplicateEntity != null && !duplicateEntity.getId().equals(entity.getId())) {
                throw new IllegalArgumentException("预算编号已存在：" + entity.getBudgetCode());
            }
        }


        CostAnnualBudgetEntity updatedEntity = costAnnualBudgetRepository.updateById(entity);
        log.info("编辑年度总预算成功，ID: {}, 预算编号: {}", updatedEntity.getId(), updatedEntity.getBudgetCode());
        
        return updatedEntity.getId();
    }

    /**
     * 根据ID删除年度总预算
     *
     * @param id 年度总预算ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        if (!StringUtils.hasText(id)) {
            throw new IllegalArgumentException("年度总预算ID不能为空");
        }

        // 验证记录是否存在
        CostAnnualBudgetEntity existingEntity = costAnnualBudgetRepository.findById(id);
        if (existingEntity == null) {
            throw new IllegalArgumentException("年度总预算不存在，ID: " + id);
        }

        // 验证是否可以删除
        if (!existingEntity.canDelete()) {
            throw new IllegalArgumentException("当前状态不允许删除，状态: " + existingEntity.getBudgetStatus());
        }

        String budgetCode = existingEntity.getBudgetCode();

        // 先删除关联的间接成本导入数据
        if (StringUtils.hasText(budgetCode)) {
            int deletedImportCount = centerCostImportRepository.deleteByBudgetCode(budgetCode);
            log.info("删除关联的间接成本导入数据，预算编号: {}, 删除数量: {}", budgetCode, deletedImportCount);
        }

        // 删除主表数据
        costAnnualBudgetRepository.deleteById(id);
        log.info("删除年度总预算成功，ID: {}, 预算编号: {}", id, budgetCode);
    }

    /**
     * 批量删除年度总预算
     *
     * @param ids 年度总预算ID列表，逗号分隔
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteBatch(String ids) {
        if (!StringUtils.hasText(ids)) {
            throw new IllegalArgumentException("年度总预算ID列表不能为空");
        }

        List<String> idList = Arrays.asList(ids.split(","));

        // 验证每个记录是否可以删除
        List<CostAnnualBudgetEntity> entities = costAnnualBudgetRepository.findByIds(idList);
        for (CostAnnualBudgetEntity entity : entities) {
            if (!entity.canDelete()) {
                throw new IllegalArgumentException("预算编号 " + entity.getBudgetCode() + " 当前状态不允许删除，状态: " + entity.getBudgetStatus());
            }
        }

        // 先删除关联的间接成本导入数据
        int totalDeletedImportCount = 0;
        for (CostAnnualBudgetEntity entity : entities) {
            String budgetCode = entity.getBudgetCode();
            if (StringUtils.hasText(budgetCode)) {
                int deletedImportCount = centerCostImportRepository.deleteByBudgetCode(budgetCode);
                totalDeletedImportCount += deletedImportCount;
                log.info("删除关联的间接成本导入数据，预算编号: {}, 删除数量: {}", budgetCode, deletedImportCount);
            }
        }

        // 删除主表数据
        costAnnualBudgetRepository.deleteByIds(idList);
        log.info("批量删除年度总预算成功，删除数量: {}, 删除关联间接成本导入数据总数: {}", idList.size(), totalDeletedImportCount);
    }

    /**
     * 生成下一个预算编号
     *
     * @return 下一个预算编号（ZYS+当前年份+3位流水）
     */
    public String generateNextBudgetCode() {
        // 获取当前年份
        String year = String.valueOf(java.time.Year.now().getValue());

        // 查询指定年份的最大预算编号
        String maxBudgetCode = costAnnualBudgetRepository.findMaxBudgetCodeByYear(year);

        int nextSequence = 1; // 默认从001开始

        if (StringUtils.hasText(maxBudgetCode)) {
            // 解析流水号：ZYS+4位年份+3位流水号
            String sequenceStr = maxBudgetCode.substring(7); // 去掉"ZYS"和4位年份
            try {
                int currentSequence = Integer.parseInt(sequenceStr);
                nextSequence = currentSequence + 1;
            } catch (NumberFormatException e) {
                log.warn("解析预算编号流水号失败，使用默认值001，预算编号: {}", maxBudgetCode);
            }
        }

        // 生成新的预算编号：ZYS+4位年份+3位流水号（补零）
        String nextBudgetCode = String.format("ZYS%s%03d", year, nextSequence);

        log.info("生成下一个预算编号成功，年份: {}, 预算编号: {}", year, nextBudgetCode);
        return nextBudgetCode;
    }

    /**
     * 保存年度预算（第一步）
     * 保存年度总预算主表信息、项目年度预算信息和直接成本明细
     *
     * @param mainBudget 年度总预算主表信息
     * @param budgetDetailList 项目年度预算明细列表
     * @return 年度总预算ID
     */
    @Transactional(rollbackFor = Exception.class)
    public String saveStep1(CostAnnualBudgetEntity mainBudget, List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList) {
        return saveStep1(mainBudget, budgetDetailList, false, null);
    }

    /**
     * 保存年度预算（第一步）- 支持变更操作
     * 保存年度总预算主表信息、项目年度预算信息和直接成本明细
     *
     * @param mainBudget 年度总预算主表信息
     * @param budgetDetailList 项目年度预算明细列表
     * @param isChange 是否为变更操作
     * @param originalBudgetId 原预算ID（变更时使用）
     * @return 年度总预算ID
     */
    @Transactional(rollbackFor = Exception.class)
    public String saveStep1(CostAnnualBudgetEntity mainBudget, List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList,
                           Boolean isChange, String originalBudgetId) {
        if (mainBudget == null) {
            throw new IllegalArgumentException("年度总预算数据不能为空");
        }
        // 验证必填字段
        validateRequiredFields(mainBudget);

        // 验证预算编号唯一性
        if (StringUtils.hasText(mainBudget.getBudgetCode())) {
            CostAnnualBudgetEntity existingEntity = costAnnualBudgetRepository.findByBudgetCode(mainBudget.getBudgetCode());
            if (existingEntity != null) {
                throw new IllegalArgumentException("预算编号已存在：" + mainBudget.getBudgetCode());
            }
        }

        // 保存主表
        CostAnnualBudgetEntity savedEntity = costAnnualBudgetRepository.save(mainBudget);

        // 处理变更逻辑（在保存后执行，确保有新预算编号）
        log.info("检查是否需要执行变更逻辑，isChange: {}, originalBudgetId: {}", isChange, originalBudgetId);

        // 严格检查：只有当isChange明确为true且originalBudgetId不为空时才执行变更逻辑
        if (Boolean.TRUE.equals(isChange) && StringUtils.hasText(originalBudgetId)) {
            log.info("✅ 满足变更条件，开始执行变更逻辑处理");
            handleBudgetChangeLogic(savedEntity, originalBudgetId, savedEntity.getBudgetCode());
        } else {
            log.info("❌ 不满足变更条件，跳过变更逻辑处理 (isChange={}, originalBudgetId={})",
                    isChange, originalBudgetId);
        }

        // 处理明细数据（设置关联关系）
        if (budgetDetailList != null && !budgetDetailList.isEmpty()) {
            log.info("开始处理 {} 条明细数据", budgetDetailList.size());
            processDetailData(savedEntity.getId(), budgetDetailList);
        }

        log.info("保存年度预算第一步成功，版本名称: {}, ID: {}, 是否变更: {}",
                mainBudget.getVersion(), savedEntity.getId(), isChange);
        return savedEntity.getId();
    }

    /**
     * 处理预算变更逻辑
     * 1. 设置父预算ID关联原预算
     * 2. 复制原预算的间接成本导入数据到新预算
     * 注意：原预算状态变更将在审批通过后处理，此处不做状态变更
     *
     * @param mainBudget 主预算信息
     * @param originalBudgetId 原预算ID
     * @param newBudgetCode 新预算编号
     */
    private void handleBudgetChangeLogic(CostAnnualBudgetEntity mainBudget, String originalBudgetId, String newBudgetCode) {
        log.info("🔄 开始处理预算变更逻辑，年份: {}, 所属单位: {}, 原预算ID: {}, 新预算编号: {}",
                mainBudget.getBudgetYear(), mainBudget.getProfessionalCompany(), originalBudgetId, newBudgetCode);

        // 安全检查：确保必要参数不为空
        if (!StringUtils.hasText(originalBudgetId)) {
            log.warn("⚠️ 原预算ID为空，无法执行变更逻辑");
            return;
        }

        if (!StringUtils.hasText(newBudgetCode)) {
            log.warn("⚠️ 新预算编号为空，无法执行变更逻辑");
            return;
        }

        // 1. 设置父预算ID关联原预算
        mainBudget.setParentBudgetId(originalBudgetId);
        log.info("✅ 设置父预算ID: {}", originalBudgetId);

        // 2. 复制原预算的间接成本导入数据
        copyOriginalBudgetImportData(originalBudgetId, newBudgetCode);

        // 注意：原预算状态变更逻辑将在审批通过后执行，此处仅建立关联关系
        log.info("✅ 预算变更关联设置完成，原预算状态将在审批通过后变更");
    }

    /**
     * 复制原预算的间接成本导入数据
     *
     * @param originalBudgetId 原预算ID
     * @param newBudgetCode 新预算编号
     */
    private void copyOriginalBudgetImportData(String originalBudgetId, String newBudgetCode) {
        log.info("准备复制间接成本导入数据，原预算ID: {}, 新预算编号: {}", originalBudgetId, newBudgetCode);

        try {
            // 1. 根据原预算ID查询原预算信息，获取预算编号
            CostAnnualBudgetEntity originalBudget = costAnnualBudgetRepository.findById(originalBudgetId);
            if (originalBudget == null) {
                log.warn("未找到原预算信息，原预算ID: {}", originalBudgetId);
                return;
            }

            String originalBudgetCode = originalBudget.getBudgetCode();
            log.info("查询到原预算信息，原预算编号: {}, 开始复制间接成本导入数据到新预算编号: {}",
                    originalBudgetCode, newBudgetCode);

            // 2. 复制间接成本导入数据
            int copiedCount = centerCostImportRepository.copyImportDataByBudgetCode(originalBudgetCode, newBudgetCode);

            if (copiedCount > 0) {
                log.info("✅ 成功复制间接成本导入数据，复制数量: {}", copiedCount);
            } else {
                log.info("ℹ️ 原预算没有间接成本导入数据，无需复制");
            }

        } catch (Exception e) {
            log.error("❌ 复制原预算间接成本导入数据失败，原预算ID: {}, 新预算编号: {}",
                    originalBudgetId, newBudgetCode, e);
            // 不抛出异常，避免影响主流程，仅记录错误日志
        }
    }

    /**
     * 编辑年度预算（第一步）
     * 编辑年度总预算主表信息、项目年度预算信息和直接成本明细
     * 采用"先删除再新增"的方式更新明细数据
     *
     * @param mainBudget 年度总预算主表信息
     * @param budgetDetailList 项目年度预算明细列表
     * @return 年度总预算ID
     */
    @Transactional(rollbackFor = Exception.class)
    public String editStep1(CostAnnualBudgetEntity mainBudget, List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList) {
        if (mainBudget == null || !StringUtils.hasText(mainBudget.getId())) {
            throw new IllegalArgumentException("年度总预算数据或ID不能为空");
        }

        // 验证记录是否存在
        CostAnnualBudgetEntity existingEntity = costAnnualBudgetRepository.findById(mainBudget.getId());
        if (existingEntity == null) {
            throw new IllegalArgumentException("年度总预算不存在，ID: " + mainBudget.getId());
        }

        // 验证是否可以修改
        if (!existingEntity.canModify()) {
            throw new IllegalArgumentException("当前状态不允许修改，状态: " + existingEntity.getBudgetStatus());
        }

        // 验证必填字段
        validateRequiredFields(mainBudget);

        // 验证预算编号唯一性（排除自己）
        if (StringUtils.hasText(mainBudget.getBudgetCode()) && !mainBudget.getBudgetCode().equals(existingEntity.getBudgetCode())) {
            CostAnnualBudgetEntity duplicateEntity = costAnnualBudgetRepository.findByBudgetCode(mainBudget.getBudgetCode());
            if (duplicateEntity != null && !duplicateEntity.getId().equals(mainBudget.getId())) {
                throw new IllegalArgumentException("预算编号已存在：" + mainBudget.getBudgetCode());
            }
        }

        // 更新主表
        CostAnnualBudgetEntity updatedEntity = costAnnualBudgetRepository.updateById(mainBudget);

        // 处理明细数据：先删除再新增
        if (budgetDetailList != null && !budgetDetailList.isEmpty()) {
            log.info("开始处理明细数据更新，先删除旧数据，再新增新数据，明细数量: {}", budgetDetailList.size());

            // 1. 先删除现有的明细数据
            costAnnualBudgetDetailRepository.deleteByBudgetId(mainBudget.getId());
            log.info("删除旧明细数据完成，budgetId: {}", mainBudget.getId());

            // 2. 重新保存新的明细数据
            processDetailData(mainBudget.getId(), budgetDetailList);
            log.info("保存新明细数据完成，budgetId: {}", mainBudget.getId());
        } else {
            // 如果没有明细数据，则删除所有现有明细数据
            log.info("没有新明细数据，删除所有现有明细数据，budgetId: {}", mainBudget.getId());
            costAnnualBudgetDetailRepository.deleteByBudgetId(mainBudget.getId());
        }

        log.info("编辑年度预算第一步成功，版本名称: {}, ID: {}", mainBudget.getVersion(), updatedEntity.getId());
        return updatedEntity.getId();
    }

    /**
     * 处理明细数据（设置关联关系和保存到数据库）
     */
    private void processDetailData(String budgetId, List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList) {
        if (budgetDetailList == null || budgetDetailList.isEmpty()) {
            log.info("没有明细数据需要处理，budgetId: {}", budgetId);
            return;
        }

        log.info("开始处理明细数据，budgetId: {}, 明细数量: {}", budgetId, budgetDetailList.size());

        // 设置关联关系
        for (CostAnnualBudgetEntity.BudgetDetailInfo detailInfo : budgetDetailList) {
            // 设置关联的预算主表ID
            detailInfo.setBudgetId(budgetId);

            // 处理直接成本明细
            if (detailInfo.getDirectCostList() != null && !detailInfo.getDirectCostList().isEmpty()) {
                for (CostAnnualBudgetEntity.DirectCostInfo directCostInfo : detailInfo.getDirectCostList()) {
                    // 设置关联的明细ID
                    directCostInfo.setBudgetDetailId(detailInfo.getId());
                }
            }
        }

        // 调用领域服务保存明细数据
        annualBudgetDetailDomainService.saveBudgetDetails(budgetId, budgetDetailList);

        log.info("明细数据处理完成，budgetId: {}", budgetId);
    }



    /**
     * 根据项目计划ID查询关联的预算科目信息
     *
     * @param projectPlanId 项目计划ID（可选参数）
     * @return 预算科目信息列表
     */
    public List<BudgetSubjectInfo> queryBudgetSubjects(String projectPlanId) {
        log.info("开始查询预算科目信息，项目计划ID: {}", projectPlanId);

        // 查询所有启用状态的预算科目
        List<CostBudgetSubjectEntity> subjectList = costBudgetSubjectRepository.findAllEnabled();
        List<BudgetSubjectInfo> resultList = new ArrayList<>();

        // 如果有项目计划ID，查询项目计划的直接成本总计
        BigDecimal directCostTotal = null;
        if (StringUtils.hasText(projectPlanId)) {
            try {
                CostProjectPlanEntity projectPlan = costProjectPlanRepository.queryByIdWithDetails(projectPlanId);
                if (projectPlan != null) {
                    directCostTotal = projectPlan.getDirectCostTotal();
                    log.info("查询到项目计划直接成本总计: {}", directCostTotal);
                } else {
                    log.warn("项目计划不存在，ID: {}", projectPlanId);
                }
            } catch (Exception e) {
                log.error("查询项目计划失败，ID: {}", projectPlanId, e);
            }
        }

        // 转换预算科目信息
        for (CostBudgetSubjectEntity subject : subjectList) {
            BudgetSubjectInfo info = new BudgetSubjectInfo();
            info.setSubjectCode(subject.getSubjectCode());
            info.setSubjectName(subject.getSubjectName());
            info.setSubjectDescription(subject.getSubjectDescription());
            info.setSortOrder(subject.getSortOrder());

            // 如果是"原材料及主要材料"科目且有项目计划ID，则设置预算金额
            if (directCostTotal != null && isRawMaterialSubject(subject)) {
                // directCostTotal单位是万元，需要转换为元：万元 × 10000 = 元
                BigDecimal budgetAmountInYuan = directCostTotal.multiply(new BigDecimal("10000"))
                    .setScale(2, java.math.RoundingMode.HALF_UP);
                info.setBudgetAmount(budgetAmountInYuan);
                log.info("为原材料科目设置预算金额: {} 万元 -> {} 元, 科目: {}",
                        directCostTotal, budgetAmountInYuan, subject.getSubjectName());
            } else {
                info.setBudgetAmount(BigDecimal.ZERO);
            }

            resultList.add(info);
        }

        log.info("查询预算科目信息完成，返回 {} 条记录", resultList.size());
        return resultList;
    }

    /**
     * 判断是否为原材料及主要材料科目
     *
     * @param subject 预算科目实体
     * @return 是否为原材料科目
     */
    private boolean isRawMaterialSubject(CostBudgetSubjectEntity subject) {
        if (subject == null || !StringUtils.hasText(subject.getSubjectName())) {
            return false;
        }

        String subjectName = subject.getSubjectName().trim();
        // 匹配"原材料及主要材料"或类似名称
        return subjectName.contains("原材料") && subjectName.contains("主要材料");
    }

    /**
     * 预算科目信息内部类
     */
    public static class BudgetSubjectInfo {
        private String subjectCode;
        private String subjectName;
        private String subjectDescription;
        private BigDecimal budgetAmount;
        private Integer sortOrder;

        // Getters and Setters
        public String getSubjectCode() { return subjectCode; }
        public void setSubjectCode(String subjectCode) { this.subjectCode = subjectCode; }

        public String getSubjectName() { return subjectName; }
        public void setSubjectName(String subjectName) { this.subjectName = subjectName; }

        public String getSubjectDescription() { return subjectDescription; }
        public void setSubjectDescription(String subjectDescription) { this.subjectDescription = subjectDescription; }

        public BigDecimal getBudgetAmount() { return budgetAmount; }
        public void setBudgetAmount(BigDecimal budgetAmount) { this.budgetAmount = budgetAmount; }

        public Integer getSortOrder() { return sortOrder; }
        public void setSortOrder(Integer sortOrder) { this.sortOrder = sortOrder; }
    }

    /**
     * 验证必填字段
     */
    private void validateRequiredFields(CostAnnualBudgetEntity entity) {
        if (!StringUtils.hasText(entity.getBudgetYear())) {
            throw new IllegalArgumentException("年份不能为空");
        }
        if (!StringUtils.hasText(entity.getProfessionalCompany())) {
            throw new IllegalArgumentException("所属单位不能为空");
        }
    }

    /**
     * 删除中心间接成本导入数据（根据预算编号和模版类型）
     * 封装对Infrastructure层的访问，避免Performance层直接依赖Infrastructure层
     *
     * @param budgetCode 预算编号
     * @param templateType 模版类型
     * @return 删除成功的数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteCenterCostImportData(String budgetCode, String templateType) {
        if (budgetCode == null || budgetCode.trim().isEmpty()) {
            log.warn("预算编号为空，无需删除导入数据");
            return 0;
        }
        if (templateType == null || templateType.trim().isEmpty()) {
            log.warn("模版类型为空，无需删除导入数据");
            return 0;
        }

        log.info("开始删除中心间接成本导入数据，预算编号: {}, 模版类型: {}", budgetCode, templateType);

        // 通过Repository接口删除数据，避免直接依赖Infrastructure层
        int deletedCount = centerCostImportRepository.deleteByBudgetCodeAndTemplateType(budgetCode, templateType);

        log.info("中心间接成本导入数据删除完成，数量: {}", deletedCount);
        return deletedCount;
    }

    /**
     * 保存中心间接成本导入数据
     * 封装对Infrastructure层的访问，避免Performance层直接依赖Infrastructure层
     *
     * @param importDataList 导入数据列表
     * @return 保存成功的数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int saveCenterCostImportData(List<CenterCostImportDTO> importDataList) {
        if (importDataList == null || importDataList.isEmpty()) {
            log.warn("导入数据列表为空");
            return 0;
        }

        log.info("开始保存中心间接成本导入数据，数量: {}", importDataList.size());

        // 通过Repository接口保存数据，避免直接依赖Infrastructure层
        int savedCount = centerCostImportRepository.saveBatch(importDataList);

        log.info("中心间接成本导入数据保存完成，数量: {}", savedCount);
        return savedCount;
    }

    /**
     * 根据预算ID和模版类型查询科目汇总数据
     * 用于第二步列表接口
     *
     * @param budgetId 年度预算ID
     * @param templateType 模版类型
     * @return 科目汇总列表
     */
    public List<CenterCostSubjectSummaryDTO> queryStep2SubjectSummary(String budgetId, String templateType) {
        log.info("开始查询第二步科目汇总数据，预算ID: {}, 模版类型: {}", budgetId, templateType);

        // 参数校验
        if (!StringUtils.hasText(budgetId)) {
            throw new IllegalArgumentException("年度预算ID不能为空");
        }
        if (!StringUtils.hasText(templateType)) {
            throw new IllegalArgumentException("模版类型不能为空");
        }

        // 验证模版类型是否有效
        if (!isValidTemplateType(templateType)) {
            throw new IllegalArgumentException("无效的模版类型：" + templateType);
        }

        // 根据预算ID查询预算编码
        CostAnnualBudgetEntity budgetEntity = costAnnualBudgetRepository.findById(budgetId);
        if (budgetEntity == null) {
            throw new IllegalArgumentException("年度预算不存在，ID: " + budgetId);
        }

        String budgetCode = budgetEntity.getBudgetCode();
        log.info("根据预算ID: {} 查询到预算编码: {}", budgetId, budgetCode);

        // 调用仓储层查询科目汇总数据
        List<CenterCostSubjectSummaryDTO> result = centerCostImportRepository.querySubjectSummaryByBudgetCodeAndTemplateType(budgetCode, templateType);

        log.info("查询第二步科目汇总数据完成，预算编码: {}, 模版类型: {}, 记录数: {}", budgetCode, templateType, result.size());
        return result;
    }

    /**
     * 验证模版类型是否有效
     */
    private boolean isValidTemplateType(String templateType) {
        return "this_center_indirect_cost_template".equals(templateType) ||
               "non_operational_center_indirect_cost_template".equals(templateType) ||
               "general_admin_indirect_cost_template".equals(templateType);
    }

    /**
     * 保存年度预算（第二步）
     * 执行间接成本分摊逻辑并保存分摊结果
     *
     * @param budgetId 年度预算ID
     * @return 保存结果
     */
    @Transactional(rollbackFor = Exception.class)
    public String saveStep2(String budgetId) {
        if (!StringUtils.hasText(budgetId)) {
            throw new IllegalArgumentException("年度预算ID不能为空");
        }

        log.info("开始执行年度预算第二步保存，budgetId: {}", budgetId);

        // 1. 验证预算是否存在
        CostAnnualBudgetEntity budgetEntity = costAnnualBudgetRepository.findById(budgetId);
        if (budgetEntity == null) {
            throw new IllegalArgumentException("年度预算不存在，ID: " + budgetId);
        }

        // 2. 获取预算明细数据（包含项目收入预算信息）
        List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList =
                costAnnualBudgetDetailRepository.findByBudgetId(budgetId);

        if (budgetDetailList == null || budgetDetailList.isEmpty()) {
            throw new IllegalArgumentException("该预算下没有明细数据，无法进行间接成本分摊");
        }

        // 3. 获取导入的中心间接成本数据
        List<CenterCostImportDTO> importDataList =
                centerCostImportRepository.findByBudgetCode(budgetEntity.getBudgetCode());

        if (importDataList == null || importDataList.isEmpty()) {
            log.warn("没有找到导入的中心间接成本数据，预算编号: {}", budgetEntity.getBudgetCode());
            return budgetId;
        }

        log.info("找到导入数据 {} 条，预算明细 {} 条，开始执行分摊计算",
                importDataList.size(), budgetDetailList.size());

        // 4. 执行分摊计算
        IndirectCostAllocationService.AllocationResult allocationResult =
                indirectCostAllocationService.allocateIndirectCost(importDataList, budgetDetailList);

        // 5. 先删除现有的间接成本数据（如果有的话）
        int deletedCount = indirectCostRepository.deleteAllIndirectCostByBudgetId(budgetId);
        if (deletedCount > 0) {
            log.info("删除现有间接成本数据 {} 条", deletedCount);
        }

        // 6. 保存分摊结果
        int savedCount = 0;

        // 保存本中心间接成本
        if (!allocationResult.getCenterIndirectCostList().isEmpty()) {
            int centerCount = indirectCostRepository.saveCenterIndirectCost(
                    allocationResult.getCenterIndirectCostList());
            savedCount += centerCount;
            log.info("保存本中心间接成本数据 {} 条", centerCount);
        }

        // 保存非经营中心间接成本
        if (!allocationResult.getNonOperatingIndirectCostList().isEmpty()) {
            int nonOperatingCount = indirectCostRepository.saveNonOperatingIndirectCost(
                    allocationResult.getNonOperatingIndirectCostList());
            savedCount += nonOperatingCount;
            log.info("保存非经营中心间接成本数据 {} 条", nonOperatingCount);
        }

        // 保存综合管理间接成本
        if (!allocationResult.getComprehensiveIndirectCostList().isEmpty()) {
            int comprehensiveCount = indirectCostRepository.saveComprehensiveIndirectCost(
                    allocationResult.getComprehensiveIndirectCostList());
            savedCount += comprehensiveCount;
            log.info("保存综合管理间接成本数据 {} 条", comprehensiveCount);
        }

        // 7. 计算并更新预算明细的统计值和总预算汇总统计值
        log.info("开始计算预算明细统计值和总预算汇总统计值");
        budgetStatisticsCalculationService.calculateAndUpdateStatistics(budgetId, budgetDetailList);
        log.info("预算明细统计值和总预算汇总统计值计算完成");

        log.info("年度预算第二步保存完成，budgetId: {}, 总共保存间接成本数据 {} 条", budgetId, savedCount);
        return budgetId;
    }

    /**
     * 根据项目年度预算明细ID查询完整详情信息
     * 包含主表信息和所有子表数据（直接成本、本中心间接成本、综合间接成本、非经营间接成本）
     *
     * @param budgetDetailId 项目年度预算明细ID
     * @return 完整详情信息
     */
    public CostAnnualBudgetDetailFullRepository.CostAnnualBudgetDetailFullInfo queryDetailFullById(String budgetDetailId) {
        if (!StringUtils.hasText(budgetDetailId)) {
            throw new IllegalArgumentException("项目年度预算明细ID不能为空");
        }

        log.info("开始查询项目年度预算完整详情，budgetDetailId: {}", budgetDetailId);

        // 调用仓储层查询完整详情信息
        CostAnnualBudgetDetailFullRepository.CostAnnualBudgetDetailFullInfo fullInfo =
            costAnnualBudgetDetailFullRepository.queryDetailFullById(budgetDetailId);

        if (fullInfo == null) {
            log.warn("项目年度预算明细不存在，budgetDetailId: {}", budgetDetailId);
            return null;
        }

        log.info("查询项目年度预算完整详情成功，budgetDetailId: {}, 项目名称: {}, 直接成本数量: {}, 本中心间接成本数量: {}, 综合间接成本数量: {}, 非经营间接成本数量: {}",
                budgetDetailId, fullInfo.getProjectName(),
                fullInfo.getDirectCostList() != null ? fullInfo.getDirectCostList().size() : 0,
                fullInfo.getCenterIndirectCostList() != null ? fullInfo.getCenterIndirectCostList().size() : 0,
                fullInfo.getComprehensiveIndirectCostList() != null ? fullInfo.getComprehensiveIndirectCostList().size() : 0,
                fullInfo.getNonOperatingIndirectCostList() != null ? fullInfo.getNonOperatingIndirectCostList().size() : 0);

        return fullInfo;
    }

    /**
     * 根据项目编号查询项目年度预算信息
     *
     * @param projectCode 项目编号
     * @return 项目年度预算信息列表
     */
    public List<CostAnnualBudgetDetailRepository.ProjectBudgetInfo> queryByProjectCode(String projectCode) {
        if (!StringUtils.hasText(projectCode)) {
            throw new IllegalArgumentException("项目编号不能为空");
        }

        log.info("开始查询项目年度预算信息，项目编号: {}", projectCode);

        // 调用领域服务查询数据
        List<CostAnnualBudgetDetailRepository.ProjectBudgetInfo> budgetInfoList =
            projectBudgetQueryService.queryByProjectCode(projectCode);

        log.info("查询项目年度预算信息完成，项目编号: {}, 查询到{}条记录",
                projectCode, budgetInfoList != null ? budgetInfoList.size() : 0);

        return budgetInfoList;
    }
}
