package com.cdkit.modules.cm.application.project;

import com.cdkit.common.exception.CdkitCloudException;
import com.cdkit.common.page.OrderParam;
import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.cdkit.config.mybatis.TenantIgnoreContext;
import com.cdkit.modules.cm.domain.project.mode.entity.CostProjectEntity;
import com.cdkit.modules.cm.domain.project.mode.entity.CostProjectWorkloadEntity;
import com.cdkit.modules.cm.domain.project.repository.CostProjectRepository;
import com.cdkit.modules.cm.domain.project.valobj.ProjectStageEnum;
import java.time.LocalDate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 市场项目台账应用服务
 * <AUTHOR>
 * @date 2025/07/14
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ProjectApplication {

    private final CostProjectRepository costProjectRepository;

    /**
     * 分页列表查询
     *
     * @param queryProject 查询条件
     * @param pageNo    页码
     * @param pageSize  每页数量
     * @return 查询结果
     */
    public PageRes<CostProjectEntity> queryPageList(CostProjectEntity queryProject, Integer pageNo, Integer pageSize) {
        PageReq pageReq = new PageReq();
        pageReq.setCurrent((long) pageNo);
        pageReq.setSize((long) pageSize);

        // 按照创建时间倒序
        OrderParam createTimeParam = new OrderParam();
        createTimeParam.setField("create_time");
        createTimeParam.setOrder("desc");

        pageReq.setOrderParam(Arrays.asList(createTimeParam));
        return costProjectRepository.page(queryProject, pageReq);
    }

    /**
     * 添加项目
     *
     * @param costProject 项目数据
     * @return 保存结果
     */
    public boolean add(CostProjectEntity costProject) {
        try {
            List<CostProjectWorkloadEntity> workloadList = costProject.getCostProjectWorkloadList();
            return costProjectRepository.saveMain(costProject, workloadList);
        } catch (Exception e) {
            log.error("添加项目失败", e);
            throw new CdkitCloudException("添加项目失败：" + e.getMessage());
        }
    }

    /**
     * 编辑项目
     *
     * @param costProject 项目数据
     * @return 更新结果
     */
    public boolean edit(CostProjectEntity costProject) {
        try {
            // 1. 查询原项目数据，检查项目阶段
            CostProjectEntity existingProject = costProjectRepository.getDomainById(costProject.getId());
            if (existingProject == null) {
                throw new CdkitCloudException("项目不存在，项目ID：" + costProject.getId());
            }

            // 2. 判断项目是否可以编辑
            if (!existingProject.canEdit()) {
                throw new CdkitCloudException("项目当前阶段为：" + existingProject.getProjectStageName() + "，只有待签合同和已签合同阶段的项目才能编辑");
            }

            // 3. 执行更新操作
            List<CostProjectWorkloadEntity> workloadList = costProject.getCostProjectWorkloadList();
            return costProjectRepository.updateMain(costProject, workloadList);
        } catch (Exception e) {
            log.error("编辑项目失败", e);
            throw new CdkitCloudException("编辑项目失败：" + e.getMessage());
        }
    }

    /**
     * 删除项目
     *
     * @param id 项目ID
     * @return 删除结果
     */
    public boolean delete(String id) {
        try {
            // 1. 查询项目数据，检查项目阶段
            CostProjectEntity existingProject = costProjectRepository.getDomainById(id);
            if (existingProject == null) {
                throw new CdkitCloudException("项目不存在，项目ID：" + id);
            }

            // 2. 判断项目是否可以删除
            if (!existingProject.canDelete()) {
                throw new CdkitCloudException("项目当前阶段为：" + existingProject.getProjectStageName() + "，只有待签合同和已签合同阶段的项目才能删除");
            }

            // 3. 执行删除操作
            return costProjectRepository.deleteMain(id);
        } catch (Exception e) {
            log.error("删除项目失败", e);
            throw new CdkitCloudException("删除项目失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除项目
     *
     * @param ids 项目ID列表
     * @return 删除结果
     */
    public boolean deleteBatch(List<String> ids) {
        try {
            // 1. 逐个检查项目是否可以删除
            for (String id : ids) {
                CostProjectEntity existingProject = costProjectRepository.getDomainById(id);
                if (existingProject == null) {
                    throw new CdkitCloudException("项目不存在，项目ID：" + id);
                }

                if (!existingProject.canDelete()) {
                    throw new CdkitCloudException("项目【" + existingProject.getProjectName() + "】当前阶段为：" +
                            existingProject.getProjectStageName() + "，只有待签合同和已签合同阶段的项目才能删除");
                }
            }

            // 2. 所有项目都可以删除，执行批量删除操作
            return costProjectRepository.deleteBatchMain(ids);
        } catch (Exception e) {
            log.error("批量删除项目失败", e);
            throw new CdkitCloudException("批量删除项目失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID查询项目详情
     *
     * @param id 项目ID
     * @return 项目详情
     */
    public CostProjectEntity queryById(String id) {
        return costProjectRepository.queryByIdWithWorkload(id);
    }

    /**
     * 根据项目ID查询工作量数据
     *
     * @param projectId 项目ID
     * @return 工作量列表
     */
    public List<CostProjectWorkloadEntity> queryWorkloadByProjectId(String projectId) {
        return costProjectRepository.queryWorkloadByProjectId(projectId);
    }

    /**
     * 获取导出数据列表（包含子表数据）
     *
     * @param queryProject 查询条件
     * @return 导出数据列表
     */
    public List<CostProjectEntity> getExportList(CostProjectEntity queryProject) {
        try {
            // 查询所有符合条件的项目数据
            PageReq pageReq = new PageReq();
            pageReq.setCurrent(1L);
            pageReq.setSize(10000L); // 设置一个较大的数值来获取所有数据

            PageRes<CostProjectEntity> pageRes = costProjectRepository.page(queryProject, pageReq);

            // 处理空结果的情况
            if (pageRes == null || pageRes.getRecords() == null) {
                log.info("查询结果为空，返回空列表");
                return new ArrayList<>();
            }

            List<CostProjectEntity> projectList = pageRes.getRecords();

            // 如果查询结果为空，直接返回空列表
            if (projectList.isEmpty()) {
                log.info("查询到0条项目数据，返回空列表");
                return new ArrayList<>();
            }

            // 为每个项目加载工作量数据
            for (CostProjectEntity project : projectList) {
                if (project != null && project.getId() != null) {
                    List<CostProjectWorkloadEntity> workloadList = costProjectRepository.queryWorkloadByProjectId(project.getId());
                    // 确保工作量列表不为null
                    project.setCostProjectWorkloadList(workloadList != null ? workloadList : new ArrayList<>());
                }
            }

            log.info("成功获取{}条项目导出数据（包含子表）", projectList.size());
            return projectList;
        } catch (Exception e) {
            log.error("获取导出数据失败", e);
            // 即使出现异常，也返回空列表而不是抛出异常，这样可以导出空Excel
            log.warn("获取导出数据失败，返回空列表以支持导出空Excel");
            return new ArrayList<>();
        }
    }

    /**
     * 获取主表导出数据列表（不包含子表数据）
     *
     * @param queryProject 查询条件
     * @return 主表导出数据列表
     */
    public List<CostProjectEntity> getMainTableExportList(CostProjectEntity queryProject) {
        try {
            // 查询所有符合条件的项目数据
            PageReq pageReq = new PageReq();
            pageReq.setCurrent(1L);
            pageReq.setSize(10000L); // 设置一个较大的数值来获取所有数据
            // 按照创建时间倒序
            // 按照创建时间倒序
            OrderParam createTimeParam = new OrderParam();
            createTimeParam.setField("create_time");
            createTimeParam.setOrder("desc");

            pageReq.setOrderParam(Arrays.asList(createTimeParam));
            PageRes<CostProjectEntity> pageRes = costProjectRepository.page(queryProject, pageReq);

            // 处理空结果的情况
            if (pageRes == null || pageRes.getRecords() == null) {
                log.info("查询结果为空，返回空列表");
                return new ArrayList<>();
            }

            List<CostProjectEntity> projectList = pageRes.getRecords();

            // 如果查询结果为空，直接返回空列表
            if (projectList.isEmpty()) {
                log.info("查询到0条项目数据，返回空列表");
                return new ArrayList<>();
            }

            // 只返回主表数据，不加载子表工作量数据
            // 清空子表数据，确保只导出主表信息
            for (CostProjectEntity project : projectList) {
                if (project != null) {
                    project.setCostProjectWorkloadList(null);
                }
            }

            log.info("成功获取{}条项目主表导出数据", projectList.size());
            return projectList;
        } catch (Exception e) {
            log.error("获取主表导出数据失败", e);
            // 即使出现异常，也返回空列表而不是抛出异常，这样可以导出空Excel
            log.warn("获取主表导出数据失败，返回空列表以支持导出空Excel");
            return new ArrayList<>();
        }
    }

    /**
     * 关联合同
     *
     * @param projectId 项目ID
     * @param contractCode 合同编号
     * @param contractName 合同名称
     * @param contractMode 合同模式
     * @param contractIssueDate 合同下发日期
     * @param contractEndDate 合同结束日期
     * @return 关联结果
     */
    public boolean linkContract(String projectId, String contractCode, String contractName,
                               String contractMode, Date contractIssueDate, Date contractEndDate) {
        try {
            // 1. 查询项目是否存在
            CostProjectEntity project = costProjectRepository.getDomainById(projectId);
            if (project == null) {
                throw new CdkitCloudException("项目不存在，项目ID：" + projectId);
            }

            // 2. 判断项目是否可以关联合同
            if (!project.canLinkContract()) {
                throw new CdkitCloudException("项目【" + project.getProjectName() + "】当前阶段为：" +
                        project.getProjectStageName() + "，只有待签合同阶段的项目才能关联合同");
            }

            // 3. 更新合同信息和项目阶段
            project.setContractCode(contractCode);
            project.setContractName(contractName);
            project.setContractMode(contractMode);
            project.setContractIssueDate(contractIssueDate);
            project.setContractEndDate(contractEndDate);
            project.setProjectStage(ProjectStageEnum.CONTRACT_SIGNED.getCode());

            // 4. 保存更新
            costProjectRepository.updateDomainById(project);

            log.info("项目关联合同成功，项目ID：{}，合同编号：{}，合同名称：{}，项目阶段已更新为：{}",
                    projectId, contractCode, contractName, ProjectStageEnum.CONTRACT_SIGNED.getName());
            return true;
        } catch (Exception e) {
            log.error("项目关联合同失败，项目ID：{}", projectId, e);
            throw new CdkitCloudException("项目关联合同失败：" + e.getMessage());
        }
    }

    /**
     * 执行项目
     * 将已签合同阶段的项目更新为执行中阶段
     *
     * @param projectId 项目ID
     * @return 执行结果
     */
    public boolean executeProject(String projectId) {
        try {
            // 1. 查询项目是否存在
            CostProjectEntity project = costProjectRepository.getDomainById(projectId);
            if (project == null) {
                throw new CdkitCloudException("项目不存在，项目ID：" + projectId);
            }

            // 2. 判断项目是否可以执行
            if (!project.canExecute()) {
                throw new CdkitCloudException("项目【" + project.getProjectName() + "】当前阶段为：" +
                        project.getProjectStageName() + "，只有已签合同阶段的项目才能执行");
            }

            // 3. 更新项目阶段为执行中
            project.setProjectStage(ProjectStageEnum.IN_EXECUTION.getCode());

            // 4. 保存更新
            costProjectRepository.updateDomainById(project);

            log.info("项目执行成功，项目ID：{}，项目名称：{}，项目阶段已更新为：{}",
                    projectId, project.getProjectName(), ProjectStageEnum.IN_EXECUTION.getName());
            return true;
        } catch (Exception e) {
            log.error("项目执行失败，项目ID：{}", projectId, e);
            throw new CdkitCloudException("项目执行失败：" + e.getMessage());
        }
    }

    /**
     * 关闭项目
     * 将执行中阶段的项目更新为已关闭阶段
     *
     * @param projectId 项目ID
     * @return 关闭结果
     */
    public boolean closeProject(String projectId) {
        try {
            // 1. 查询项目是否存在
            CostProjectEntity project = costProjectRepository.getDomainById(projectId);
            if (project == null) {
                throw new CdkitCloudException("项目不存在，项目ID：" + projectId);
            }

            // 2. 判断项目是否处于执行中阶段
            if (!ProjectStageEnum.IN_EXECUTION.getCode().equals(project.getProjectStage())) {
                log.warn("项目【{}】当前阶段为：{}，只有执行中阶段的项目才能关闭",
                        project.getProjectName(), project.getProjectStageName());
                return false;
            }

            // 3. 更新项目阶段为已关闭
            project.setProjectStage(ProjectStageEnum.CLOSED.getCode());

            // 4. 保存更新
            costProjectRepository.updateDomainById(project);

            log.info("项目关闭成功，项目ID：{}，项目名称：{}，项目阶段已更新为：{}",
                    projectId, project.getProjectName(), ProjectStageEnum.CLOSED.getName());
            return true;
        } catch (Exception e) {
            log.error("项目关闭失败，项目ID：{}", projectId, e);
            throw new CdkitCloudException("项目关闭失败：" + e.getMessage());
        }
    }

    /**
     * 批量关闭指定年度的执行中项目
     * 当进入下一年度时，自动关闭上一年度执行中的项目
     *
     * @param projectYear 项目年度
     * @return 关闭的项目数量
     */
    public int closeInExecutionProjectsByYear(String projectYear) {
        try {
            // 1. 查询指定年度执行中的项目
            List<CostProjectEntity> inExecutionProjects = costProjectRepository.queryInExecutionProjectsByYear(projectYear);

            if (inExecutionProjects.isEmpty()) {
                log.info("{}年度没有执行中的项目需要关闭", projectYear);
                return 0;
            }

            // 2. 批量关闭项目
            int closedCount = 0;
            for (CostProjectEntity project : inExecutionProjects) {
                try {
                    // 更新项目阶段为已关闭
                    project.setProjectStage(ProjectStageEnum.CLOSED.getCode());
                    costProjectRepository.updateDomainById(project);
                    closedCount++;

                    log.info("自动关闭项目成功，项目ID：{}，项目名称：{}，项目年度：{}",
                            project.getId(), project.getProjectName(), projectYear);
                } catch (Exception e) {
                    log.error("自动关闭项目失败，项目ID：{}，项目名称：{}，错误信息：{}",
                            project.getId(), project.getProjectName(), e.getMessage());
                }
            }

            log.info("{}年度项目自动关闭完成，共关闭{}个项目", projectYear, closedCount);
            return closedCount;
        } catch (Exception e) {
            log.error("批量关闭{}年度执行中项目失败", projectYear, e);
            throw new CdkitCloudException("批量关闭项目失败：" + e.getMessage());
        }
    }

    /**
     * 查询指定年度执行中的项目列表
     *
     * @param projectYear 项目年度
     * @return 执行中的项目列表
     */
    public List<CostProjectEntity> queryInExecutionProjectsByYear(String projectYear) {
        try {
            return costProjectRepository.queryInExecutionProjectsByYear(projectYear);
        } catch (Exception e) {
            log.error("查询{}年度执行中项目失败", projectYear, e);
            throw new CdkitCloudException("查询执行中项目失败：" + e.getMessage());
        }
    }


}
