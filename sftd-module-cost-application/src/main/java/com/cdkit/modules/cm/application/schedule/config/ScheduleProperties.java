package com.cdkit.modules.cm.application.schedule.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 定时任务配置属性
 * 用于配置定时任务的相关参数
 * 
 * <AUTHOR>
 * @date 2025/07/14
 */
@Data
@Component
@ConfigurationProperties(prefix = "cost.schedule")
public class ScheduleProperties {

    /**
     * 是否启用项目自动关闭定时任务
     * 默认启用
     */
    private boolean enableProjectAutoClose = true;

    /**
     * 项目自动关闭定时任务的cron表达式
     * 默认每年1月1日凌晨1点执行
     */
    private String projectAutoCloseCron = "0 0 1 1 1 ?";

    /**
     * 是否启用测试定时任务
     * 默认关闭，仅用于开发测试
     */
    private boolean enableTestTask = false;

    /**
     * 测试定时任务的cron表达式
     * 默认每天凌晨2点执行
     */
    private String testTaskCron = "0 0 2 * * ?";

    /**
     * 定时任务执行超时时间（秒）
     * 默认30分钟
     */
    private int taskTimeoutSeconds = 1800;
}
