package com.cdkit.modules.cm.application.plm;

import com.cdkit.modules.cm.domain.gateway.plm.ProductFileGateway;
import com.cdkit.modules.cm.domain.gateway.plm.entity.ProductFileTreeEntity;
import com.cdkit.modules.cm.domain.gateway.plm.entity.ProductRecipeInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 产品档案应用服务
 * <AUTHOR>
 * @date 2025/07/18
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ProductFileApplication {
    
    private final ProductFileGateway productFileGateway;
    
    /**
     * 获取产品档案树结构
     * 
     * @param productCode 产品编码
     * @return 产品档案树结构
     */
    public List<ProductFileTreeEntity> getProductFileTree(String productCode) {
        if (!StringUtils.hasText(productCode)) {
            log.warn("产品编码不能为空");
            return new ArrayList<>();
        }
        
        log.info("获取产品档案树结构 - productCode: {}", productCode);
        return productFileGateway.getProductFileTree(productCode);
    }
    
    /**
     * 获取产品档案树最后节点的物料编码列表
     * 
     * @param productCode 产品编码
     * @return 最后节点的物料编码列表
     */
    public List<String> getLastNodeMaterialCodes(String productCode) {
        if (!StringUtils.hasText(productCode)) {
            log.warn("产品编码不能为空");
            return new ArrayList<>();
        }
        
        log.info("获取产品档案树最后节点物料编码 - productCode: {}", productCode);
        return productFileGateway.getLastNodeMaterialCodes(productCode);
    }
    
    /**
     * 批量获取多个产品的最后节点物料编码
     *
     * @param productCodes 产品编码列表
     * @return 所有产品的最后节点物料编码列表
     */
    public List<String> getBatchLastNodeMaterialCodes(List<String> productCodes) {
        if (productCodes == null || productCodes.isEmpty()) {
            log.warn("产品编码列表不能为空");
            return new ArrayList<>();
        }

        log.info("批量获取产品档案树最后节点物料编码 - productCodes: {}", productCodes);

        List<String> allMaterialCodes = new ArrayList<>();

        for (String productCode : productCodes) {
            if (StringUtils.hasText(productCode)) {
                List<String> materialCodes = productFileGateway.getLastNodeMaterialCodes(productCode);
                allMaterialCodes.addAll(materialCodes);
            }
        }

        log.info("批量获取完成，共获取{}个物料编码", allMaterialCodes.size());
        return allMaterialCodes;
    }

    /**
     * 获取产品配方信息
     * 包含第一层的配方信息（配方名称、配方编号）和最底层的物料列表
     *
     * @param productCode 产品编码
     * @return 产品配方信息
     */
    public ProductRecipeInfo getProductRecipeInfo(String productCode) {
        if (!StringUtils.hasText(productCode)) {
            log.warn("产品编码不能为空");
            return null;
        }

        log.info("获取产品配方信息 - productCode: {}", productCode);
        return productFileGateway.getProductRecipeInfo(productCode);
    }

    /**
     * 批量获取多个产品的配方信息
     *
     * @param productCodes 产品编码列表
     * @return 产品配方信息列表
     */
    public List<ProductRecipeInfo> getBatchProductRecipeInfo(List<String> productCodes) {
        if (productCodes == null || productCodes.isEmpty()) {
            log.warn("产品编码列表不能为空");
            return new ArrayList<>();
        }

        log.info("批量获取产品配方信息 - productCodes: {}", productCodes);

        List<ProductRecipeInfo> recipeInfoList = new ArrayList<>();

        for (String productCode : productCodes) {
            if (StringUtils.hasText(productCode)) {
                ProductRecipeInfo recipeInfo = productFileGateway.getProductRecipeInfo(productCode);
                if (recipeInfo != null) {
                    recipeInfoList.add(recipeInfo);
                }
            }
        }

        log.info("批量获取完成，共获取{}个产品配方信息", recipeInfoList.size());
        return recipeInfoList;
    }
}
