<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.cdkitframework.boot</groupId>
        <artifactId>sftd-module-cost-budget</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>sftd-module-cost-application</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.cdkitframework.boot</groupId>
            <artifactId>sftd-module-cost-domain</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.cdkitframework.boot</groupId>
            <artifactId>sftd-module-cost-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.cdkitframework.boot</groupId>
            <artifactId>cdkit-system-cloud-api</artifactId>
            <version>3.7.0-SNAPSHOT</version>
        </dependency>

        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>