package com.cdkit.modules.cm.performance.project;

import cn.hutool.core.bean.BeanUtil;
import com.cdkit.modules.cm.api.project.dto.CostProjectDTO;
import com.cdkit.modules.cm.api.project.dto.CostProjectWorkloadDTO;
import com.cdkit.modules.cm.domain.project.mode.entity.CostProjectEntity;
import com.cdkit.modules.cm.domain.project.mode.entity.CostProjectWorkloadEntity;

import java.util.List;

/**
 * 项目表现层转换器
 * <AUTHOR>
 * @date 2025/07/14
 */
public class CostProjectConverter {

    /**
     * DTO转领域实体
     */
    public static CostProjectEntity toEntity(CostProjectDTO dto) {
        if (dto == null) {
            return null;
        }
        CostProjectEntity entity = BeanUtil.copyProperties(dto, CostProjectEntity.class);
        
        // 转换工作量列表
        if (dto.getCostProjectWorkloadList() != null) {
            List<CostProjectWorkloadEntity> workloadList = BeanUtil.copyToList(
                    dto.getCostProjectWorkloadList(), CostProjectWorkloadEntity.class);
            entity.setCostProjectWorkloadList(workloadList);
        }
        
        return entity;
    }

    /**
     * 领域实体转DTO
     */
    public static CostProjectDTO toDTO(CostProjectEntity entity) {
        if (entity == null) {
            return null;
        }
        CostProjectDTO dto = BeanUtil.copyProperties(entity, CostProjectDTO.class);
        
        // 转换工作量列表
        if (entity.getCostProjectWorkloadList() != null) {
            List<CostProjectWorkloadDTO> workloadList = BeanUtil.copyToList(
                    entity.getCostProjectWorkloadList(), CostProjectWorkloadDTO.class);
            dto.setCostProjectWorkloadList(workloadList);
        }
        
        return dto;
    }

    /**
     * 领域实体列表转DTO列表
     */
    public static List<CostProjectDTO> toDTOList(List<CostProjectEntity> entityList) {
        return BeanUtil.copyToList(entityList, CostProjectDTO.class);
    }

    /**
     * DTO列表转领域实体列表
     */
    public static List<CostProjectEntity> toEntityList(List<CostProjectDTO> dtoList) {
        return BeanUtil.copyToList(dtoList, CostProjectEntity.class);
    }

    /**
     * 工作量DTO转领域实体
     */
    public static CostProjectWorkloadEntity toWorkloadEntity(CostProjectWorkloadDTO dto) {
        if (dto == null) {
            return null;
        }
        return BeanUtil.copyProperties(dto, CostProjectWorkloadEntity.class);
    }

    /**
     * 工作量领域实体转DTO
     */
    public static CostProjectWorkloadDTO toWorkloadDTO(CostProjectWorkloadEntity entity) {
        if (entity == null) {
            return null;
        }
        return BeanUtil.copyProperties(entity, CostProjectWorkloadDTO.class);
    }

    /**
     * 工作量领域实体列表转DTO列表
     */
    public static List<CostProjectWorkloadDTO> toWorkloadDTOList(List<CostProjectWorkloadEntity> entityList) {
        return BeanUtil.copyToList(entityList, CostProjectWorkloadDTO.class);
    }

    /**
     * 工作量DTO列表转领域实体列表
     */
    public static List<CostProjectWorkloadEntity> toWorkloadEntityList(List<CostProjectWorkloadDTO> dtoList) {
        return BeanUtil.copyToList(dtoList, CostProjectWorkloadEntity.class);
    }
}
