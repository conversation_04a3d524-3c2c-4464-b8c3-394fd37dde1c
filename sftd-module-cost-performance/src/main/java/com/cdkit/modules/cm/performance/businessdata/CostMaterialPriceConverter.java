package com.cdkit.modules.cm.performance.businessdata;

import cn.hutool.core.bean.BeanUtil;
import com.cdkit.modules.cm.api.businessdata.dto.CostMaterialPriceAddRequest;
import com.cdkit.modules.cm.api.businessdata.dto.CostMaterialPriceDTO;
import com.cdkit.modules.cm.api.businessdata.dto.CostMaterialPriceEditRequest;
import com.cdkit.modules.cm.domain.businessdata.model.entity.CostMaterialPriceEntity;

import java.util.List;

/**
 * 材料单价表现层转换器
 * <AUTHOR>
 * @date 2025/07/16
 */
public class CostMaterialPriceConverter {

    /**
     * 领域实体转DTO
     */
    public static CostMaterialPriceDTO toDTO(CostMaterialPriceEntity entity) {
        if (entity == null) {
            return null;
        }
        return BeanUtil.copyProperties(entity, CostMaterialPriceDTO.class);
    }

    /**
     * DTO转领域实体
     */
    public static CostMaterialPriceEntity toEntity(CostMaterialPriceDTO dto) {
        if (dto == null) {
            return null;
        }
        return BeanUtil.copyProperties(dto, CostMaterialPriceEntity.class);
    }

    /**
     * 新增请求转领域实体
     */
    public static CostMaterialPriceEntity toEntity(CostMaterialPriceAddRequest request) {
        if (request == null) {
            return null;
        }
        return BeanUtil.copyProperties(request, CostMaterialPriceEntity.class);
    }

    /**
     * 编辑请求转领域实体
     */
    public static CostMaterialPriceEntity toEntity(CostMaterialPriceEditRequest request) {
        if (request == null) {
            return null;
        }
        return BeanUtil.copyProperties(request, CostMaterialPriceEntity.class);
    }

    /**
     * 领域实体列表转DTO列表
     */
    public static List<CostMaterialPriceDTO> toDTOList(List<CostMaterialPriceEntity> entityList) {
        return BeanUtil.copyToList(entityList, CostMaterialPriceDTO.class);
    }

    /**
     * DTO列表转领域实体列表
     */
    public static List<CostMaterialPriceEntity> toEntityList(List<CostMaterialPriceDTO> dtoList) {
        return BeanUtil.copyToList(dtoList, CostMaterialPriceEntity.class);
    }
}
