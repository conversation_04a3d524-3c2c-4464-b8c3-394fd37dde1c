package com.cdkit.modules.cm.performance.businessdata;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.common.api.vo.Result;
import com.cdkit.common.exception.CdkitCloudException;
import com.cdkit.common.page.PageRes;
import com.cdkit.common.system.vo.LoginUser;
import com.cdkit.modules.cm.api.businessdata.IMaterialPriceApi;
import com.cdkit.modules.cm.api.businessdata.dto.CostMaterialPriceAddRequest;
import com.cdkit.modules.cm.api.businessdata.dto.CostMaterialPriceDTO;
import com.cdkit.modules.cm.api.businessdata.dto.CostMaterialPriceEditRequest;
import com.cdkit.modules.cm.application.businessdata.MaterialPriceApplication;
import com.cdkit.modules.cm.domain.gateway.material.entity.MaterialDetailDTO;
import com.cdkit.modules.cm.domain.businessdata.model.entity.CostMaterialPriceEntity;
import com.cdkitframework.poi.excel.ExcelImportUtil;
import com.cdkitframework.poi.excel.def.NormalExcelConstants;
import com.cdkitframework.poi.excel.entity.ExportParams;
import com.cdkitframework.poi.excel.entity.ImportParams;
import com.cdkitframework.poi.excel.view.CdkitEntityExcelView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @Description: 材料单价管理控制器
 * @Author: sunhzh
 * @Date:   2025-07-16
 * @Version: V1.0
 */
@Tag(name="材料单价管理")
@RestController
@RequestMapping("/cm/costMaterialPrice")
@Slf4j
@RequiredArgsConstructor
@Validated
public class CostMaterialPriceController implements IMaterialPriceApi {

    private final MaterialPriceApplication materialPriceApplication;

    /**
     * 分页列表查询
     *
     * @param materialName 物料名称
     * @param materialCode 物料编码
     * @param materialPriceStatus 状态
     * @param priceType 类型
     * @param source 来源
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @return 查询结果
     */
    @Operation(summary="材料单价管理-分页列表查询")
    @Override
    public Result<IPage<CostMaterialPriceDTO>> queryPageList(
            @RequestParam(name="materialName", required=false) String materialName,
            @RequestParam(name="materialCode", required=false) String materialCode,
            @RequestParam(name="materialPriceStatus", required=false) String materialPriceStatus,
            @RequestParam(name="priceType", required=false) String priceType,
            @RequestParam(name="source", required=false) String source,
            @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
            @RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {

        // 构建查询条件
        CostMaterialPriceEntity queryEntity = new CostMaterialPriceEntity()
                .setMaterialName(materialName)
                .setMaterialCode(materialCode)
                .setMaterialPriceStatus(materialPriceStatus)
                .setPriceType(priceType)
                .setSource(source);

        PageRes<CostMaterialPriceEntity> pageResult = materialPriceApplication.queryPageList(queryEntity, pageNo, pageSize);

        // 转换为DTO
        List<CostMaterialPriceDTO> dtoList = CostMaterialPriceConverter.toDTOList(pageResult.getRecords());


        IPage<CostMaterialPriceDTO> page = new Page<CostMaterialPriceDTO>(pageNo, pageSize);
        if (pageResult != null) {
            page.setCurrent(pageResult.getCurrent());
            page.setSize(pageResult.getSize());
            page.setTotal(pageResult.getTotal());
            page.setRecords(dtoList);
        }

        return Result.OK(page);
    }

    /**
     * 新增材料单价
     *
     * @param request 新增请求
     * @return 操作结果
     */
    @Operation(summary="材料单价管理-新增")
    @Override
    public Result<String> add(@Valid @RequestBody CostMaterialPriceAddRequest request) {
        CostMaterialPriceEntity entity = CostMaterialPriceConverter.toEntity(request);
        materialPriceApplication.add(entity);
        return Result.OK("新增成功！");
    }

    /**
     * 编辑材料单价
     *
     * @param request 编辑请求
     * @return 操作结果
     */
    @Operation(summary="材料单价管理-编辑")
    @Override
    public Result<String> edit(@Valid @RequestBody CostMaterialPriceEditRequest request) {
        CostMaterialPriceEntity entity = CostMaterialPriceConverter.toEntity(request);
        materialPriceApplication.edit(entity);
        return Result.OK("编辑成功！");
    }

    /**
     * 删除材料单价
     *
     * @param id 主键ID
     * @return 操作结果
     */
    @Operation(summary="材料单价管理-删除")
    @Override
    public Result<String> delete(@RequestParam(name="id", required=true) String id) {
        materialPriceApplication.delete(id);
        return Result.OK("删除成功！");
    }

    /**
     * 批量删除材料单价
     *
     * @param ids ID列表，逗号分隔
     * @return 操作结果
     */
    @Operation(summary="材料单价管理-批量删除")
    @Override
    public Result<String> deleteBatch(@RequestParam(name="ids", required=true) String ids) {
        List<String> idList = Arrays.asList(ids.split(","));
        materialPriceApplication.deleteBatch(idList);
        return Result.OK("批量删除成功！");
    }

    /**
     * 提交材料单价
     *
     * @param id 主键ID
     * @return 操作结果
     */
    @Operation(summary="材料单价管理-提交")
    @Override
    public Result<String> submit(@RequestParam(name="id", required=true) String id) {
        materialPriceApplication.submit(id);
        return Result.OK("提交成功！");
    }

    /**
     * 通过id查询
     *
     * @param id 主键ID
     * @return 查询结果
     */
    @Operation(summary="材料单价管理-通过id查询")
    @Override
    public Result<CostMaterialPriceDTO> queryById(@RequestParam(name="id", required=true) String id) {
        CostMaterialPriceEntity entity = materialPriceApplication.queryById(id);
        if (entity == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(CostMaterialPriceConverter.toDTO(entity));
    }

    /**
     * 获取物料最新价格数据（用于新增时回显）
     *
     * @param materialCode 物料编码
     * @return 最新价格数据
     */
    @Operation(summary="材料单价管理-获取物料最新价格")
    @Override
    public Result<CostMaterialPriceDTO> getLatestPrice(@RequestParam(name="materialCode", required=true) String materialCode) {
        CostMaterialPriceEntity entity = materialPriceApplication.getLatestPriceForEcho(materialCode);
        if (entity == null) {
            return Result.OK(null);
        }
        return Result.OK(CostMaterialPriceConverter.toDTO(entity));
    }

    /**
     * 材料选择分页接口
     * 调用IMdMaterialApi.listByPage方法
     *
     * @param materialCode 物料编码
     * @param materialName 物料名称
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @return 查询结果
     */
    @Operation(summary="材料单价管理-材料选择分页查询")
    @Override
    public  Page<MaterialDetailDTO>  selectMaterialByPage(
            @RequestParam(name="materialCode", required=false) String materialCode,
            @RequestParam(name="materialName", required=false) String materialName,
            @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
            @RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {

        return materialPriceApplication.selectMaterialByPage(materialCode, materialName, pageNo, pageSize);
    }

    /**
     * 导出excel
     *
     * @param costMaterialPriceDTO 查询条件
     */
    @Operation(summary="材料单价管理-导出excel")
    @Override
    public ModelAndView exportXls(CostMaterialPriceDTO costMaterialPriceDTO) {
        try {
            // Step.1 获取当前用户信息
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            String userName = "系统用户";
            if (sysUser != null) {
                userName = sysUser.getRealname().toString();
            }

            // Step.2 转换查询条件为领域实体
            CostMaterialPriceEntity queryEntity = null;
            if (costMaterialPriceDTO != null) {
                queryEntity = CostMaterialPriceConverter.toEntity(costMaterialPriceDTO);
            } else {
                queryEntity = new CostMaterialPriceEntity();
            }

            // Step.3 通过应用层获取导出数据
            List<CostMaterialPriceEntity> entityList = materialPriceApplication.getExportList(queryEntity);

            // Step.4 转换为DTO列表
            List<CostMaterialPriceDTO> exportList = CostMaterialPriceConverter.toDTOList(entityList);

            // 确保导出列表不为null
            if (exportList == null) {
                exportList = Arrays.asList();
            }

            // Step.5 使用Cdkit包的导出逻辑 - 使用CostMaterialPriceDTO导出
            ModelAndView mv = new ModelAndView(new CdkitEntityExcelView());
            mv.addObject(NormalExcelConstants.FILE_NAME, "材料单价管理列表");
            mv.addObject(NormalExcelConstants.CLASS, CostMaterialPriceDTO.class);
            mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("材料单价管理数据", "导出人:" + userName, "材料单价管理"));
            mv.addObject(NormalExcelConstants.DATA_LIST, exportList);

            log.info("导出Excel成功，共{}条数据", exportList.size());
            return mv;
        } catch (Exception e) {
            log.error("导出Excel失败", e);
            // 即使出现异常，也尝试导出空Excel
            try {
                ModelAndView mv = new ModelAndView(new CdkitEntityExcelView());
                mv.addObject(NormalExcelConstants.FILE_NAME, "材料单价管理列表");
                mv.addObject(NormalExcelConstants.CLASS, CostMaterialPriceDTO.class);
                mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("材料单价管理数据", "导出人:系统用户", "材料单价管理"));
                mv.addObject(NormalExcelConstants.DATA_LIST, Arrays.asList());
                log.warn("导出异常，返回空Excel文件");
                return mv;
            } catch (Exception ex) {
                log.error("导出空Excel也失败", ex);
                throw new CdkitCloudException("导出Excel失败：" + e.getMessage());
            }
        }
    }
    @Operation(summary="材料单价管理-导出excel模版")
    @Override
    public ModelAndView exportXlsTemplate(CostMaterialPriceDTO costMaterialPriceDTO) {
        try {
            // Step.1 获取当前用户信息
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            String userName = "系统用户";
            if (sysUser != null) {
                userName = sysUser.getRealname().toString();
            }

            // Step.4 转换为DTO列表
            List<CostMaterialPriceDTO> exportList = new ArrayList<>();

            // Step.5 使用Cdkit包的导出逻辑 - 使用CostMaterialPriceDTO导出
            ModelAndView mv = new ModelAndView(new CdkitEntityExcelView());
            mv.addObject(NormalExcelConstants.FILE_NAME, "材料单价管理列表");
            mv.addObject(NormalExcelConstants.CLASS, CostMaterialPriceDTO.class);
            mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("材料单价管理数据", "导出人:" + userName, "材料单价管理"));
            mv.addObject(NormalExcelConstants.DATA_LIST, exportList);

            log.info("导出Excel成功，共{}条数据", exportList.size());
            return mv;
        } catch (Exception e) {
            log.error("导出Excel失败", e);
            // 即使出现异常，也尝试导出空Excel
            try {
                ModelAndView mv = new ModelAndView(new CdkitEntityExcelView());
                mv.addObject(NormalExcelConstants.FILE_NAME, "材料单价管理列表");
                mv.addObject(NormalExcelConstants.CLASS, CostMaterialPriceDTO.class);
                mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("材料单价管理数据", "导出人:系统用户", "材料单价管理"));
                mv.addObject(NormalExcelConstants.DATA_LIST, Arrays.asList());
                log.warn("导出异常，返回空Excel文件");
                return mv;
            } catch (Exception ex) {
                log.error("导出空Excel也失败", ex);
                throw new CdkitCloudException("导出Excel失败：" + e.getMessage());
            }
        }
    }

    @Operation(summary="材料单价管理-导入excel")
    @Override
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            // 获取上传文件对象
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<CostMaterialPriceDTO> list = ExcelImportUtil.importExcel(file.getInputStream(), CostMaterialPriceDTO.class, params);
                for (CostMaterialPriceDTO dto : list) {
                    // 将DTO转换为Entity
                    CostMaterialPriceEntity costMaterialPriceDTO = CostMaterialPriceConverter.toEntity(dto);
                    materialPriceApplication.add(costMaterialPriceDTO);
                }
                return Result.OK("文件导入成功！数据行数:" + list.size());
            } catch (Exception e) {
                log.error(e.getMessage(),e);
                return Result.error("文件导入失败:"+e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.OK("文件导入失败！");
    }

}
