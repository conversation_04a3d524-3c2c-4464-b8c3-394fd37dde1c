package com.cdkit.modules.cm.performance.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.common.api.vo.Result;
import com.cdkit.common.exception.CdkitCloudException;
import com.cdkit.common.page.PageRes;
import com.cdkit.common.system.vo.LoginUser;
import com.cdkit.modules.cm.api.project.IProjectApi;
import com.cdkit.modules.cm.api.project.dto.CostProjectDTO;
import com.cdkit.modules.cm.api.project.dto.CostProjectWorkloadDTO;
import com.cdkit.modules.cm.api.project.dto.LinkContractRequestDTO;
import com.cdkit.modules.cm.application.project.ProjectApplication;
import com.cdkit.modules.cm.domain.project.mode.entity.CostProjectEntity;
import com.cdkit.modules.cm.domain.project.mode.entity.CostProjectWorkloadEntity;
import com.cdkitframework.poi.excel.ExcelImportUtil;
import com.cdkitframework.poi.excel.def.NormalExcelConstants;
import com.cdkitframework.poi.excel.entity.ExportParams;
import com.cdkitframework.poi.excel.entity.ImportParams;
import com.cdkitframework.poi.excel.view.CdkitEntityExcelView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;


import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @Description: 市场项目台账
 * @Author: sunhzh
 * @Date:   2025-07-14
 * @Version: V1.0
 */
@Tag(name="市场项目台账")
@RestController
@RequestMapping("/cm/costProject")
@Slf4j
@RequiredArgsConstructor
public class CostProjectController implements IProjectApi {

    private final ProjectApplication projectApplication;

    @Operation(summary="市场项目台账-分页列表查询")
    @Override
    public Result<IPage<CostProjectDTO>> queryPageList(CostProjectDTO queryVO, Integer pageNo, Integer pageSize) {

        CostProjectEntity buildDomain = CostProjectConverter.toEntity(queryVO);
        PageRes<CostProjectEntity> queryPageList = projectApplication.queryPageList(buildDomain, pageNo, pageSize);

        // 使用 MyBatis Plus 的分页对象
        IPage<CostProjectDTO> page = new Page<CostProjectDTO>(pageNo, pageSize);
        if (queryPageList != null) {
            page.setCurrent(queryPageList.getCurrent());
            page.setSize(queryPageList.getSize());
            page.setTotal(queryPageList.getTotal());
            page.setRecords(CostProjectConverter.toDTOList(queryPageList.getRecords()));
        }

        return Result.OK(page);

    }

    @Operation(summary="市场项目台账-添加")
    @Override
    public Result<String> add(@RequestBody CostProjectDTO costProject) {
        try {
            CostProjectEntity entity = CostProjectConverter.toEntity(costProject);
            boolean result = projectApplication.add(entity);
            if (result) {
                return Result.OK("添加成功！");
            } else {
                return Result.error("添加失败！");
            }
        } catch (Exception e) {
            log.error("添加项目失败", e);
            return Result.error("添加项目失败：" + e.getMessage());
        }
    }

    @Operation(summary="市场项目台账-编辑")
    @Override
    public Result<String> edit(@RequestBody CostProjectDTO costProject) {
        try {
            CostProjectEntity entity = CostProjectConverter.toEntity(costProject);
            boolean result = projectApplication.edit(entity);
            if (result) {
                return Result.OK("编辑成功！");
            } else {
                return Result.error("编辑失败！");
            }
        } catch (Exception e) {
            log.error("编辑项目失败", e);
            return Result.error(e.getMessage());
        }
    }

    @Operation(summary="市场项目台账-通过id删除")
    @Override
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        try {
            boolean result = projectApplication.delete(id);
            if (result) {
                return Result.OK("删除成功！");
            } else {
                return Result.error("删除失败！");
            }
        } catch (Exception e) {
            log.error("删除项目失败", e);
            return Result.error(e.getMessage());
        }
    }

    @Operation(summary="市场项目台账-批量删除")
    @Override
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        try {
            List<String> idList = Arrays.asList(ids.split(","));
            boolean result = projectApplication.deleteBatch(idList);
            if (result) {
                return Result.OK("批量删除成功！");
            } else {
                return Result.error("批量删除失败！");
            }
        } catch (Exception e) {
            log.error("批量删除项目失败", e);
            return Result.error(e.getMessage());
        }
    }

    @Operation(summary="市场项目台账-通过id查询")
    @Override
    public Result<CostProjectDTO> queryById(@RequestParam(name = "id", required = true) String id) {
        try {
            CostProjectEntity entity = projectApplication.queryById(id);
            CostProjectDTO dto = CostProjectConverter.toDTO(entity);
            return Result.OK(dto);
        } catch (Exception e) {
            log.error("查询项目详情失败", e);
            return Result.error("查询项目详情失败：" + e.getMessage());
        }
    }

    @Operation(summary="市场项目台账-通过id查询子表数据")
    @Override
    public Result<List<CostProjectWorkloadDTO>> queryWorkloadByMainId(@RequestParam(name = "id", required = true) String id) {
        try {
            List<CostProjectWorkloadEntity> entityList = projectApplication.queryWorkloadByProjectId(id);
            List<CostProjectWorkloadDTO> dtoList = CostProjectConverter.toWorkloadDTOList(entityList);
            return Result.OK(dtoList);
        } catch (Exception e) {
            log.error("查询工作量数据失败", e);
            return Result.error("查询工作量数据失败：" + e.getMessage());
        }
    }

    /**
     * 导出excel
     *
     * @param costProjectDTO 查询条件
     */
    @Operation(summary="市场项目台账-导出excel")
    public ModelAndView exportXls(CostProjectDTO costProjectDTO) {
        try {
            // Step.1 获取当前用户信息
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            String userName = "系统用户";
            if (sysUser != null) {
                userName = sysUser.getRealname().toString();
            }

            // Step.2 转换查询条件为领域实体
            CostProjectEntity queryEntity = null;
            if (costProjectDTO != null) {
                queryEntity = CostProjectConverter.toEntity(costProjectDTO);
            } else {
                queryEntity = new CostProjectEntity();
            }

            // Step.3 通过应用层获取导出数据（只获取主表数据）
            List<CostProjectEntity> entityList = projectApplication.getMainTableExportList(queryEntity);

            // Step.4 转换为DTO列表（只包含主表信息）
            List<CostProjectDTO> exportList = CostProjectConverter.toDTOList(entityList);

            // 确保导出列表不为null
            if (exportList == null) {
                exportList = Arrays.asList();
            }

            // Step.5 使用Cdkit包的导出逻辑 - 使用CostProjectDTO只导出主表
            ModelAndView mv = new ModelAndView(new CdkitEntityExcelView());
            mv.addObject(NormalExcelConstants.FILE_NAME, "市场项目台账列表");
            mv.addObject(NormalExcelConstants.CLASS, CostProjectDTO.class);
            mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("市场项目台账数据", "导出人:" + userName, "市场项目台账"));
            mv.addObject(NormalExcelConstants.DATA_LIST, exportList);

            log.info("导出Excel成功，共{}条数据", exportList.size());
            return mv;
        } catch (Exception e) {
            log.error("导出Excel失败", e);
            // 即使出现异常，也尝试导出空Excel
            try {
                ModelAndView mv = new ModelAndView(new CdkitEntityExcelView());
                mv.addObject(NormalExcelConstants.FILE_NAME, "市场项目台账列表");
                mv.addObject(NormalExcelConstants.CLASS, CostProjectDTO.class);
                mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("市场项目台账数据", "导出人:系统用户", "市场项目台账"));
                mv.addObject(NormalExcelConstants.DATA_LIST, Arrays.asList());
                log.warn("导出异常，返回空Excel文件");
                return mv;
            } catch (Exception ex) {
                log.error("导出空Excel也失败", ex);
                throw new CdkitCloudException("导出Excel失败：" + e.getMessage());
            }
        }
    }
    @Operation(summary="市场项目台账-导入excel")
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            // 获取上传文件对象
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<CostProjectDTO> list = ExcelImportUtil.importExcel(file.getInputStream(), CostProjectDTO.class, params);
                for (CostProjectDTO dto : list) {
                    // 将DTO转换为Entity
                    CostProjectEntity projectEntity = CostProjectConverter.toEntity(dto);
                    projectApplication.add(projectEntity);
                }
                return Result.OK("文件导入成功！数据行数:" + list.size());
            } catch (Exception e) {
                log.error(e.getMessage(),e);
                return Result.error("文件导入失败:"+e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.OK("文件导入失败！");
    }

    @Operation(summary="市场项目台账-关联合同")
    @Override
    public Result<String> linkContract(@RequestBody LinkContractRequestDTO request) {
        try {
            // 参数校验
            if (request == null || request.getProjectId() == null || request.getProjectId().trim().isEmpty()) {
                return Result.error("项目ID不能为空");
            }

            boolean result = projectApplication.linkContract(
                    request.getProjectId(),
                    request.getContractCode(),
                    request.getContractName(),
                    request.getContractMode(),
                    request.getContractIssueDate(),
                    request.getContractEndDate()
            );

            if (result) {
                return Result.OK("关联合同成功！项目阶段已更新为已签合同");
            } else {
                return Result.error("关联合同失败！");
            }
        } catch (Exception e) {
            log.error("关联合同失败", e);
            return Result.error(e.getMessage());
        }
    }

    @Operation(summary="市场项目台账-执行项目")
    @Override
    public Result<String> executeProject(@RequestParam(name = "projectId", required = true) String projectId) {
        try {
            // 参数校验
            if (projectId == null || projectId.trim().isEmpty()) {
                return Result.error("项目ID不能为空");
            }

            boolean result = projectApplication.executeProject(projectId);

            if (result) {
                return Result.OK("项目执行成功！项目阶段已更新为执行中");
            } else {
                return Result.error("项目执行失败！");
            }
        } catch (Exception e) {
            log.error("项目执行失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 导出excel模版
     *
     * @param costProjectDTO 查询条件
     */
    @Operation(summary="市场项目台账-导出excel模版")
    public ModelAndView exportXlsTemplate(CostProjectDTO costProjectDTO) {
        try {
            // Step.1 获取当前用户信息
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            String userName = "系统用户";
            if (sysUser != null) {
                userName = sysUser.getRealname().toString();
            }

            // Step.4 转换为DTO列表（只包含主表信息）
            List<CostProjectDTO> exportList = new ArrayList<>();

            // Step.5 使用Cdkit包的导出逻辑 - 使用CostProjectDTO只导出主表
            ModelAndView mv = new ModelAndView(new CdkitEntityExcelView());
            mv.addObject(NormalExcelConstants.FILE_NAME, "市场项目台账列表");
            mv.addObject(NormalExcelConstants.CLASS, CostProjectDTO.class);
            mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("市场项目台账数据", "导出人:" + userName, "市场项目台账"));
            mv.addObject(NormalExcelConstants.DATA_LIST, exportList);

            log.info("导出Excel成功，共{}条数据", exportList.size());
            return mv;
        } catch (Exception e) {
            log.error("导出Excel失败", e);
            // 即使出现异常，也尝试导出空Excel
            try {
                ModelAndView mv = new ModelAndView(new CdkitEntityExcelView());
                mv.addObject(NormalExcelConstants.FILE_NAME, "市场项目台账列表");
                mv.addObject(NormalExcelConstants.CLASS, CostProjectDTO.class);
                mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("市场项目台账数据", "导出人:系统用户", "市场项目台账"));
                mv.addObject(NormalExcelConstants.DATA_LIST, Arrays.asList());
                log.warn("导出异常，返回空Excel文件");
                return mv;
            } catch (Exception ex) {
                log.error("导出空Excel也失败", ex);
                throw new CdkitCloudException("导出Excel失败：" + e.getMessage());
            }
        }
    }
}
