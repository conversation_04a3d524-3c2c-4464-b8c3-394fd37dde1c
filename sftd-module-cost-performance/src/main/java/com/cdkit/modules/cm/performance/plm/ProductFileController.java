package com.cdkit.modules.cm.performance.plm;

import com.cdkit.common.api.vo.Result;
import com.cdkit.modules.cm.application.plm.ProductFileApplication;
import com.cdkit.modules.cm.domain.gateway.plm.entity.ProductFileTreeEntity;
import com.cdkit.modules.cm.domain.gateway.plm.entity.ProductRecipeInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品档案控制器
 * <AUTHOR>
 * @date 2025/07/18
 */
@Tag(name = "产品档案管理")
@RestController
@RequestMapping("/cm/productFile")
@RequiredArgsConstructor
@Slf4j
public class ProductFileController {
    
    private final ProductFileApplication productFileApplication;
    
    /**
     * 获取产品档案树结构
     * 
     * @param productCode 产品编码
     * @return 产品档案树结构
     */
    @GetMapping("/tree")
    @Operation(summary = "获取产品档案树结构", description = "根据产品编码获取完整的产品档案树结构")
    public Result<List<ProductFileTreeEntity>> getProductFileTree(
            @RequestParam(name = "productCode", required = true) String productCode) {
        try {
            log.info("获取产品档案树结构 - productCode: {}", productCode);
            List<ProductFileTreeEntity> treeList = productFileApplication.getProductFileTree(productCode);
            return Result.OK(treeList);
        } catch (Exception e) {
            log.error("获取产品档案树结构失败 - productCode: {}", productCode, e);
            return Result.error("获取产品档案树结构失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取产品档案树最后节点的物料编码列表
     * 
     * @param productCode 产品编码
     * @return 最后节点的物料编码列表
     */
    @GetMapping("/lastNodeMaterialCodes")
    @Operation(summary = "获取最后节点物料编码", description = "获取产品档案树中所有叶子节点的物料编码列表")
    public Result<List<String>> getLastNodeMaterialCodes(
            @RequestParam(name = "productCode", required = true) String productCode) {
        try {
            log.info("获取最后节点物料编码 - productCode: {}", productCode);
            List<String> materialCodes = productFileApplication.getLastNodeMaterialCodes(productCode);
            return Result.OK(materialCodes);
        } catch (Exception e) {
            log.error("获取最后节点物料编码失败 - productCode: {}", productCode, e);
            return Result.error("获取最后节点物料编码失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量获取多个产品的最后节点物料编码
     *
     * @param productCodes 产品编码列表
     * @return 所有产品的最后节点物料编码列表
     */
    @PostMapping("/batchLastNodeMaterialCodes")
    @Operation(summary = "批量获取最后节点物料编码", description = "批量获取多个产品档案树中所有叶子节点的物料编码列表")
    public Result<List<String>> getBatchLastNodeMaterialCodes(
            @RequestBody List<String> productCodes) {
        try {
            log.info("批量获取最后节点物料编码 - productCodes: {}", productCodes);
            List<String> materialCodes = productFileApplication.getBatchLastNodeMaterialCodes(productCodes);
            return Result.OK(materialCodes);
        } catch (Exception e) {
            log.error("批量获取最后节点物料编码失败 - productCodes: {}", productCodes, e);
            return Result.error("批量获取最后节点物料编码失败: " + e.getMessage());
        }
    }

    /**
     * 获取产品配方信息
     * 包含第一层的配方信息（配方名称、配方编号）和最底层的物料列表
     *
     * @param productCode 产品编码
     * @return 产品配方信息
     */
    @GetMapping("/recipeInfo")
    @Operation(summary = "获取产品配方信息", description = "获取产品的配方信息，包含第一层配方信息和最底层物料列表")
    public Result<ProductRecipeInfo> getProductRecipeInfo(
            @RequestParam(name = "productCode", required = true) String productCode) {
        try {
            log.info("获取产品配方信息 - productCode: {}", productCode);
            ProductRecipeInfo recipeInfo = productFileApplication.getProductRecipeInfo(productCode);
            return Result.OK(recipeInfo);
        } catch (Exception e) {
            log.error("获取产品配方信息失败 - productCode: {}", productCode, e);
            return Result.error("获取产品配方信息失败: " + e.getMessage());
        }
    }

    /**
     * 批量获取多个产品的配方信息
     *
     * @param productCodes 产品编码列表
     * @return 产品配方信息列表
     */
    @PostMapping("/batchRecipeInfo")
    @Operation(summary = "批量获取产品配方信息", description = "批量获取多个产品的配方信息")
    public Result<List<ProductRecipeInfo>> getBatchProductRecipeInfo(
            @RequestBody List<String> productCodes) {
        try {
            log.info("批量获取产品配方信息 - productCodes: {}", productCodes);
            List<ProductRecipeInfo> recipeInfoList = productFileApplication.getBatchProductRecipeInfo(productCodes);
            return Result.OK(recipeInfoList);
        } catch (Exception e) {
            log.error("批量获取产品配方信息失败 - productCodes: {}", productCodes, e);
            return Result.error("批量获取产品配方信息失败: " + e.getMessage());
        }
    }
}
