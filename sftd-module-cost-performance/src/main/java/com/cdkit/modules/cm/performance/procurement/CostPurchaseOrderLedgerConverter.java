package com.cdkit.modules.cm.performance.procurement;

import cn.hutool.core.bean.BeanUtil;
import com.cdkit.modules.cm.api.procurement.dto.CostPurchaseOrderLedgerDTO;
import com.cdkit.modules.cm.domain.procurement.entity.CostPurchaseOrderLedgerEntity;

import java.util.List;

/**
 * VO DTO 转换
 * <AUTHOR>
 * @date 2025/07/10
 */
public class CostPurchaseOrderLedgerConverter {

    public static List<CostPurchaseOrderLedgerDTO> toVOList(List<CostPurchaseOrderLedgerEntity> list) {
        return BeanUtil.copyToList(list, CostPurchaseOrderLedgerDTO.class);
    }
}
