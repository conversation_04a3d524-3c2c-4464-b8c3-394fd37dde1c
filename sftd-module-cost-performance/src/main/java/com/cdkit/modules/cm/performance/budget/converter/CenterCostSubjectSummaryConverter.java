package com.cdkit.modules.cm.performance.budget.converter;

import com.cdkit.modules.cm.api.budget.dto.CenterCostSubjectSummaryDTO;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 中心间接成本科目汇总转换器
 * 用于API层和领域层DTO之间的转换
 * 
 * <AUTHOR>
 * @date 2025-08-06
 */
public class CenterCostSubjectSummaryConverter {

    /**
     * 领域层DTO转换为API层DTO
     *
     * @param domainDTO 领域层DTO
     * @return API层DTO
     */
    public static CenterCostSubjectSummaryDTO toApiDTO(com.cdkit.modules.cm.domain.budget.model.dto.CenterCostSubjectSummaryDTO domainDTO) {
        if (domainDTO == null) {
            return null;
        }

        CenterCostSubjectSummaryDTO apiDTO = new CenterCostSubjectSummaryDTO();
        BeanUtils.copyProperties(domainDTO, apiDTO);
        return apiDTO;
    }

    /**
     * API层DTO转换为领域层DTO
     *
     * @param apiDTO API层DTO
     * @return 领域层DTO
     */
    public static com.cdkit.modules.cm.domain.budget.model.dto.CenterCostSubjectSummaryDTO toDomainDTO(CenterCostSubjectSummaryDTO apiDTO) {
        if (apiDTO == null) {
            return null;
        }

        com.cdkit.modules.cm.domain.budget.model.dto.CenterCostSubjectSummaryDTO domainDTO = 
            new com.cdkit.modules.cm.domain.budget.model.dto.CenterCostSubjectSummaryDTO();
        BeanUtils.copyProperties(apiDTO, domainDTO);
        return domainDTO;
    }

    /**
     * 领域层DTO列表转换为API层DTO列表
     *
     * @param domainDTOList 领域层DTO列表
     * @return API层DTO列表
     */
    public static List<CenterCostSubjectSummaryDTO> toApiDTOList(List<com.cdkit.modules.cm.domain.budget.model.dto.CenterCostSubjectSummaryDTO> domainDTOList) {
        if (domainDTOList == null) {
            return null;
        }

        return domainDTOList.stream()
                .map(CenterCostSubjectSummaryConverter::toApiDTO)
                .collect(Collectors.toList());
    }

    /**
     * API层DTO列表转换为领域层DTO列表
     *
     * @param apiDTOList API层DTO列表
     * @return 领域层DTO列表
     */
    public static List<com.cdkit.modules.cm.domain.budget.model.dto.CenterCostSubjectSummaryDTO> toDomainDTOList(List<CenterCostSubjectSummaryDTO> apiDTOList) {
        if (apiDTOList == null) {
            return null;
        }

        return apiDTOList.stream()
                .map(CenterCostSubjectSummaryConverter::toDomainDTO)
                .collect(Collectors.toList());
    }
}
