package com.cdkit.modules.cm.performance.budget.converter;

import com.cdkit.modules.cm.api.budget.dto.CostAnnualBudgetDetailImportDTO;
import com.cdkit.modules.cm.api.budget.request.CostAnnualBudgetSaveRequest;
import com.cdkit.modules.cm.domain.businessdata.model.entity.CostBudgetSubjectEntity;
import com.cdkit.modules.cm.domain.businessdata.repository.CostBudgetSubjectRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 年度预算明细导入数据转换器
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
@Component
public class CostAnnualBudgetDetailImportConverter {

    @Autowired
    private CostBudgetSubjectRepository costBudgetSubjectRepository;

    /**
     * 将导入DTO列表转换为CostAnnualBudgetSaveRequest
     * 
     * @param importDTOList 导入DTO列表
     * @return CostAnnualBudgetSaveRequest
     */
    public CostAnnualBudgetSaveRequest toSaveRequest(List<CostAnnualBudgetDetailImportDTO> importDTOList) {
        if (importDTOList == null || importDTOList.isEmpty()) {
            log.warn("导入数据为空");
            return null;
        }

        CostAnnualBudgetSaveRequest saveRequest = new CostAnnualBudgetSaveRequest();
        
        // 主表信息不设置，只返回明细数据
        
        // 获取所有启用的预算科目，建立名称到科目信息的映射
        Map<String, CostBudgetSubjectEntity> subjectMap = getSubjectNameMap();
        
        // 转换明细数据
        List<CostAnnualBudgetSaveRequest.CostAnnualBudgetDetailRequest> detailList = new ArrayList<>();
        
        for (CostAnnualBudgetDetailImportDTO importDTO : importDTOList) {
            CostAnnualBudgetSaveRequest.CostAnnualBudgetDetailRequest detailRequest = 
                new CostAnnualBudgetSaveRequest.CostAnnualBudgetDetailRequest();
            
            // 设置基础信息
            detailRequest.setProjectName(importDTO.getProjectName());
            detailRequest.setProfessionalCompany(importDTO.getProfessionalCompany());
            detailRequest.setCenter(importDTO.getCenter());
            detailRequest.setBudgetType(importDTO.getBudgetType());
            detailRequest.setWbsCode(importDTO.getWbsCode());
            detailRequest.setProjectType(importDTO.getProjectType());
            detailRequest.setFourthLevelBusiness(importDTO.getFourthLevelBusiness());
            detailRequest.setBusinessSubcategory(importDTO.getBusinessSubcategory());
            
            // 设置收入预算（转换为万元）
            if (importDTO.getRevenueBudget() != null) {
                detailRequest.setRevenueBudget(importDTO.getRevenueBudget().divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));
            }
            
            // 计算直接成本总额并设置直接成本明细
            BigDecimal directCostTotal = calculateDirectCostTotal(importDTO);
            detailRequest.setDirectCostBudget(directCostTotal);
            
            // 设置直接成本明细列表
            List<CostAnnualBudgetSaveRequest.CostAnnualBudgetDetailDirectCostRequest> directCostList = 
                buildDirectCostList(importDTO, subjectMap);
            detailRequest.setDirectCostList(directCostList);
            
            detailList.add(detailRequest);
        }
        
        saveRequest.setBudgetDetailList(detailList);
        
        log.info("成功转换 {} 条明细数据为CostAnnualBudgetSaveRequest", importDTOList.size());
        return saveRequest;
    }

    /**
     * 获取预算科目名称到科目信息的映射
     */
    private Map<String, CostBudgetSubjectEntity> getSubjectNameMap() {
        try {
            List<CostBudgetSubjectEntity> enabledSubjects = costBudgetSubjectRepository.findAllEnabled();
            return enabledSubjects.stream()
                    .collect(Collectors.toMap(
                            CostBudgetSubjectEntity::getSubjectName,
                            subject -> subject,
                            (existing, replacement) -> existing // 如果有重复名称，保留第一个
                    ));
        } catch (Exception e) {
            log.error("获取预算科目映射失败", e);
            return Map.of(); // 返回空映射
        }
    }

    /**
     * 计算直接成本总额
     */
    private BigDecimal calculateDirectCostTotal(CostAnnualBudgetDetailImportDTO importDTO) {
        BigDecimal total = BigDecimal.ZERO;
        
        // 累加所有直接成本科目金额（转换为万元）
        total = addIfNotNull(total, importDTO.getRawMaterials());
        total = addIfNotNull(total, importDTO.getFuelPower());
        total = addIfNotNull(total, importDTO.getLaborCost());
        total = addIfNotNull(total, importDTO.getTaxes());
        total = addIfNotNull(total, importDTO.getInsuranceFee());
        total = addIfNotNull(total, importDTO.getTravelExpense());
        total = addIfNotNull(total, importDTO.getOverseasPersonnelFee());
        total = addIfNotNull(total, importDTO.getSeaMealFee());
        total = addIfNotNull(total, importDTO.getPropertyManagementFee());
        total = addIfNotNull(total, importDTO.getFixedAssetDepreciation());
        total = addIfNotNull(total, importDTO.getIntangibleAssetAmortization());
        total = addIfNotNull(total, importDTO.getLongTermPrepaidExpenseAmortization());
        total = addIfNotNull(total, importDTO.getInvestmentPropertyDepreciation());
        total = addIfNotNull(total, importDTO.getRightOfUseAssetDepreciation());
        total = addIfNotNull(total, importDTO.getOther());
        total = addIfNotNull(total, importDTO.getOfficeSupplies());
        total = addIfNotNull(total, importDTO.getGeneralMaterials());
        total = addIfNotNull(total, importDTO.getLowValueConsumables());
        total = addIfNotNull(total, importDTO.getTransportationFee());
        total = addIfNotNull(total, importDTO.getLoadingUnloadingFee());
        total = addIfNotNull(total, importDTO.getPortMiscellaneousFee());
        total = addIfNotNull(total, importDTO.getShipFee());
        total = addIfNotNull(total, importDTO.getShipInspectionFee());
        total = addIfNotNull(total, importDTO.getAircraftFee());
        total = addIfNotNull(total, importDTO.getWharfFee());
        total = addIfNotNull(total, importDTO.getStorageFee());
        total = addIfNotNull(total, importDTO.getLeaseFee());
        total = addIfNotNull(total, importDTO.getProcessingFee());
        total = addIfNotNull(total, importDTO.getEngineeringOutsourcing());
        total = addIfNotNull(total, importDTO.getTechnicalOutsourcing());
        total = addIfNotNull(total, importDTO.getLaborOutsourcing());
        total = addIfNotNull(total, importDTO.getRepairFee());
        total = addIfNotNull(total, importDTO.getOfficeFee());
        total = addIfNotNull(total, importDTO.getCertificateFee());
        total = addIfNotNull(total, importDTO.getTranslationFee());
        total = addIfNotNull(total, importDTO.getHeatingFee());
        total = addIfNotNull(total, importDTO.getCommunicationFee());
        total = addIfNotNull(total, importDTO.getBookMaterialFee());
        total = addIfNotNull(total, importDTO.getPrintingPublishingFee());
        total = addIfNotNull(total, importDTO.getOwnerServiceFee());
        total = addIfNotNull(total, importDTO.getArchiveManagementFee());
        total = addIfNotNull(total, importDTO.getInformationSystem());
        total = addIfNotNull(total, importDTO.getHealthSafetyEnvironmentalFee());
        total = addIfNotNull(total, importDTO.getSystemCertificationFee());
        total = addIfNotNull(total, importDTO.getInspectionTestFee());
        total = addIfNotNull(total, importDTO.getAgencyFee());
        total = addIfNotNull(total, importDTO.getBusinessTrainingServiceFee());
        total = addIfNotNull(total, importDTO.getBusinessConferenceServiceFee());
        total = addIfNotNull(total, importDTO.getConsultingFee());
        total = addIfNotNull(total, importDTO.getAuditFee());
        total = addIfNotNull(total, importDTO.getLitigationFee());
        total = addIfNotNull(total, importDTO.getAdvertisingFee());
        total = addIfNotNull(total, importDTO.getPublicityFee());
        total = addIfNotNull(total, importDTO.getDesignFee());
        total = addIfNotNull(total, importDTO.getExpertFee());
        total = addIfNotNull(total, importDTO.getSharedBusinessServiceFee());
        total = addIfNotNull(total, importDTO.getConferenceFee());
        total = addIfNotNull(total, importDTO.getForeignGuestEntertainmentFee());
        total = addIfNotNull(total, importDTO.getBusinessEntertainmentFee());
        total = addIfNotNull(total, importDTO.getRetirementExpense());
        total = addIfNotNull(total, importDTO.getPartyAssociationFee());
        total = addIfNotNull(total, importDTO.getEpidemicPreventionFee());
        total = addIfNotNull(total, importDTO.getAssociationFee());
        total = addIfNotNull(total, importDTO.getInternalCollaborationCost());
        
        // 转换为万元
        return total.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP);
    }

    /**
     * 安全累加BigDecimal值
     */
    private BigDecimal addIfNotNull(BigDecimal total, BigDecimal value) {
        return value != null ? total.add(value) : total;
    }

    /**
     * 构建直接成本明细列表
     * 严格以预算科目主表为准，只返回预算科目表中启用状态的科目
     * Excel中存在但预算科目表中不存在的列不会返回
     */
    private List<CostAnnualBudgetSaveRequest.CostAnnualBudgetDetailDirectCostRequest> buildDirectCostList(
            CostAnnualBudgetDetailImportDTO importDTO, Map<String, CostBudgetSubjectEntity> subjectMap) {

        List<CostAnnualBudgetSaveRequest.CostAnnualBudgetDetailDirectCostRequest> directCostList = new ArrayList<>();

        // 创建Excel字段名到金额的映射，便于查找
        Map<String, BigDecimal> excelAmountMap = createExcelAmountMap(importDTO);

        // 严格遍历预算科目表中的启用科目，Excel中多余的列会被忽略
        for (CostBudgetSubjectEntity subject : subjectMap.values()) {
            CostAnnualBudgetSaveRequest.CostAnnualBudgetDetailDirectCostRequest directCostRequest =
                new CostAnnualBudgetSaveRequest.CostAnnualBudgetDetailDirectCostRequest();

            // 设置预算科目信息
            directCostRequest.setSubjectCode(subject.getSubjectCode());
            directCostRequest.setSubjectName(subject.getSubjectName());
            directCostRequest.setSubjectDescription(subject.getSubjectDescription());

            // 从Excel中获取对应的金额，如果Excel中没有对应列则设置为0
            BigDecimal amount = excelAmountMap.getOrDefault(subject.getSubjectName(), BigDecimal.ZERO);

            // 处理null值，确保amount不为null
            if (amount == null) {
                amount = BigDecimal.ZERO;
            }

            // 转换为万元
            directCostRequest.setBudgetAmount(amount.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));

            directCostList.add(directCostRequest);
        }

        // 按照sortOrder升序排序，保证顺序一致性
        directCostList.sort((a, b) -> {
            // 从subjectMap中获取对应的科目实体来比较sortOrder
            CostBudgetSubjectEntity subjectA = subjectMap.get(a.getSubjectName());
            CostBudgetSubjectEntity subjectB = subjectMap.get(b.getSubjectName());

            if (subjectA == null && subjectB == null) return 0;
            if (subjectA == null) return 1;
            if (subjectB == null) return -1;

            Integer sortOrderA = subjectA.getSortOrder();
            Integer sortOrderB = subjectB.getSortOrder();

            if (sortOrderA == null && sortOrderB == null) return 0;
            if (sortOrderA == null) return 1;
            if (sortOrderB == null) return -1;

            return sortOrderA.compareTo(sortOrderB);
        });

        log.info("构建直接成本明细列表完成，严格按预算科目表返回 {} 个科目", directCostList.size());
        return directCostList;
    }

    /**
     * 创建Excel字段名到金额的映射
     * 对于null值，统一设置为BigDecimal.ZERO
     */
    private Map<String, BigDecimal> createExcelAmountMap(CostAnnualBudgetDetailImportDTO importDTO) {
        Map<String, BigDecimal> amountMap = new java.util.HashMap<>();

        // 将Excel中的所有金额字段映射到对应的科目名称，null值转换为0
        amountMap.put("原材料及主要材料", safeGetAmount(importDTO.getRawMaterials()));
        amountMap.put("燃料动力", safeGetAmount(importDTO.getFuelPower()));
        amountMap.put("人工成本", safeGetAmount(importDTO.getLaborCost()));
        amountMap.put("税费", safeGetAmount(importDTO.getTaxes()));
        amountMap.put("保险费", safeGetAmount(importDTO.getInsuranceFee()));
        amountMap.put("差旅费", safeGetAmount(importDTO.getTravelExpense()));
        amountMap.put("出国人员费", safeGetAmount(importDTO.getOverseasPersonnelFee()));
        amountMap.put("海餐费", safeGetAmount(importDTO.getSeaMealFee()));
        amountMap.put("物业管理费", safeGetAmount(importDTO.getPropertyManagementFee()));
        amountMap.put("固定资产折旧", safeGetAmount(importDTO.getFixedAssetDepreciation()));
        amountMap.put("无形资产摊销", safeGetAmount(importDTO.getIntangibleAssetAmortization()));
        amountMap.put("长期待摊费用摊销", safeGetAmount(importDTO.getLongTermPrepaidExpenseAmortization()));
        amountMap.put("投资性房地产折旧", safeGetAmount(importDTO.getInvestmentPropertyDepreciation()));
        amountMap.put("使用权资产折旧", safeGetAmount(importDTO.getRightOfUseAssetDepreciation()));
        amountMap.put("其他", safeGetAmount(importDTO.getOther()));
        amountMap.put("物料消耗-办公耗材", safeGetAmount(importDTO.getOfficeSupplies()));
        amountMap.put("物料消耗-一般材料", safeGetAmount(importDTO.getGeneralMaterials()));
        amountMap.put("低值易耗品", safeGetAmount(importDTO.getLowValueConsumables()));
        amountMap.put("运输费", safeGetAmount(importDTO.getTransportationFee()));
        amountMap.put("装卸费", safeGetAmount(importDTO.getLoadingUnloadingFee()));
        amountMap.put("港杂费", safeGetAmount(importDTO.getPortMiscellaneousFee()));
        amountMap.put("船舶费", safeGetAmount(importDTO.getShipFee()));
        amountMap.put("船检费", safeGetAmount(importDTO.getShipInspectionFee()));
        amountMap.put("飞机费", safeGetAmount(importDTO.getAircraftFee()));
        amountMap.put("码头费", safeGetAmount(importDTO.getWharfFee()));
        amountMap.put("仓储费", safeGetAmount(importDTO.getStorageFee()));
        amountMap.put("租赁费", safeGetAmount(importDTO.getLeaseFee()));
        amountMap.put("加工费", safeGetAmount(importDTO.getProcessingFee()));
        amountMap.put("外包费-工程外包", safeGetAmount(importDTO.getEngineeringOutsourcing()));
        amountMap.put("外包费-技术外包", safeGetAmount(importDTO.getTechnicalOutsourcing()));
        amountMap.put("外包费-劳务外包", safeGetAmount(importDTO.getLaborOutsourcing()));
        amountMap.put("修理费", safeGetAmount(importDTO.getRepairFee()));
        amountMap.put("办公费", safeGetAmount(importDTO.getOfficeFee()));
        amountMap.put("办证费", safeGetAmount(importDTO.getCertificateFee()));
        amountMap.put("翻译费", safeGetAmount(importDTO.getTranslationFee()));
        amountMap.put("取暖费", safeGetAmount(importDTO.getHeatingFee()));
        amountMap.put("通讯费", safeGetAmount(importDTO.getCommunicationFee()));
        amountMap.put("图书资料费", safeGetAmount(importDTO.getBookMaterialFee()));
        amountMap.put("印刷出版费", safeGetAmount(importDTO.getPrintingPublishingFee()));
        amountMap.put("业主服务费", safeGetAmount(importDTO.getOwnerServiceFee()));
        amountMap.put("档案管理费", safeGetAmount(importDTO.getArchiveManagementFee()));
        amountMap.put("信息系统", safeGetAmount(importDTO.getInformationSystem()));
        amountMap.put("健康安全环保费", safeGetAmount(importDTO.getHealthSafetyEnvironmentalFee()));
        amountMap.put("体系认证费", safeGetAmount(importDTO.getSystemCertificationFee()));
        amountMap.put("检(化)验费", safeGetAmount(importDTO.getInspectionTestFee()));
        amountMap.put("代理费", safeGetAmount(importDTO.getAgencyFee()));
        amountMap.put("经营性培训服务费", safeGetAmount(importDTO.getBusinessTrainingServiceFee()));
        amountMap.put("经营性会务服务费", safeGetAmount(importDTO.getBusinessConferenceServiceFee()));
        amountMap.put("咨询费", safeGetAmount(importDTO.getConsultingFee()));
        amountMap.put("审计费", safeGetAmount(importDTO.getAuditFee()));
        amountMap.put("诉讼费", safeGetAmount(importDTO.getLitigationFee()));
        amountMap.put("广告费", safeGetAmount(importDTO.getAdvertisingFee()));
        amountMap.put("宣传费", safeGetAmount(importDTO.getPublicityFee()));
        amountMap.put("设计费", safeGetAmount(importDTO.getDesignFee()));
        amountMap.put("专家费", safeGetAmount(importDTO.getExpertFee()));
        amountMap.put("共享业务服务费", safeGetAmount(importDTO.getSharedBusinessServiceFee()));
        amountMap.put("会议费", safeGetAmount(importDTO.getConferenceFee()));
        amountMap.put("外宾招待费", safeGetAmount(importDTO.getForeignGuestEntertainmentFee()));
        amountMap.put("业务招待费", safeGetAmount(importDTO.getBusinessEntertainmentFee()));
        amountMap.put("离退休费用", safeGetAmount(importDTO.getRetirementExpense()));
        amountMap.put("党团协会费用", safeGetAmount(importDTO.getPartyAssociationFee()));
        amountMap.put("防疫费", safeGetAmount(importDTO.getEpidemicPreventionFee()));
        amountMap.put("协会(学会)费", safeGetAmount(importDTO.getAssociationFee()));
        amountMap.put("内部协同成本", safeGetAmount(importDTO.getInternalCollaborationCost()));

        return amountMap;
    }

    /**
     * 安全获取金额，null值转换为BigDecimal.ZERO
     */
    private BigDecimal safeGetAmount(BigDecimal amount) {
        return amount != null ? amount : BigDecimal.ZERO;
    }

}
