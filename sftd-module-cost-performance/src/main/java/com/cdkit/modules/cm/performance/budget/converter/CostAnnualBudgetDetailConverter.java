package com.cdkit.modules.cm.performance.budget.converter;

import com.cdkit.modules.cm.api.budget.dto.CostAnnualBudgetDetailDTO;
import com.cdkit.modules.cm.domain.budget.mode.entity.CostAnnualBudgetEntity;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 年度总预算详情转换器
 * <AUTHOR>
 * @date 2025-08-04
 */
public class CostAnnualBudgetDetailConverter {

    /**
     * 领域实体转换为详情DTO
     *
     * @param entity 领域实体
     * @param budgetDetailList 明细数据列表
     * @return 详情DTO对象
     */
    public static CostAnnualBudgetDetailDTO toDetailDTO(CostAnnualBudgetEntity entity, 
                                                       List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList) {
        if (entity == null) {
            return null;
        }
        
        CostAnnualBudgetDetailDTO dto = new CostAnnualBudgetDetailDTO();
        
        // 复制主表数据
        BeanUtils.copyProperties(entity, dto);
        
        // 转换明细数据
        if (budgetDetailList != null && !budgetDetailList.isEmpty()) {
            List<CostAnnualBudgetDetailDTO.CostAnnualBudgetDetailItemDTO> detailDTOList = 
                budgetDetailList.stream()
                    .map(CostAnnualBudgetDetailConverter::toBudgetDetailItemDTO)
                    .collect(Collectors.toList());
            dto.setBudgetDetailList(detailDTOList);
        }
        
        return dto;
    }

    /**
     * 明细实体转换为明细DTO
     *
     * @param detailInfo 明细实体
     * @return 明细DTO
     */
    public static CostAnnualBudgetDetailDTO.CostAnnualBudgetDetailItemDTO toBudgetDetailItemDTO(
            CostAnnualBudgetEntity.BudgetDetailInfo detailInfo) {
        if (detailInfo == null) {
            return null;
        }
        
        CostAnnualBudgetDetailDTO.CostAnnualBudgetDetailItemDTO detailDTO = 
            new CostAnnualBudgetDetailDTO.CostAnnualBudgetDetailItemDTO();
        
        // 复制明细数据
        BeanUtils.copyProperties(detailInfo, detailDTO);
        
        // 转换直接成本明细列表
        if (detailInfo.getDirectCostList() != null && !detailInfo.getDirectCostList().isEmpty()) {
            List<CostAnnualBudgetDetailDTO.CostAnnualBudgetDetailDirectCostDTO> directCostDTOList = 
                detailInfo.getDirectCostList().stream()
                    .map(CostAnnualBudgetDetailConverter::toDirectCostDTO)
                    .collect(Collectors.toList());
            detailDTO.setDirectCostList(directCostDTOList);
        }
        
        return detailDTO;
    }

    /**
     * 直接成本实体转换为直接成本DTO
     *
     * @param directCostInfo 直接成本实体
     * @return 直接成本DTO
     */
    public static CostAnnualBudgetDetailDTO.CostAnnualBudgetDetailDirectCostDTO toDirectCostDTO(
            CostAnnualBudgetEntity.DirectCostInfo directCostInfo) {
        if (directCostInfo == null) {
            return null;
        }
        
        CostAnnualBudgetDetailDTO.CostAnnualBudgetDetailDirectCostDTO directCostDTO = 
            new CostAnnualBudgetDetailDTO.CostAnnualBudgetDetailDirectCostDTO();
        
        // 复制直接成本数据
        BeanUtils.copyProperties(directCostInfo, directCostDTO);
        
        return directCostDTO;
    }

    /**
     * 明细DTO列表转换为明细实体列表
     *
     * @param detailDTOList 明细DTO列表
     * @return 明细实体列表
     */
    public static List<CostAnnualBudgetEntity.BudgetDetailInfo> toBudgetDetailInfoList(
            List<CostAnnualBudgetDetailDTO.CostAnnualBudgetDetailItemDTO> detailDTOList) {
        if (detailDTOList == null || detailDTOList.isEmpty()) {
            return new ArrayList<>();
        }
        
        return detailDTOList.stream()
                .map(CostAnnualBudgetDetailConverter::toBudgetDetailInfo)
                .collect(Collectors.toList());
    }

    /**
     * 明细DTO转换为明细实体
     *
     * @param detailDTO 明细DTO
     * @return 明细实体
     */
    public static CostAnnualBudgetEntity.BudgetDetailInfo toBudgetDetailInfo(
            CostAnnualBudgetDetailDTO.CostAnnualBudgetDetailItemDTO detailDTO) {
        if (detailDTO == null) {
            return null;
        }
        
        CostAnnualBudgetEntity.BudgetDetailInfo detailInfo = new CostAnnualBudgetEntity.BudgetDetailInfo();
        
        // 复制明细数据
        BeanUtils.copyProperties(detailDTO, detailInfo);
        
        // 转换直接成本明细列表
        if (detailDTO.getDirectCostList() != null && !detailDTO.getDirectCostList().isEmpty()) {
            List<CostAnnualBudgetEntity.DirectCostInfo> directCostInfoList = 
                detailDTO.getDirectCostList().stream()
                    .map(CostAnnualBudgetDetailConverter::toDirectCostInfo)
                    .collect(Collectors.toList());
            detailInfo.setDirectCostList(directCostInfoList);
        }
        
        return detailInfo;
    }

    /**
     * 直接成本DTO转换为直接成本实体
     *
     * @param directCostDTO 直接成本DTO
     * @return 直接成本实体
     */
    public static CostAnnualBudgetEntity.DirectCostInfo toDirectCostInfo(
            CostAnnualBudgetDetailDTO.CostAnnualBudgetDetailDirectCostDTO directCostDTO) {
        if (directCostDTO == null) {
            return null;
        }
        
        CostAnnualBudgetEntity.DirectCostInfo directCostInfo = new CostAnnualBudgetEntity.DirectCostInfo();
        
        // 复制直接成本数据
        BeanUtils.copyProperties(directCostDTO, directCostInfo);
        
        return directCostInfo;
    }
}
