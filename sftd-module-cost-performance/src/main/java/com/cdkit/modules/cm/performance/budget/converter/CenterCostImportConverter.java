package com.cdkit.modules.cm.performance.budget.converter;

import com.cdkit.modules.cm.domain.budget.model.dto.CenterCostImportDTO;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 中心间接成本导入数据转换器
 * 用于Performance层和Domain层之间的数据转换
 * 
 * <AUTHOR>
 * @date 2025-08-06
 */
public class CenterCostImportConverter {

    /**
     * 将Excel行数据转换为Domain层的DTO
     * 
     * @param row Excel行数据
     * @param budgetCode 预算编号
     * @param sheetName Sheet页名称（作为中心名称）
     * @param templateType 模板类型
     * @param rowIndex 行索引（用于错误提示）
     * @return 转换后的DTO，如果数据无效则返回null
     */
    public static CenterCostImportDTO convertRowToDTO(Map<String, Object> row, 
                                                     String budgetCode, 
                                                     String sheetName, 
                                                     String templateType, 
                                                     int rowIndex) {
        if (row == null || row.isEmpty()) {
            return null;
        }

        try {
            // 获取预算科目名称（必填）- 使用映射后的字段名
            String subjectName = getStringValue(row, "subjectName");
            if (!StringUtils.hasText(subjectName)) {
                return null; // 预算科目名称为空，跳过此行
            }

            // 获取成本金额（必填）- 使用映射后的字段名
            BigDecimal costAmount = getBigDecimalValue(row, "costAmount");
            if (costAmount == null) {
                System.out.println("跳过第" + rowIndex + "行：成本金额为null，sheet页: " + sheetName + ", 科目: " + subjectName);
                return null; // 成本金额为null，跳过此行
            }

            // 允许金额为0的数据，只排除负数
            if (costAmount.compareTo(BigDecimal.ZERO) < 0) {
                System.out.println("跳过第" + rowIndex + "行：成本金额为负数 " + costAmount + "，sheet页: " + sheetName + ", 科目: " + subjectName);
                return null; // 成本金额为负数，跳过此行
            }

            // 构建DTO（科目释义将在validateAndSetSubjectCodes方法中设置）
            return CenterCostImportDTO.builder()
                    .budgetCode(budgetCode)
                    .center(sheetName)
                    .templateType(templateType)
                    .subjectName(subjectName)
                    .subjectDescription(null) // 初始为null，后续在校验时设置
                    .costAmount(costAmount)
                    .remark(getStringValue(row, "remark"))
                    .build();

        } catch (Exception e) {
            throw new RuntimeException("解析第" + rowIndex + "行数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量转换Excel行数据为DTO列表
     * 
     * @param rows Excel行数据列表
     * @param budgetCode 预算编号
     * @param sheetName Sheet页名称
     * @param templateType 模板类型
     * @return DTO列表
     */
    public static List<CenterCostImportDTO> convertRowsToDTO(List<Map<String, Object>> rows,
                                                           String budgetCode,
                                                           String sheetName,
                                                           String templateType) {
        List<CenterCostImportDTO> resultList = new ArrayList<>();
        
        if (rows == null || rows.isEmpty()) {
            return resultList;
        }

        System.out.println("开始转换sheet页 " + sheetName + " 的数据，共 " + rows.size() + " 行");

        for (int i = 0; i < rows.size(); i++) {
            Map<String, Object> row = rows.get(i);
            try {
                CenterCostImportDTO dto = convertRowToDTO(row, budgetCode, sheetName, templateType, i + 2);
                if (dto != null) {
                    resultList.add(dto);
                    System.out.println("成功转换第" + (i + 2) + "行：科目=" + dto.getSubjectName() + ", 金额=" + dto.getCostAmount());
                }
            } catch (Exception e) {
                // 记录错误但继续处理下一行
                System.err.println("解析sheet页 " + sheetName + " 第 " + (i + 2) + " 行数据失败: " + e.getMessage());
            }
        }

        System.out.println("sheet页 " + sheetName + " 转换完成，有效数据 " + resultList.size() + " 条");

        return resultList;
    }

    /**
     * 从Map中获取字符串值
     */
    private static String getStringValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        if (value == null) {
            return null;
        }
        return value.toString().trim();
    }

    /**
     * 从Map中获取BigDecimal值
     */
    private static BigDecimal getBigDecimalValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        if (value == null) {
            return null;
        }
        
        try {
            if (value instanceof BigDecimal) {
                return (BigDecimal) value;
            } else if (value instanceof Number) {
                return new BigDecimal(value.toString());
            } else {
                String strValue = value.toString().trim();
                if (!StringUtils.hasText(strValue)) {
                    return null;
                }
                return new BigDecimal(strValue);
            }
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
