package com.cdkit.modules.cm.performance.budget.converter;

import com.cdkit.modules.cm.api.budget.dto.CostAnnualBudgetDetailFullDTO;
import com.cdkit.modules.cm.domain.budget.repository.CostAnnualBudgetDetailFullRepository;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 项目年度预算详情完整信息转换器
 * <AUTHOR>
 * @date 2025-08-07
 */
public class CostAnnualBudgetDetailFullConverter {

    /**
     * 完整详情信息转换为DTO
     *
     * @param fullInfo 完整详情信息
     * @return DTO对象
     */
    public static CostAnnualBudgetDetailFullDTO toFullDTO(CostAnnualBudgetDetailFullRepository.CostAnnualBudgetDetailFullInfo fullInfo) {
        if (fullInfo == null) {
            return null;
        }

        CostAnnualBudgetDetailFullDTO dto = new CostAnnualBudgetDetailFullDTO();

        // 复制主表数据
        BeanUtils.copyProperties(fullInfo, dto);
        
        // 转换直接成本列表
        if (fullInfo.getDirectCostList() != null && !fullInfo.getDirectCostList().isEmpty()) {
            List<CostAnnualBudgetDetailFullDTO.DirectCostItemDTO> directCostDTOList = 
                fullInfo.getDirectCostList().stream()
                    .map(CostAnnualBudgetDetailFullConverter::toDirectCostItemDTO)
                    .collect(Collectors.toList());
            dto.setDirectCostList(directCostDTOList);
        }
        
        // 转换本中心间接成本列表
        if (fullInfo.getCenterIndirectCostList() != null && !fullInfo.getCenterIndirectCostList().isEmpty()) {
            List<CostAnnualBudgetDetailFullDTO.CenterIndirectCostItemDTO> centerIndirectCostDTOList = 
                fullInfo.getCenterIndirectCostList().stream()
                    .map(CostAnnualBudgetDetailFullConverter::toCenterIndirectCostItemDTO)
                    .collect(Collectors.toList());
            dto.setCenterIndirectCostList(centerIndirectCostDTOList);
        }
        
        // 转换综合间接成本列表
        if (fullInfo.getComprehensiveIndirectCostList() != null && !fullInfo.getComprehensiveIndirectCostList().isEmpty()) {
            List<CostAnnualBudgetDetailFullDTO.ComprehensiveIndirectCostItemDTO> comprehensiveIndirectCostDTOList = 
                fullInfo.getComprehensiveIndirectCostList().stream()
                    .map(CostAnnualBudgetDetailFullConverter::toComprehensiveIndirectCostItemDTO)
                    .collect(Collectors.toList());
            dto.setComprehensiveIndirectCostList(comprehensiveIndirectCostDTOList);
        }
        
        // 转换非经营间接成本列表
        if (fullInfo.getNonOperatingIndirectCostList() != null && !fullInfo.getNonOperatingIndirectCostList().isEmpty()) {
            List<CostAnnualBudgetDetailFullDTO.NonOperatingIndirectCostItemDTO> nonOperatingIndirectCostDTOList = 
                fullInfo.getNonOperatingIndirectCostList().stream()
                    .map(CostAnnualBudgetDetailFullConverter::toNonOperatingIndirectCostItemDTO)
                    .collect(Collectors.toList());
            dto.setNonOperatingIndirectCostList(nonOperatingIndirectCostDTOList);
        }
        
        return dto;
    }

    /**
     * 直接成本Domain对象转换为DTO
     *
     * @param item 直接成本Domain对象
     * @return 直接成本DTO
     */
    private static CostAnnualBudgetDetailFullDTO.DirectCostItemDTO toDirectCostItemDTO(CostAnnualBudgetDetailFullRepository.DirectCostItem item) {
        if (item == null) {
            return null;
        }

        CostAnnualBudgetDetailFullDTO.DirectCostItemDTO dto = new CostAnnualBudgetDetailFullDTO.DirectCostItemDTO();
        BeanUtils.copyProperties(item, dto);
        return dto;
    }

    /**
     * 本中心间接成本Domain对象转换为DTO
     *
     * @param item 本中心间接成本Domain对象
     * @return 本中心间接成本DTO
     */
    private static CostAnnualBudgetDetailFullDTO.CenterIndirectCostItemDTO toCenterIndirectCostItemDTO(CostAnnualBudgetDetailFullRepository.CenterIndirectCostItem item) {
        if (item == null) {
            return null;
        }

        CostAnnualBudgetDetailFullDTO.CenterIndirectCostItemDTO dto = new CostAnnualBudgetDetailFullDTO.CenterIndirectCostItemDTO();
        BeanUtils.copyProperties(item, dto);
        return dto;
    }

    /**
     * 综合间接成本Domain对象转换为DTO
     *
     * @param item 综合间接成本Domain对象
     * @return 综合间接成本DTO
     */
    private static CostAnnualBudgetDetailFullDTO.ComprehensiveIndirectCostItemDTO toComprehensiveIndirectCostItemDTO(CostAnnualBudgetDetailFullRepository.ComprehensiveIndirectCostItem item) {
        if (item == null) {
            return null;
        }

        CostAnnualBudgetDetailFullDTO.ComprehensiveIndirectCostItemDTO dto = new CostAnnualBudgetDetailFullDTO.ComprehensiveIndirectCostItemDTO();
        BeanUtils.copyProperties(item, dto);
        return dto;
    }

    /**
     * 非经营间接成本Domain对象转换为DTO
     *
     * @param item 非经营间接成本Domain对象
     * @return 非经营间接成本DTO
     */
    private static CostAnnualBudgetDetailFullDTO.NonOperatingIndirectCostItemDTO toNonOperatingIndirectCostItemDTO(CostAnnualBudgetDetailFullRepository.NonOperatingIndirectCostItem item) {
        if (item == null) {
            return null;
        }

        CostAnnualBudgetDetailFullDTO.NonOperatingIndirectCostItemDTO dto = new CostAnnualBudgetDetailFullDTO.NonOperatingIndirectCostItemDTO();
        BeanUtils.copyProperties(item, dto);
        return dto;
    }
}
