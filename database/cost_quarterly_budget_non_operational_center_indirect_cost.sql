-- 季度预算-预算科目明细非经营中心间接成本表数据库设计
-- 创建时间: 2025-08-12
-- 描述: 季度预算-预算科目明细非经营中心间接成本表，关联季度预算主表，包含非经营中心间接成本的预算科目、科目释义、支出预算金额等字段

-- 季度预算-预算科目明细非经营中心间接成本表
CREATE TABLE `cost_quarterly_budget_non_opt_center_indirect_cost`
(
    `id`                                    VARCHAR(36)  NOT NULL COMMENT 'UUID主键',

    -- 关联信息
    `quarterly_budget_id`                   VARCHAR(36)  NOT NULL COMMENT '关联季度预算主表ID',

    -- 预算科目信息
    `budget_subject_code`                   VARCHAR(50)  NOT NULL COMMENT '非经营中心间接成本-预算科目编码（关联预算科目表）',
    `budget_subject_name`                   VARCHAR(100) NOT NULL COMMENT '非经营中心间接成本-预算科目名称',
    `subject_description`                   VARCHAR(200) COMMENT '非经营中心间接成本-预算释义',

    -- 支出预算金额（元）
    `expenditure_budget_amount`             DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '非经营中心间接成本-支出预算金额（元）',

    -- 系统字段
    `create_time`                           DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`                             VARCHAR(50) COMMENT '创建人',
    `update_time`                           DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`                             VARCHAR(50) COMMENT '更新人',
    `tenant_id`                             INT          NOT NULL DEFAULT 0 COMMENT '租户ID',
    `del_flag`                              TINYINT      NOT NULL DEFAULT 0 COMMENT '删除标识 0:未删除 1:删除',
    `sys_org_code`                          VARCHAR(50) COMMENT '所属部门代码',

    PRIMARY KEY (`id`),
    KEY `idx_quarterly_budget_id` (`quarterly_budget_id`),
    KEY `idx_budget_subject_code` (`budget_subject_code`),
    KEY `idx_tenant_del` (`tenant_id`, `del_flag`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='季度预算-预算科目明细非经营中心间接成本表';
