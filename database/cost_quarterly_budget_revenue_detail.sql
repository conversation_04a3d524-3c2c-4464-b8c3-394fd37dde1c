-- 季度预算收入明细表数据库设计
-- 创建时间: 2025-08-12
-- 描述: 季度预算收入明细表，关联季度预算主表，包含产品信息、合同类型、数量、单价、年度应收预算等字段

-- 季度预算收入明细表
CREATE TABLE `cost_quarterly_budget_revenue_detail`
(
    `id`                           VARCHAR(36)  NOT NULL COMMENT 'UUID主键',
    
    -- 关联信息
    `quarterly_budget_id`          VARCHAR(36)  NOT NULL COMMENT '关联季度预算主表ID',
    
    -- 产品信息
    `product_name`                 VARCHAR(200) NOT NULL COMMENT '产品名称',
    `unit`                         VARCHAR(20)  NOT NULL COMMENT '单位',
    
    -- 合同信息
    `contract_type`                VARCHAR(20)  NOT NULL COMMENT '合同类型(lump_sum-总价合同/rate-费率合同)',
    
    -- 数量信息
    `product_quantity`             DECIMAL(15, 4) NOT NULL DEFAULT 0.0000 COMMENT '产品数量',
    `estimated_annual_process_volume` DECIMAL(15, 4) NOT NULL DEFAULT 0.0000 COMMENT '预计年处理量（水/油）',
    
    -- 价格信息
    `unit_price`                   DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '单价（元）',
    `annual_receivable_budget`     DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '年度应收预算（元）',
    
    -- 编制依据
    `compilation_basis`            TEXT COMMENT '编制依据',
    
    -- 备注信息
    `remark`                       TEXT COMMENT '备注',
    
    -- 系统字段
    `create_time`                  DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`                    VARCHAR(50) COMMENT '创建人',
    `update_time`                  DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`                    VARCHAR(50) COMMENT '更新人',
    `tenant_id`                    INT          NOT NULL DEFAULT 0 COMMENT '租户ID',
    `del_flag`                     TINYINT      NOT NULL DEFAULT 0 COMMENT '删除标识 0:未删除 1:删除',
    `sys_org_code`                 VARCHAR(50) COMMENT '所属部门代码',
    
    PRIMARY KEY (`id`),
    KEY `idx_quarterly_budget_id` (`quarterly_budget_id`),
    KEY `idx_product_name` (`product_name`),
    KEY `idx_contract_type` (`contract_type`),
    KEY `idx_tenant_del` (`tenant_id`, `del_flag`)
        
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='季度预算-收入明细表';

-- 添加表注释说明
ALTER TABLE `cost_quarterly_budget_revenue_detail` 
COMMENT = '季度预算-收入明细表';
