-- 季度预算数据库表设计
-- 创建时间: 2025-08-11

-- 季度预算主表
CREATE TABLE `cost_quarterly_budget`
(
    `id`                           VARCHAR(36)  NOT NULL COMMENT 'UUID主键',
    
    -- 基本信息
    `quarterly_budget_no`          VARCHAR(20)  NOT NULL COMMENT '季度预算单号(JDYS+8位日期+3位流水)',
    `quarterly_budget_code`        VARCHAR(20) COMMENT '季度预算编码(融合服务平台回传)',
    `quarterly_budget_name`        VARCHAR(100) NOT NULL COMMENT '季度预算名称',
    `version`                      VARCHAR(100)  NOT NULL COMMENT '版本',
    `budget_status`                VARCHAR(20)  NOT NULL DEFAULT 'PENDING_LOCK' COMMENT '状态(PENDING_LOCK-待锁定/APPROVING-审批中/LOCKED-已锁定/REJECTED-已驳回/CHANGED-已变更)',
    
    -- 关联信息
    `quarterly_plan_no`            VARCHAR(20) COMMENT '季度计划编号',
    `quarterly_plan_name`          VARCHAR(100) COMMENT '季度计划名称',
    `project_manager_name`         VARCHAR(50) COMMENT '项目经理',
    
    -- 时间信息
    `quarter`                      VARCHAR(20)  NOT NULL COMMENT '季度(如：2025年第一季度)',
    `start_date`                   DATE         NOT NULL COMMENT '开始时间',
    `end_date`                     DATE         NOT NULL COMMENT '结束时间',
    
    -- 是否涉及多年预算
    `is_multi_year_budget`         VARCHAR(10)  NOT NULL DEFAULT 'N' COMMENT '是否涉及多年预算(Y-是/N-否)',
    
    -- 关联年度预算信息
    `annual_budget_code`           VARCHAR(20) COMMENT '年度预算编码',
    `annual_budget_name`           VARCHAR(100) COMMENT '年度预算名称',
    `professional_company`         VARCHAR(100) COMMENT '所属单位',
    `center`                       VARCHAR(50) COMMENT '下属中心',
    `project_name`                 VARCHAR(100) COMMENT '项目名称',
    `budget_type`                  VARCHAR(20) COMMENT '预算类型',
    `annual_revenue_budget`        DECIMAL(15, 2) DEFAULT 0.00 COMMENT '年度收入预算金额（不含税，元）',
    `annual_expenditure_budget`    DECIMAL(15, 2) DEFAULT 0.00 COMMENT '年度支出预算金额（不含税，元）',
    
    -- 市场项目信息
    `market_project_name`          VARCHAR(100) COMMENT '市场项目名称',
    `customer_name`                VARCHAR(100) COMMENT '客户名称',
    `entrusting_department`        VARCHAR(100) COMMENT '委托部门',
    `contract_task_name`           VARCHAR(100) COMMENT '合同/任务名称',
    `contract_task_no`             VARCHAR(50) COMMENT '合同/任务编号',
    `contract_sign_date`           DATE COMMENT '合同签订/任务下发日期',
    `contract_deadline`            DATE COMMENT '合同/任务截止日期',
    `estimated_revenue_with_tax`   DECIMAL(15, 2) DEFAULT 0.00 COMMENT '预估收入金额（含税，元）',
    
    -- 预算金额统计（不含税）
    `revenue_budget_amount`        DECIMAL(15, 2) DEFAULT 0.00 COMMENT '收入预算金额（不含税，元）',
    `expenditure_budget_amount`    DECIMAL(15, 2) DEFAULT 0.00 COMMENT '支出预算金额（不含税，元）',
    `net_profit`                   DECIMAL(15, 2) DEFAULT 0.00 COMMENT '净利润（元）',
    `net_profit_rate`              DECIMAL(8, 4) DEFAULT 0.0000 COMMENT '净利润率（%）',

    -- 新增预算统计字段
    `annual_revenue_remaining_budget`    DECIMAL(15, 2) DEFAULT 0.00 COMMENT '年度收入剩余预算金额（不含税，元）',
    `project_revenue_budget_total`       DECIMAL(15, 2) DEFAULT 0.00 COMMENT '项目收入预算总额（元）',
    `annual_expend_remaining_budget` DECIMAL(15, 2) DEFAULT 0.00 COMMENT '年度支出剩余预算金额（不含税，元）',
    `project_expend_budget_total`   DECIMAL(15, 2) DEFAULT 0.00 COMMENT '项目支出预算总额（不含税，元）',
    `indirect_cost_budget_total`         DECIMAL(15, 2) DEFAULT 0.00 COMMENT '间接费预算总额（元）',
    `project_marginal_profit`            DECIMAL(15, 2) DEFAULT 0.00 COMMENT '项目边际利润（元）',
    `project_marginal_profit_rate`       DECIMAL(8, 4) DEFAULT 0.0000 COMMENT '项目边际利润率（%）',
    `project_net_profit`                 DECIMAL(15, 2) DEFAULT 0.00 COMMENT '项目净利润（元）',
    `project_net_profit_rate`            DECIMAL(8, 4) DEFAULT 0.0000 COMMENT '项目净利润率（%）',
    
    -- 工作流相关
    `wiid`                         VARCHAR(50) COMMENT '工作流实例ID',
    
    -- 备注信息
    `remark`                       TEXT COMMENT '备注',
    
    -- 系统字段
    `create_time`                  DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`                    VARCHAR(50) COMMENT '创建人',
    `update_time`                  DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`                    VARCHAR(50) COMMENT '更新人',
    `tenant_id`                    INT          NOT NULL DEFAULT 0 COMMENT '租户ID',
    `del_flag`                     TINYINT      NOT NULL DEFAULT 0 COMMENT '删除标识 0:未删除 1:删除',
    `sys_org_code`                 VARCHAR(50) COMMENT '所属部门代码',
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_quarterly_budget_no` (`quarterly_budget_no`),
    KEY `idx_quarterly_budget_code` (`quarterly_budget_code`),
    KEY `idx_budget_status` (`budget_status`),
    KEY `idx_professional_company` (`professional_company`),
    KEY `idx_center` (`center`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_del_flag` (`del_flag`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='季度预算主表';


-- 采办包明细表数据库设计
-- 创建时间: 2025-08-12
-- 描述: 采办包明细表，关联季度预算主表，包含直接成本、预算科目、金额等字段

-- 采办包明细表
CREATE TABLE `cost_quarterly_budget_proc_pkg_detail`
(
    `id`                     VARCHAR(36)  NOT NULL COMMENT 'UUID主键',

    -- 关联信息
    `quarterly_budget_id`    VARCHAR(36)  NOT NULL COMMENT '关联季度预算主表ID',

    -- 业务字段
    `direct_cost`            VARCHAR(200) NOT NULL COMMENT '直接成本（文本框，必填）',
    `budget_subject_code`    VARCHAR(50)  NOT NULL COMMENT '预算科目编码（关联预算科目表）',
    `budget_subject_name`    VARCHAR(100) NOT NULL COMMENT '预算科目名称',
    `amount`                 DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '金额（元，必填，支持小数）',

    -- 系统字段
    `create_time`            DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`              VARCHAR(50) COMMENT '创建人',
    `update_time`            DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`              VARCHAR(50) COMMENT '更新人',
    `tenant_id`              INT          NOT NULL DEFAULT 0 COMMENT '租户ID',
    `del_flag`               TINYINT      NOT NULL DEFAULT 0 COMMENT '删除标识 0:未删除 1:删除',
    `sys_org_code`           VARCHAR(50) COMMENT '所属部门代码',

    PRIMARY KEY (`id`),
    KEY `idx_quarterly_budget_id` (`quarterly_budget_id`),
    KEY `idx_budget_subject_code` (`budget_subject_code`),
    KEY `idx_tenant_del` (`tenant_id`, `del_flag`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='采办包明细表';

-- 添加表注释说明
ALTER TABLE `cost_quarterly_budget_proc_pkg_detail` COMMENT = '采办包明细表：用于记录季度预算下的采办包明细信息，包含直接成本、预算科目、金额等核心字段，支持与季度预算主表和预算科目表的关联查询';



CREATE TABLE `cost_quarterly_budget_material_detail`
(
    `id`                        varchar(36)    NOT NULL COMMENT 'UUID主键',
    -- 关联信息
    `quarterly_budget_id`    VARCHAR(36)  NOT NULL COMMENT '关联季度预算主表ID',
    `material_code`             varchar(50)    NOT NULL COMMENT '物料编码',
    `material_name`             varchar(100)   NOT NULL COMMENT '物料名称',
    `usage_amount`              decimal(15, 4) NOT NULL COMMENT '用量',
    `unit`                      varchar(20)    NOT NULL COMMENT '单位',
    `unit_price_excluding_tax`  decimal(15, 6) NOT NULL COMMENT '不含税单价(元)',
    `total_price_excluding_tax` decimal(15, 2) NOT NULL COMMENT '不含税总价(元)',
    `compilation_basis`            TEXT COMMENT '编制依据',
    `remark`                    text COMMENT '备注',
    `create_time`               datetime       NOT NULL COMMENT '创建时间',
    `create_by`                 varchar(50)             DEFAULT NULL COMMENT '创建人',
    `update_time`               datetime                DEFAULT NULL COMMENT '更新时间',
    `update_by`                 varchar(50)             DEFAULT NULL COMMENT '更新人',
    `tenant_id`                 int(10)        NOT NULL COMMENT '租户ID',
    `del_flag`                  int(3)         NOT NULL DEFAULT '0' COMMENT '删除标识 0:未删除 1:删除',
    `sys_org_code`              varchar(50)             DEFAULT NULL COMMENT '所属部门代码',
    PRIMARY KEY (`id`),
    KEY `idx_material_code` (`material_code`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_del_flag` (`del_flag`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='季度预算-原料明细表';


-- 预算科目明细直接成本表数据库设计
-- 创建时间: 2025-08-12
-- 描述: 预算科目明细直接成本表，关联季度预算主表，包含预算科目、年度预算金额、剩余预算、间接费预算等字段

-- 预算科目明细直接成本表
CREATE TABLE `cost_quarterly_budget_subject_direct_cost`
(
    `id`                                    VARCHAR(36)  NOT NULL COMMENT 'UUID主键',

    -- 关联信息
    `quarterly_budget_id`                   VARCHAR(36)  NOT NULL COMMENT '关联季度预算主表ID',

    -- 预算科目信息
    `budget_subject_code`                   VARCHAR(50)  NOT NULL COMMENT '直接成本-预算科目编码（关联预算科目表）',
    `budget_subject_name`                   VARCHAR(100) NOT NULL COMMENT '直接成本-预算科目名称',
    `subject_description`                   VARCHAR(200) COMMENT '直接成本-科目释义',

    -- 年度预算金额（不含税，元）
    `annual_expenditure_budget_amount`      DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '年度支出预算金额（不含税，元）',
    `annual_remain_expend_budget`   DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '年度剩余支出预算金额（不含税，元）',

    -- 间接费预算金额（不含税，元）
    `indirect_cost_reference_amount`        DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '间接费预算参考金额（不含税，元）',
    `indirect_cost_budget_amount`           DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '间接费预算金额（不含税，元）',

    -- 支出预算金额（不含税，元）
    `expenditure_budget_amount`             DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '支出预算金额（不含税，元）',

    -- 系统字段
    `create_time`                           DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`                             VARCHAR(50) COMMENT '创建人',
    `update_time`                           DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`                             VARCHAR(50) COMMENT '更新人',
    `tenant_id`                             INT          NOT NULL DEFAULT 0 COMMENT '租户ID',
    `del_flag`                              TINYINT      NOT NULL DEFAULT 0 COMMENT '删除标识 0:未删除 1:删除',
    `sys_org_code`                          VARCHAR(50) COMMENT '所属部门代码',

    PRIMARY KEY (`id`),
    KEY `idx_quarterly_budget_id` (`quarterly_budget_id`),
    KEY `idx_budget_subject_code` (`budget_subject_code`),
    KEY `idx_tenant_del` (`tenant_id`, `del_flag`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='预算科目明细直接成本表';

-- 添加表注释说明
ALTER TABLE `cost_quarterly_budget_subject_direct_cost` COMMENT = '季度预算-预算科目明细直接成本表';


