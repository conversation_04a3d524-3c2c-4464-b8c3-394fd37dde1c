-- 年度总预算数据库表设计
-- 作者: sunhzh
-- 创建时间: 2025-07-30

-- 1. 年度总预算主表
CREATE TABLE `cost_annual_budget`
(
    `id`                     VARCHAR(36)  NOT NULL COMMENT 'UUID主键',
    `budget_code`            VARCHAR(20)  NOT NULL COMMENT '总预算编号(ZYS+4位年份+3位流水)',
    `budget_name`            VARCHAR(100) NOT NULL COMMENT '总预算名称',
    `budget_year`            YEAR         NOT NULL COMMENT '年份',
    `version`                VARCHAR(10)  NOT NULL COMMENT '版本号',
    `budget_status`          VARCHAR(20)  NOT NULL DEFAULT 'PENDING_LOCK' COMMENT '状态(PENDING_LOCK-待锁定/APPROVING-审批中/LOCKED-已锁定/CHANGED-已变更)',
    `professional_company`   VARCHAR(100) NOT NULL COMMENT '所属单位',
    
    -- 收入信息（不含税）
    `revenue_total_amount`        DECIMAL(15, 2) DEFAULT 0.00 COMMENT '收入总金额(万元)',
    `revenue_used_amount`         DECIMAL(15, 2) DEFAULT 0.00 COMMENT '收入已使用金额(万元)',
    `revenue_remaining_amount`    DECIMAL(15, 2) DEFAULT 0.00 COMMENT '收入剩余金额(万元)',
    `revenue_difference_amount`   DECIMAL(15, 2) DEFAULT 0.00 COMMENT '收入总金额差异额(万元)',

    -- 直接成本信息（不含税）
    `direct_cost_total_amount`    DECIMAL(15, 2) DEFAULT 0.00 COMMENT '直接成本总金额(万元)',
    `direct_cost_used_amount`     DECIMAL(15, 2) DEFAULT 0.00 COMMENT '直接成本已使用金额(万元)',
    `direct_cost_remaining_amount` DECIMAL(15, 2) DEFAULT 0.00 COMMENT '直接成本剩余金额(万元)',
    `direct_cost_total_budget`    DECIMAL(15, 2) DEFAULT 0.00 COMMENT '直接成本总金额(万元)',
    
    -- 审批信息
    `submit_time`            DATETIME COMMENT '提交时间',
    `submit_by`              VARCHAR(50) COMMENT '提交人',
    `approve_time`           DATETIME COMMENT '审批时间',
    `approve_by`             VARCHAR(50) COMMENT '审批人',
    `approve_remark`         TEXT COMMENT '审批备注',
    
    -- 变更信息
    `parent_budget_id`       VARCHAR(36) COMMENT '父预算ID(变更时关联原预算)',
    `change_reason`          TEXT COMMENT '变更原因',

    -- 附件信息
    `attachment_url`         TEXT COMMENT '附件URL',

    -- 系统字段
    `create_time`            DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`              VARCHAR(50) COMMENT '创建人',
    `update_time`            DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`              VARCHAR(50) COMMENT '更新人',
    `tenant_id`              INT          NOT NULL DEFAULT 0 COMMENT '租户ID',
    `del_flag`               TINYINT      NOT NULL DEFAULT 0 COMMENT '删除标识 0:未删除 1:删除',
    `sys_org_code`           VARCHAR(50) COMMENT '所属部门代码',
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_budget_code` (`budget_code`),
    KEY `idx_budget_code` (`budget_code`),
    KEY `idx_budget_year` (`budget_year`),
    KEY `idx_budget_status` (`budget_status`),
    KEY `idx_professional_company` (`professional_company`),
    KEY `idx_parent_budget_id` (`parent_budget_id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_del_flag` (`del_flag`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='年度总预算主表';

-- 2. 年度总预算明细表
CREATE TABLE `cost_annual_budget_detail`
(
    `id`                     VARCHAR(36) NOT NULL COMMENT 'UUID主键',
    `budget_id`              VARCHAR(36) NOT NULL COMMENT '关联预算主表ID',
    `plan_id`                VARCHAR(36) NOT NULL COMMENT '关联项目年度计划ID',
    `project_code`           VARCHAR(50) COMMENT '项目编号',
    `project_name`           VARCHAR(100) NOT NULL COMMENT '年度预算项目名称',
    `professional_company`   VARCHAR(100) COMMENT '所属单位',
    `center`                 VARCHAR(50) COMMENT '下属中心',
    `budget_type`            VARCHAR(20) COMMENT '预算类型',
    `wbs_code`               VARCHAR(50) COMMENT 'WBS编号',
    `project_type`           VARCHAR(20) COMMENT '项目类型',
    `fourth_level_business`  VARCHAR(50) COMMENT '四级业务',
    `business_area`          VARCHAR(50) COMMENT '业务小类',
    `revenue_budget`         DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '收入预算金额(万元)',
    `direct_cost_budget`     DECIMAL(15, 2) DEFAULT 0.00 COMMENT '直接成本预算(万元)',
    `other_cost_budget`      DECIMAL(15, 2) DEFAULT 0.00 COMMENT '其他成本预算(万元)',
    `profit_budget`          DECIMAL(15, 2) DEFAULT 0.00 COMMENT '利润预算(万元)',
    
    -- 系统字段
    `create_time`            DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`              VARCHAR(50) COMMENT '创建人',
    `update_time`            DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`              VARCHAR(50) COMMENT '更新人',
    `tenant_id`              INT         NOT NULL DEFAULT 0 COMMENT '租户ID',
    `del_flag`               TINYINT     NOT NULL DEFAULT 0 COMMENT '删除标识 0:未删除 1:删除',
    `sys_org_code`           VARCHAR(50) COMMENT '所属部门代码',
    
    PRIMARY KEY (`id`),
    KEY `idx_budget_id` (`budget_id`),
    KEY `idx_plan_id` (`plan_id`),
    KEY `idx_project_code` (`project_code`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_del_flag` (`del_flag`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='年度总预算明细表';

-- 4. 年度总预算本中心间接成本表
CREATE TABLE `cost_annual_budget_center_indirect_cost`
(
    `id`                     VARCHAR(36) NOT NULL COMMENT 'UUID主键',
    `budget_id`              VARCHAR(36) NOT NULL COMMENT '关联预算主表ID',
    `sequence_no`            INT NOT NULL COMMENT '序号',
    `cost_item`              VARCHAR(50) NOT NULL COMMENT '费用科目',
    `cost_description`       VARCHAR(200) COMMENT '科目释义',
    `cost_amount`            DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '支出预算金额(万元)',

    -- 系统字段
    `create_time`            DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`              VARCHAR(50) COMMENT '创建人',
    `update_time`            DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`              VARCHAR(50) COMMENT '更新人',
    `tenant_id`              INT         NOT NULL DEFAULT 0 COMMENT '租户ID',
    `del_flag`               TINYINT     NOT NULL DEFAULT 0 COMMENT '删除标识 0:未删除 1:删除',
    `sys_org_code`           VARCHAR(50) COMMENT '所属部门代码',

    PRIMARY KEY (`id`),
    KEY `idx_budget_id` (`budget_id`),
    KEY `idx_cost_item` (`cost_item`),
    KEY `idx_sequence_no` (`sequence_no`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_del_flag` (`del_flag`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='年度总预算本中心间接成本表';

-- 5. 年度总预算非经营中心间接成本表
CREATE TABLE `cost_annual_budget_non_operating_indirect_cost`
(
    `id`                     VARCHAR(36) NOT NULL COMMENT 'UUID主键',
    `budget_id`              VARCHAR(36) NOT NULL COMMENT '关联预算主表ID',
    `sequence_no`            INT NOT NULL COMMENT '序号',
    `cost_item`              VARCHAR(50) NOT NULL COMMENT '费用科目',
    `cost_description`       VARCHAR(200) COMMENT '科目释义',
    `cost_amount`            DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '支出预算金额(万元)',

    -- 系统字段
    `create_time`            DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`              VARCHAR(50) COMMENT '创建人',
    `update_time`            DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`              VARCHAR(50) COMMENT '更新人',
    `tenant_id`              INT         NOT NULL DEFAULT 0 COMMENT '租户ID',
    `del_flag`               TINYINT     NOT NULL DEFAULT 0 COMMENT '删除标识 0:未删除 1:删除',
    `sys_org_code`           VARCHAR(50) COMMENT '所属部门代码',

    PRIMARY KEY (`id`),
    KEY `idx_budget_id` (`budget_id`),
    KEY `idx_cost_item` (`cost_item`),
    KEY `idx_sequence_no` (`sequence_no`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_del_flag` (`del_flag`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='年度总预算非经营中心间接成本表';

-- 6. 年度总预算综合管理间接成本表
CREATE TABLE `cost_annual_budget_comprehensive_indirect_cost`
(
    `id`                     VARCHAR(36) NOT NULL COMMENT 'UUID主键',
    `budget_id`              VARCHAR(36) NOT NULL COMMENT '关联预算主表ID',
    `sequence_no`            INT NOT NULL COMMENT '序号',
    `cost_item`              VARCHAR(50) NOT NULL COMMENT '费用科目',
    `cost_description`       VARCHAR(200) COMMENT '科目释义',
    `cost_amount`            DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '支出预算金额(万元)',

    -- 系统字段
    `create_time`            DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`              VARCHAR(50) COMMENT '创建人',
    `update_time`            DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`              VARCHAR(50) COMMENT '更新人',
    `tenant_id`              INT         NOT NULL DEFAULT 0 COMMENT '租户ID',
    `del_flag`               TINYINT     NOT NULL DEFAULT 0 COMMENT '删除标识 0:未删除 1:删除',
    `sys_org_code`           VARCHAR(50) COMMENT '所属部门代码',

    PRIMARY KEY (`id`),
    KEY `idx_budget_id` (`budget_id`),
    KEY `idx_cost_item` (`cost_item`),
    KEY `idx_sequence_no` (`sequence_no`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_del_flag` (`del_flag`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='年度总预算综合管理间接成本表';


