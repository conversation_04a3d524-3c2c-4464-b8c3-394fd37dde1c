-- 预算科目管理数据库表设计
-- 作者: sunhzh
-- 创建时间: 2025-07-31

-- 预算科目管理表
CREATE TABLE `cost_budget_subject`
(
    `id`                     VARCHAR(36)  NOT NULL COMMENT 'UUID主键',
    `subject_code`           VARCHAR(50)  NOT NULL COMMENT '预算科目编码',
    `subject_name`           VARCHAR(100) NOT NULL COMMENT '预算科目名称',
    `subject_description`    VARCHAR(200) COMMENT '科目释义',
    `subject_status`                 VARCHAR(20)  NOT NULL DEFAULT 'Y' COMMENT '状态(Y-启用/N-停用)',
    `sort_order`             INT          NOT NULL DEFAULT 0 COMMENT '排序号',
    `remark`                 VARCHAR(500) COMMENT '备注',
    
    -- 系统字段
    `create_time`            DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`              VARCHAR(50) COMMENT '创建人',
    `update_time`            DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`              VARCHAR(50) COMMENT '更新人',
    `tenant_id`              INT          NOT NULL DEFAULT 0 COMMENT '租户ID',
    `del_flag`               TINYINT      NOT NULL DEFAULT 0 COMMENT '删除标识 0:未删除 1:删除',
    `sys_org_code`           VARCHAR(50) COMMENT '所属部门代码',
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_subject_code` (`subject_code`),
    KEY `idx_subject_code` (`subject_code`),
    KEY `idx_subject_name` (`subject_name`),
    KEY `idx_status` (`subject_status`),
    KEY `idx_sort_order` (`sort_order`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_del_flag` (`del_flag`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='预算科目管理表';

