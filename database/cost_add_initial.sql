--  市场项目台账
-- 项目主表（合并合同信息）
CREATE TABLE `cost_project`
(
    `id`                     VARCHAR(36) COMMENT '主键ID',
    -- 项目信息
    `project_year`           YEAR         NOT NULL COMMENT '项目年度',
    `project_code`           VARCHAR(50)  NOT NULL COMMENT '项目编号',
    `project_name`           VARCHAR(255) NOT NULL COMMENT '项目名称',
    `project_status`         VARCHAR(20)  NOT NULL DEFAULT 'routine' COMMENT '项目状态：risk(风险), for_development(待开发), routine(常规)',
    `is_new_market`          BOOLEAN COMMENT '是否新市场',
    `is_new_business`        BOOLEAN COMMENT '是否新业务',
    `is_renewal_project`     BOOLEAN COMMENT '是否续签项目',
    `professional_company`   VARCHAR(100) COMMENT '专业公司',
    `project_stage`          VARCHAR(50)           DEFAULT 'pending_contract' COMMENT '项目阶段：pending_contract(待签合同), contract_signed(已签合同), in_execution(执行中), closed(已关闭)',
    `project_center`         VARCHAR(100) COMMENT '中心',
    `project_group`          VARCHAR(100) COMMENT '项目组',
    `project_leader`         VARCHAR(50) COMMENT '项目负责',
    -- 客户信息
    `customer_name`          VARCHAR(100) NOT NULL COMMENT '客户名称',
    `entrust_department`     VARCHAR(100) COMMENT '委托部门',
    `is_upstream_business`   BOOLEAN COMMENT '是否上游业务',
    `upstream_business_type` VARCHAR(50) COMMENT '上游业务类型',

    -- 合同信息（合并到项目表）
    `contract_name`          VARCHAR(255) COMMENT '合同名称',
    `contract_mode`          VARCHAR(50) COMMENT '合同模式',

    -- 项目盈利总况
    `estimated_revenue`      DECIMAL(15, 2) COMMENT '项目预计收入（元，税后）',
    `estimated_cost`         DECIMAL(15, 2) COMMENT '项目预计成本（元，税后）',
    `has_outsourcing`        BOOLEAN COMMENT '存在经营性外包',

    -- 产业信息
    `segmented_business`     VARCHAR(50)  NOT NULL COMMENT '细分业务',
    `first_level_business`   VARCHAR(50) COMMENT '一级业务',
    `second_level_business`  VARCHAR(50) COMMENT '二级业务',
    `third_level_business`   VARCHAR(50) COMMENT '三级业务',
    `business_area`          VARCHAR(50)  NOT NULL COMMENT '业务所属区域',

    -- 执行单位信息
    `exec_department`        VARCHAR(100) COMMENT '执行部门',
    `exec_unit`              VARCHAR(100) NOT NULL COMMENT '执行单位',

    -- 附件信息
    `project_attachment`     TEXT COMMENT '项目附件',

    -- 系统字段
    `create_time`            DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`              VARCHAR(50) COMMENT '创建人',
    `update_time`            DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`              VARCHAR(50) COMMENT '更新人',
    `tenant_id`              INT          NOT NULL DEFAULT 0 COMMENT '租户ID',
    `del_flag`               TINYINT      NOT NULL DEFAULT 0 COMMENT '删除标识 0:未删除 1:删除',
    `sys_org_code`           VARCHAR(50) COMMENT '所属部门代码',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_project_code` (`project_code`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_del_flag` (`del_flag`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='市场项目台账表';


-- 工作量动态表（记录月度工作量）
CREATE TABLE `cost_project_workload`
(
    `id`                 VARCHAR(36) COMMENT '主键ID',
    `project_id`         VARCHAR(36)         NOT NULL COMMENT '关联项目ID',
    `month`              TINYINT(2) UNSIGNED NOT NULL COMMENT '月份 (1-12)',
    `estimated_workload` DECIMAL(15, 2)      NOT NULL DEFAULT 0.00 COMMENT '预计工作量（元）',
    `actual_workload`    DECIMAL(15, 2)      NOT NULL DEFAULT 0.00 COMMENT '实际工作量（元）',
    `estimated_revenue`  DECIMAL(15, 2)      NOT NULL DEFAULT 0.00 COMMENT '当期预估收入（元）',

    -- 系统字段
    `create_time`        DATETIME            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`          VARCHAR(50) COMMENT '创建人',
    `update_time`        DATETIME            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`          VARCHAR(50) COMMENT '更新人',
    `tenant_id`          INT                 NOT NULL DEFAULT 0 COMMENT '租户ID',
    `del_flag`           TINYINT             NOT NULL DEFAULT 0 COMMENT '删除标识 0:未删除 1:删除',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_project_month` (`project_id`, `month`),
    KEY `idx_project_id` (`project_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='项目工作量月度表';

-- 材料单价管理
-- 材料单价表
CREATE TABLE `cost_material_price`
(
    `id`                 VARCHAR(36) COMMENT '主键ID',

    -- 业务字段 (根据图片内容)
    `material_name`      VARCHAR(100)                                                  NOT NULL COMMENT '物料名称',
    `material_code`      VARCHAR(50) COMMENT '物料编码',
    `status`             ENUM ('pending_submit','not_effective','in_effect','expired') NOT NULL DEFAULT 'pending_submit' COMMENT '状态: pending_submit-待提交, not_effective-未生效, in_effect-生效中, expired-已失效',
    `tax_unit_price`     DECIMAL(18, 2)                                                NOT NULL COMMENT '含税单价',
    `tax_rate`           DECIMAL(5, 2)                                                 NOT NULL DEFAULT 13.00 COMMENT '税率(%)',
    `non_tax_unit_price` DECIMAL(18, 2)                                                NOT NULL COMMENT '不含税单价',
    `price_type`         ENUM ('fixed','float') COMMENT '类型: fixed-固定, float-浮动',
    `up_rate`            DECIMAL(5, 2) COMMENT '上浮比例',
    `source`             VARCHAR(100) COMMENT '来源',
    `effective_date`     DATE                                                          NOT NULL COMMENT '生效日期',
    `expiration_date`    DATE                                                          NOT NULL COMMENT '失效日期',

    -- 系统字段 (所有表共用)
    `create_time`        DATETIME                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`          VARCHAR(50) COMMENT '创建人',
    `update_time`        DATETIME                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`          VARCHAR(50) COMMENT '更新人',
    `tenant_id`          INT                                                           NOT NULL DEFAULT 0 COMMENT '租户ID',
    `del_flag`           TINYINT(1)                                                    NOT NULL DEFAULT 0 COMMENT '删除标识 0:未删除 1:删除',

    PRIMARY KEY (`id`),
    INDEX `idx_material_code` (`material_code`),
    INDEX `idx_status` (`status`),
    INDEX `idx_effective_date` (`effective_date`),
    INDEX `idx_expiration_date` (`expiration_date`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='材料单价管理表';


-- 项目计划主表
CREATE TABLE `cost_project_plan`
(
    `id`                VARCHAR(36)  NOT NULL COMMENT 'UUID主键',
    `plan_code`         VARCHAR(15)  NOT NULL COMMENT '计划编号(JH+8位日期+3位流水)',
    `plan_name`         VARCHAR(100) NOT NULL COMMENT '计划名称',
    `project_plan_status`            VARCHAR(20)  NOT NULL DEFAULT 'PENDING_SUBMIT' COMMENT '状态(PENDING_SUBMIT/APPROVING/LOCKED)',
    `project_code`      VARCHAR(50) COMMENT '项目编号',
    `project_name`      VARCHAR(100) NOT NULL COMMENT '项目名称',
    `plan_type`         VARCHAR(20) COMMENT '计划类型(年度计划/季度计划 ANNUAL/QUARTERLY)',
    `center`            VARCHAR(50) COMMENT '中心',
    `project_group`     VARCHAR(50) COMMENT '项目组',
    `contract_mode`     VARCHAR(20) COMMENT '合同模式',
    `contract_revenue`  DECIMAL(15, 2) COMMENT '合同/预估收入(税后万元)',
    `direct_cost_total` DECIMAL(15, 2) COMMENT '直接成本小计',
    `other_cost_total`  DECIMAL(15, 2) COMMENT '其他成本小计',
    `tax_cost_total`    DECIMAL(15, 2) COMMENT '税金及附加小计',
    `cost_total`        DECIMAL(15, 2) COMMENT '成本总计',
    `project_profit`    DECIMAL(15, 2) COMMENT '项目利润(万元)',
    `profit_margin`     DECIMAL(5, 2) COMMENT '利润率(%)',

    -- 系统字段
    `create_time`       DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`         VARCHAR(50) COMMENT '创建人',
    `update_time`       DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`         VARCHAR(50) COMMENT '更新人',
    `tenant_id`         INT          NOT NULL DEFAULT 0 COMMENT '租户ID',
    `del_flag`          TINYINT      NOT NULL DEFAULT 0 COMMENT '删除标识 0:未删除 1:删除',
    `sys_org_code`      VARCHAR(50) COMMENT '所属部门代码',

    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_plan_code` (`plan_code`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='项目计划主表';

-- 计划明细表
CREATE TABLE `cost_project_plan_detail`
(
    `id`                     VARCHAR(36) NOT NULL COMMENT 'UUID主键',
    `plan_id`                VARCHAR(36) NOT NULL COMMENT '关联计划ID',
    `block`                  VARCHAR(50) COMMENT '区块',
    `platform_facility`      VARCHAR(100) COMMENT '平台设施',
    `product_model`          VARCHAR(50) NOT NULL COMMENT '产品型号',
    `density`                DECIMAL(6, 4) COMMENT '密度',
    `usage_amount`                  DECIMAL(15, 4) COMMENT '用量(吨)',
    `estimated_annual_oil`   DECIMAL(15, 4) COMMENT '预计年处理量(油，方)',
    `estimated_annual_water` DECIMAL(15, 4) COMMENT '预计年处理量(水，方)',
    `fee_rate`               DECIMAL(10, 4) COMMENT '收费费率(元/方)',
    `revenue_oil`            DECIMAL(15, 2) COMMENT '年度预算应收(油，万元)',
    `revenue_water`          DECIMAL(15, 2) COMMENT '年度预算应收(水，万元)',
    `demand_ton`             DECIMAL(15, 4) COMMENT '年度预算需求吨(吨)',

    -- 系统字段
    `create_time`            DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`              VARCHAR(50) COMMENT '创建人',
    `update_time`            DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`              VARCHAR(50) COMMENT '更新人',
    `tenant_id`              INT         NOT NULL DEFAULT 0 COMMENT '租户ID',
    `del_flag`               TINYINT     NOT NULL DEFAULT 0 COMMENT '删除标识 0:未删除 1:删除',
    `sys_org_code`           VARCHAR(50) COMMENT '所属部门代码',

    PRIMARY KEY (`id`),
    KEY `fk_cost_plan_detail` (`plan_id`),
    KEY `idx_tenant_id` (`tenant_id`),
    CONSTRAINT `fk_cost_plan_detail` FOREIGN KEY (`plan_id`)
        REFERENCES `cost_project_plan` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='计划明细表';

-- 直接成本明细表
CREATE TABLE `cost_direct_cost`
(
    `id`                       VARCHAR(36) NOT NULL COMMENT 'UUID主键',
    `plan_id`                  VARCHAR(36) NOT NULL COMMENT '关联计划ID',
    `product_name`             VARCHAR(50) NOT NULL COMMENT '产品名称',
    `estimated_usage`          DECIMAL(15, 4) COMMENT '预计用量(吨)',
    `formula_name`             VARCHAR(100) COMMENT '配方名称',
    `formula_code`             VARCHAR(50) COMMENT '配方编号',
    `unit_price_including_tax` DECIMAL(15, 6) COMMENT '材料成本含税单价(万元)',
    `unit_price_excluding_tax` DECIMAL(15, 6) COMMENT '材料成本不含税单价(万元)',
    `tax_rate`                 DECIMAL(5, 2) COMMENT '税率(%)',
    `total_including_tax`      DECIMAL(15, 2) COMMENT '材料成本含税总价(万元)',
    `total_excluding_tax`      DECIMAL(15, 2) COMMENT '材料成本不含税总价(万元)',

    -- 系统字段
    `create_time`              DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`                VARCHAR(50) COMMENT '创建人',
    `update_time`              DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`                VARCHAR(50) COMMENT '更新人',
    `tenant_id`                INT         NOT NULL DEFAULT 0 COMMENT '租户ID',
    `del_flag`                 TINYINT     NOT NULL DEFAULT 0 COMMENT '删除标识 0:未删除 1:删除',
    `sys_org_code`             VARCHAR(50) COMMENT '所属部门代码',

    PRIMARY KEY (`id`),
    KEY `fk_cost_direct_cost` (`plan_id`),
    KEY `idx_tenant_id` (`tenant_id`),
    CONSTRAINT `fk_cost_direct_cost` FOREIGN KEY (`plan_id`)
        REFERENCES `cost_project_plan` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='预算依据-直接成本明细';

-- 其他成本明细表
CREATE TABLE `cost_other_cost`
(
    `id`               VARCHAR(36)    NOT NULL COMMENT 'UUID主键',
    `plan_id`          VARCHAR(36)    NOT NULL COMMENT '关联计划ID',
    `cost_category`    VARCHAR(50)    NOT NULL COMMENT '成本类目',
    `cost_description` VARCHAR(200) COMMENT '成本说明',
    `fee_amount`       DECIMAL(15, 2) NOT NULL COMMENT '费用金额(万元)',

    -- 系统字段
    `create_time`      DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`        VARCHAR(50) COMMENT '创建人',
    `update_time`      DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`        VARCHAR(50) COMMENT '更新人',
    `tenant_id`        INT            NOT NULL DEFAULT 0 COMMENT '租户ID',
    `del_flag`         TINYINT        NOT NULL DEFAULT 0 COMMENT '删除标识 0:未删除 1:删除',
    `sys_org_code`     VARCHAR(50) COMMENT '所属部门代码',

    PRIMARY KEY (`id`),
    KEY `fk_cost_other_cost` (`plan_id`),
    KEY `idx_tenant_id` (`tenant_id`),
    CONSTRAINT `fk_cost_other_cost` FOREIGN KEY (`plan_id`)
        REFERENCES `cost_project_plan` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='预算依据-其他成本明细';

-- 税金及附加明细表
CREATE TABLE `cost_tax_cost`
(
    `id`               VARCHAR(36)    NOT NULL COMMENT 'UUID主键',
    `plan_id`          VARCHAR(36)    NOT NULL COMMENT '关联计划ID',
    `cost_category`    VARCHAR(50)    NOT NULL COMMENT '成本类目',
    `cost_description` VARCHAR(200) COMMENT '成本说明',
    `fee_amount`       DECIMAL(15, 2) NOT NULL COMMENT '费用金额(万元)',

    -- 系统字段
    `create_time`      DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`        VARCHAR(50) COMMENT '创建人',
    `update_time`      DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`        VARCHAR(50) COMMENT '更新人',
    `tenant_id`        INT            NOT NULL DEFAULT 0 COMMENT '租户ID',
    `del_flag`         TINYINT        NOT NULL DEFAULT 0 COMMENT '删除标识 0:未删除 1:删除',
    `sys_org_code`     VARCHAR(50) COMMENT '所属部门代码',

    PRIMARY KEY (`id`),
    KEY `fk_cost_tax_cost` (`plan_id`),
    KEY `idx_tenant_id` (`tenant_id`),
    CONSTRAINT `fk_cost_tax_cost` FOREIGN KEY (`plan_id`)
        REFERENCES `cost_project_plan` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='预算依据-税金及附加明细';

-- 原料明细表
CREATE TABLE `cost_material_detail`
(
    `id`                       VARCHAR(36) NOT NULL COMMENT 'UUID主键',
    `plan_id`                  VARCHAR(36) NOT NULL COMMENT '关联计划ID',
    `material_code`            VARCHAR(50) NOT NULL COMMENT '物料编码',
    `material_name`            VARCHAR(100) NOT NULL COMMENT '物料名称',
    `usage_amount`             DECIMAL(15, 4) NOT NULL DEFAULT 0.0000 COMMENT '用量',
    `unit`                     VARCHAR(20) NOT NULL COMMENT '单位',
    `tax_rate`                 DECIMAL(5, 2) NOT NULL DEFAULT 0.00 COMMENT '税率(%)',
    `unit_price_including_tax` DECIMAL(15, 6) NOT NULL DEFAULT 0.000000 COMMENT '含税单价(元)',
    `unit_price_excluding_tax` DECIMAL(15, 6) NOT NULL DEFAULT 0.000000 COMMENT '不含税单价(元)',
    `total_price_including_tax` DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '含税总价(元)',
    `total_price_excluding_tax` DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '不含税总价(元)',
    `remark`                   TEXT COMMENT '备注',

    -- 系统字段
    `create_time`              DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`                VARCHAR(50) COMMENT '创建人',
    `update_time`              DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`                VARCHAR(50) COMMENT '更新人',
    `tenant_id`                INT NOT NULL DEFAULT 0 COMMENT '租户ID',
    `del_flag`                 TINYINT NOT NULL DEFAULT 0 COMMENT '删除标识 0:未删除 1:删除',
    `sys_org_code`             VARCHAR(50) COMMENT '所属部门代码',

    PRIMARY KEY (`id`),
    KEY `idx_plan_id` (`plan_id`),
    KEY `idx_material_code` (`material_code`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_del_flag` (`del_flag`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='原料明细表';

-- 采购管理系统数据库脚本
-- 1. 采购申请主表
CREATE TABLE `coast_procurement_request`
(
    `id`                    VARCHAR(36) PRIMARY KEY COMMENT '主键ID',
    `request_code`          VARCHAR(50)              NOT NULL UNIQUE COMMENT '采购申请编号',
    `material_code`         VARCHAR(50)              NOT NULL COMMENT '物料编码',
    `material_name`         VARCHAR(100)             NOT NULL COMMENT '物料名称',
    `approval_status`       VARCHAR(20)    DEFAULT '待审批' COMMENT '审批状态：待提交、审批中、已通过',
    `purchase_quantity`     INT                      NOT NULL COMMENT '本次采购量',
    `unit`                  VARCHAR(20)              NOT NULL COMMENT '单位',
    `unit_price`            DECIMAL(10, 2)           NOT NULL COMMENT '单价',
    `total_amount_incl_tax` DECIMAL(12, 2)           NOT NULL COMMENT '含税总价',
    `total_amount_excl_tax` DECIMAL(12, 2)           NOT NULL COMMENT '不含税总价',
    `tax_amount`            DECIMAL(15, 2) DEFAULT 0.00 COMMENT '税额',
    `remark`                TEXT COMMENT '备注',
    `create_time`           datetime                 null comment '创建时间',
    `create_by`             varchar(36)              null comment '创建人',
    `update_time`           datetime                 null comment '更新时间',
    `update_by`             varchar(36)              null comment '更新人',
    `tenant_id`             int                      null comment '租户id',
    `del_flag`              int            default 0 null comment '删除标识 0:未删除 1:删除',
    `sys_org_code`          varchar(36)              null comment '所属部门',
    INDEX `idx_request_code` (`request_code`),
    INDEX `idx_material_code` (`material_code`),
    INDEX `idx_material_name` (`material_name`),
    INDEX `idx_create_time` (`create_time`),
    INDEX `idx_approval_status` (`approval_status`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='采购申请主表';


-- 2. 采购申请明细表
CREATE TABLE `cost_procurement_request_detail`
(
    `id`                VARCHAR(36) PRIMARY KEY COMMENT '主键ID',
    `request_id`        BIGINT         NOT NULL COMMENT '采购申请ID',
    `budget_code`       VARCHAR(50)    NOT NULL COMMENT '预算编码',
    `budget_name`       VARCHAR(200)   NOT NULL COMMENT '预算名称',
    `total_quantity`    DECIMAL(10, 2) NOT NULL COMMENT '总预算量',
    `unit_price`        DECIMAL(10, 2) COMMENT '审批中量',
    `total_price`       DECIMAL(10, 2) COMMENT '可采购量',
    `purchase_quantity` DECIMAL(10, 2) COMMENT '本次采购量',
    `remark`            VARCHAR(500) COMMENT '备注',
    `create_time`       datetime       null comment '创建时间',
    `create_by`         varchar(36)    null comment '创建人',
    `update_time`       datetime       null comment '更新时间',
    `update_by`         varchar(36)    null comment '更新人',
    `tenant_id`         int            null comment '租户id',
    `del_flag`          int default 0  null comment '删除标识 0:未删除 1:删除',
    `sys_org_code`      varchar(36)    null comment '所属部门',
    INDEX `idx_request_id` (`request_id`),
    INDEX `idx_budget_code` (`budget_code`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='采购申请明细表';


-- 3. 采购订单台账表
CREATE TABLE `cost_purchase_order_ledger`
(
    `id`                        VARCHAR(36) PRIMARY KEY COMMENT '主键ID',
    `sign_time`                 datetime       DEFAULT NULL COMMENT '签订时间',
    `planned_delivery_date`     date           DEFAULT NULL COMMENT '计划交货日期/验收日期',
    `order_type`                varchar(50)    DEFAULT NULL COMMENT '订单类型',
    `demand_number`             varchar(50)    DEFAULT NULL COMMENT '需求编号',
    `project_number`            varchar(50)    DEFAULT NULL COMMENT '立项号',
    `pr_number`                 varchar(50)    DEFAULT NULL COMMENT 'PR号',
    `order_name`                varchar(255)   DEFAULT NULL COMMENT '订单名称',
    `project_name`              varchar(255)   DEFAULT NULL COMMENT '立项名称',
    `material_name`             varchar(100)   DEFAULT NULL COMMENT '原材料/服务名称',
    `specification`             varchar(100)   DEFAULT NULL COMMENT '包装要求/规格',
    `material_code`             varchar(50)    DEFAULT NULL COMMENT '物资编码',
    `unit`                      varchar(20)    DEFAULT NULL COMMENT '单位',
    `quantity`                  decimal(12, 2) DEFAULT NULL COMMENT '数量',
    `budget_unit_price`         decimal(12, 2) DEFAULT NULL COMMENT '预算单价',
    `budget_total_price`        decimal(12, 2) DEFAULT NULL COMMENT '预算总价',
    `agreement_unit_price`      decimal(12, 2) DEFAULT NULL COMMENT '协议单价',
    `agreement_total_price`     decimal(12, 2) DEFAULT NULL COMMENT '协议总价',
    `order_unit_price`          decimal(12, 2) DEFAULT NULL COMMENT '订单单价',
    `order_excluding_tax_price` decimal(12, 2) DEFAULT NULL COMMENT '订单不含税单价',
    `order_amount`              decimal(12, 2) DEFAULT NULL COMMENT '订单金额',
    `matched_agreement_number`  varchar(50)    DEFAULT NULL COMMENT '匹配协议编号',
    `agreement_type`            varchar(50)    DEFAULT NULL COMMENT '协议类型',
    `order_number`              varchar(50)    DEFAULT NULL COMMENT '订单号',
    `sap_order_number`          varchar(50)    DEFAULT NULL COMMENT 'SAP订单号',
    `supplier`                  varchar(100)   DEFAULT NULL COMMENT '供应商',
    `execution_region`          varchar(50)    DEFAULT NULL COMMENT '执行区域',
    `application_unit`          varchar(100)   DEFAULT NULL COMMENT '申请单位',
    `create_time`               datetime                 null comment '创建时间',
    `create_by`                 varchar(36)              null comment '创建人',
    `update_time`               datetime                 null comment '更新时间',
    `update_by`                 varchar(36)              null comment '更新人',
    `tenant_id`                 int                      null comment '租户id',
    `del_flag`                  int            default 0 null comment '删除标识 0:未删除 1:删除',
    `sys_org_code`              varchar(36)              null comment '所属部门',
    KEY `idx_material_code` (`material_code`),
    KEY `idx_order_number` (`order_number`),
    KEY `idx_sap_order_number` (`sap_order_number`),
    KEY `idx_sign_time` (`sign_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='采购订单台账表';